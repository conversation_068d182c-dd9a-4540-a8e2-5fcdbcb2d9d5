import AdvancedChat from '@/components/advanced-chat';

export default function AdvancedChatPage() {
  return (
    <div className="container mx-auto p-4">
      <div className="max-w-6xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-center mb-2">
            Advanced AI Chat Interface
          </h1>
          <p className="text-muted-foreground text-center mb-4">
            Complete showcase of AI Elements components - Reasoning, Sources, Code Blocks, and more
          </p>
          
          <div className="bg-muted/50 rounded-lg p-4 mb-4">
            <h2 className="font-semibold mb-2">✨ Features Included:</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
              <div>• Conversation UI</div>
              <div>• Message Components</div>
              <div>• Reasoning Display</div>
              <div>• Source Citations</div>
              <div>• Code Blocks</div>
              <div>• Action Buttons</div>
              <div>• Model Selection</div>
              <div>• Loading States</div>
            </div>
          </div>
          
          <div className="text-center text-sm text-muted-foreground">
            Try asking: "Show me a Python function" or "Write TypeScript code" to see code blocks and reasoning
          </div>
        </div>
        
        <AdvancedChat />
      </div>
    </div>
  );
}
