"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash, <PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useDeleteItem } from "../api/use-delete-item";
import { useConfirm } from "@/hooks/use-comform";
import { useRouter } from "next/navigation";

interface ItemActionsProps {
  id: string;
  onEdit?: () => void;
}

export const ItemActions = ({ id, onEdit }: ItemActionsProps) => {
  const [ConfirmDialog, confirm] = useConfirm(
    "Are you sure?",
    "You are about to delete this item. This action cannot be undone."
  );

  const deleteMutation = useDeleteItem(id);
  const router = useRouter();

  const handleDelete = async () => {
    const ok = await confirm();

    if (ok) {
      deleteMutation.mutate();
    }
  };

  const handleViewDetails = () => {
    router.push(`/dashboard/items/${id}`);
  };

  return (
    <>
      <ConfirmDialog />
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="size-8 p-0">
            <MoreHorizontal className="size-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            disabled={deleteMutation.isPending}
            onClick={handleViewDetails}
          >
            <Eye className="size-4 mr-2" />
            View Details
          </DropdownMenuItem>
          <DropdownMenuItem
            disabled={deleteMutation.isPending}
            onClick={onEdit}
          >
            <Edit className="size-4 mr-2" />
            Edit
          </DropdownMenuItem>
          <DropdownMenuItem
            disabled={deleteMutation.isPending}
            onClick={handleDelete}
            className="text-destructive focus:text-destructive"
          >
            <Trash className="size-4 mr-2" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
};