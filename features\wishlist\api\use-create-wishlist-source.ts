import { useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";
import { InferRequestType, InferResponseType } from "hono";

type ResponseType = InferResponseType<typeof client.api.wishlist.possibilities[":id"]["sources"]["$post"]>;
type RequestType = InferRequestType<typeof client.api.wishlist.possibilities[":id"]["sources"]["$post"]>["json"];

export const useCreateWishlistSource = () => {
  const userId = useCurrentUserId();
  const queryClient = useQueryClient();

  const mutation = useMutation<ResponseType, Error, { possibilityId: string; source: RequestType }>({
    mutationFn: async ({ possibilityId, source }) => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const response = await client.api.wishlist.possibilities[":id"]["sources"].$post(
        { param: { id: possibilityId }, json: source },
        {
          headers: {
            "X-User-ID": userId,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to create source");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["wishlist-items", userId],
      });
      queryClient.invalidateQueries({
        queryKey: ["wishlist-item", userId],
      });
    },
  });

  return mutation;
};