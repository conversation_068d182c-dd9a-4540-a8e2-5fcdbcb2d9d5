import { ChatAgent } from "./agent-langgraph";

// Test the LangGraph pattern implementation
export async function testLangGraphPattern() {
  console.log("🧪 Testing LangGraph Pattern Implementation...");
  
  const agent = new ChatAgent();
  
  try {
    // Test 1: Simple tool call
    console.log("\n📋 Test 1: Simple tool call - fetch accounts");
    const result1 = await agent.invoke({
      messages: [],
      input: "Show me all my accounts",
      personaId: "test-user-123",
      media: []
    });
    
    console.log("✅ Test 1 Messages:", result1.messages.length);
    console.log("✅ Test 1 Final Response:", result1.messages[result1.messages.length - 1].content);
    
    // Check for tool calls in the conversation
    const toolCallMessages = result1.messages.filter(msg => msg.tool_calls && msg.tool_calls.length > 0);
    if (toolCallMessages.length > 0) {
      console.log("🔧 Tools called:", toolCallMessages.map(msg => 
        msg.tool_calls?.map(call => call.name)
      ).flat());
    }
    
    // Test 2: Tool call with parameters
    console.log("\n📊 Test 2: Tool call with parameters - create account");
    const result2 = await agent.invoke({
      messages: [],
      input: "Create a new account called 'Test Savings'",
      personaId: "test-user-123",
      media: []
    });
    
    console.log("✅ Test 2 Messages:", result2.messages.length);
    console.log("✅ Test 2 Final Response:", result2.messages[result2.messages.length - 1].content);
    
    const toolCallMessages2 = result2.messages.filter(msg => msg.tool_calls && msg.tool_calls.length > 0);
    if (toolCallMessages2.length > 0) {
      console.log("🔧 Tools called:", toolCallMessages2.map(msg => 
        msg.tool_calls?.map(call => call.name)
      ).flat());
    }
    
    console.log("\n🎉 LangGraph pattern test completed successfully!");
    return true;
    
  } catch (error) {
    console.error("❌ LangGraph pattern test failed:", error);
    return false;
  }
}

// Test the workflow structure
export async function testWorkflowStructure() {
  console.log("🔧 Testing Workflow Structure...");
  
  const agent = new ChatAgent();
  
  try {
    const result = await agent.invoke({
      messages: [],
      input: "What's today's date?",
      personaId: "test-user-123",
      media: []
    });
    
    console.log("📊 Workflow Results:");
    console.log("- Total messages:", result.messages.length);
    console.log("- Message types:", result.messages.map(msg => msg.constructor.name));
    
    // Check workflow execution
    const hasSystemMessage = result.messages.some(msg => msg.constructor.name === 'SystemMessage');
    const hasHumanMessage = result.messages.some(msg => msg.constructor.name === 'HumanMessage');
    const hasAIMessage = result.messages.some(msg => msg.constructor.name === 'AIMessage');
    const hasToolMessage = result.messages.some(msg => msg.constructor.name === 'ToolMessage');
    
    console.log("✅ Workflow components:");
    console.log("- System message:", hasSystemMessage ? "✅" : "❌");
    console.log("- Human message:", hasHumanMessage ? "✅" : "❌");
    console.log("- AI message:", hasAIMessage ? "✅" : "❌");
    console.log("- Tool message:", hasToolMessage ? "✅" : "❌");
    
    return true;
    
  } catch (error) {
    console.error("❌ Workflow structure test failed:", error);
    return false;
  }
}

// Main test runner
export async function runLangGraphTests() {
  console.log("🚀 Starting LangGraph Pattern Tests\n");
  
  const patternTest = await testLangGraphPattern();
  const workflowTest = await testWorkflowStructure();
  
  if (patternTest && workflowTest) {
    console.log("\n🎉 All LangGraph pattern tests passed!");
    console.log("✅ Tools are properly connected using the LangGraph pattern");
    console.log("✅ Workflow structure is correct");
    console.log("✅ Agent can execute financial tools automatically");
  } else {
    console.log("\n❌ Some tests failed. Please check the implementation.");
  }
}

// Export for easy testing
if (require.main === module) {
  runLangGraphTests().catch(console.error);
}