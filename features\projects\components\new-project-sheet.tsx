import { z } from "zod";

import { ProjectForm } from "./project-form";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>itle,
} from "@/components/ui/sheet";
import { useNewProject } from "../hooks/use-new-project";
import { useCreateProject } from "../api/use-create-project";
import { Button } from "@/components/ui/button";
import { useCreateCategory } from "@/features/categories/api/use-create-categories";

const formSchema = z.object({
  name: z.string(),
  description: z.string().nullable().optional(),
  budget: z.string(),
  startDate: z.coerce.date(),
  endDate: z.coerce.date(),
});

type FormValues = z.input<typeof formSchema>;

export const NewProjectSheet = () => {
  const { isOpen, onClose } = useNewProject();
  const createMutation = useCreateProject();

  const onSubmit = (values: any) => {
    createMutation.mutate(values, {
      onSuccess: () => {
        onClose();
      },
    });
  };

  const isPending = createMutation.isPending;

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="overflow-y-auto">
        <SheetHeader>
          <SheetTitle>New Project</SheetTitle>
          <SheetDescription>Create a new Project</SheetDescription>
        </SheetHeader>
        <ProjectForm onSubmit={onSubmit} disabled={isPending} />
        <SheetFooter>
          <Button variant={"destructive"} onClick={onClose}>
            Cancel
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};
