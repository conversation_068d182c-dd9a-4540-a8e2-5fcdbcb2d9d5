import { useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";
import { InferRequestType, InferResponseType } from "hono";

type ResponseType = InferResponseType<typeof client.api.wishlist.possibilities[":id"]["$patch"]>;
type RequestType = InferRequestType<typeof client.api.wishlist.possibilities[":id"]["$patch"]>["json"];

export const useEditWishlistPossibility = () => {
  const userId = useCurrentUserId();
  const queryClient = useQueryClient();

  const mutation = useMutation<ResponseType, Error, { id: string; possibility: RequestType; files?: FileList }>({
    mutationFn: async ({ id, possibility, files }) => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      // If files are provided, use the multipart form endpoint
      if (files && files.length > 0) {
        const formData = new FormData();
        formData.append("data", JSON.stringify(possibility));
        
        // Append files with specific keys
        for (let i = 0; i < files.length; i++) {
          formData.append(`file-${i}`, files[i]);
        }

        const response = await fetch(`/api/wishlist/possibilities/${id}/with-files`, {
          method: "PATCH",
          body: formData,
          headers: {
            "X-User-ID": userId,
          },
        });

        if (!response.ok) {
          throw new Error("Failed to update possibility with files");
        }

        return response.json();
      } else {
        // Use the regular JSON endpoint
        const response = await client.api.wishlist.possibilities[":id"].$patch(
          { param: { id }, json: possibility },
          {
            headers: {
              "X-User-ID": userId,
            },
          }
        );

        if (!response.ok) {
          throw new Error("Failed to update possibility");
        }

        return response.json();
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["wishlist-items", userId],
      });
      queryClient.invalidateQueries({
        queryKey: ["wishlist-item", userId],
      });
    },
  });

  return mutation;
};