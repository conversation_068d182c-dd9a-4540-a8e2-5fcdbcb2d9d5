import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { useState } from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { FileInput } from "@/components/file-input";
import { MediaGallery } from "@/components/media-gallery";
import { Image } from "lucide-react";

const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  notes: z.string().optional(),
});

type FormValues = z.input<typeof formSchema>;

type Props = {
  id?: string;
  defaultValues?: FormValues;
  onSubmit: (values: FormValues, files?: FileList) => void;
  onDelete?: () => void;
  disabled?: boolean;
};

export const WishlistPossibilityForm = ({
  id,
  defaultValues,
  onSubmit,
  onDelete,
  disabled,
}: Props) => {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: defaultValues || {
      name: "",
      description: "",
      notes: "",
    },
  });

  const handleSubmit = (values: FormValues) => {
    // Convert File[] to FileList
    const fileList = uploadedFiles.length > 0 ? (() => {
      const dt = new DataTransfer();
      uploadedFiles.forEach(file => dt.items.add(file));
      return dt.files;
    })() : undefined;
    
    onSubmit(values, fileList);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Possibility Name</FormLabel>
              <FormControl>
                <Input
                  disabled={disabled}
                  placeholder="iPhone 15 Pro Max"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description (Optional)</FormLabel>
              <FormControl>
                <Textarea
                  disabled={disabled}
                  placeholder="Detailed description of this possibility..."
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes (Optional)</FormLabel>
              <FormControl>
                <Textarea
                  disabled={disabled}
                  placeholder="Additional notes about this possibility..."
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Separator className="my-4" />
        <div className="space-y-4">
          <FormLabel className="flex items-center gap-2">
            <Image className="h-4 w-4" />
            Possibility Images
          </FormLabel>
          
          {id ? (
            // Edit mode: Show existing files and allow new uploads
            <div className="space-y-4">
              <MediaGallery
                entityType="wishlist_possibility"
                entityId={id}
                category="images"
                allowDelete={true}
                showViewToggle={false}
                compact={false}
              />
              <div className="border-t pt-4">
                <FileInput
                  onFilesSelected={setUploadedFiles}
                  maxFiles={10}
                  acceptedTypes={['image/jpeg', 'image/png', 'image/webp', 'image/gif']}
                  allowCamera={true}
                  title="Add More Images"
                  description="Select additional images to upload with this possibility"
                />
              </div>
            </div>
          ) : (
            // Create mode: Allow file selection for upload after creation
            <FileInput
              onFilesSelected={setUploadedFiles}
              maxFiles={10}
              acceptedTypes={['image/jpeg', 'image/png', 'image/webp', 'image/gif']}
              allowCamera={true}
              title="Select Possibility Images"
              description="Choose images to upload with this possibility (will be uploaded after creating)"
            />
          )}
        </div>

        <div className="flex gap-2">
          <Button type="submit" disabled={disabled} className="flex-1">
            {id ? "Update" : "Create"} Possibility
          </Button>
          {!!id && (
            <Button
              type="button"
              disabled={disabled}
              onClick={onDelete}
              variant="destructive"
            >
              Delete
            </Button>
          )}
        </div>
      </form>
    </Form>
  );
};