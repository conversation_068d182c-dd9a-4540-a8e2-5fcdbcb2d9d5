import { useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";
import { InferRequestType, InferResponseType } from "hono";

type ResponseType = InferResponseType<typeof client.api.wishlist.sources[":id"]["$patch"]>;
type RequestType = InferRequestType<typeof client.api.wishlist.sources[":id"]["$patch"]>["json"];

export const useEditWishlistSource = () => {
  const userId = useCurrentUserId();
  const queryClient = useQueryClient();

  const mutation = useMutation<ResponseType, Error, { id: string; source: RequestType }>({
    mutationFn: async ({ id, source }) => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const response = await client.api.wishlist.sources[":id"].$patch(
        { param: { id }, json: source },
        {
          headers: {
            "X-User-ID": userId,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update source");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["wishlist-items", userId],
      });
      queryClient.invalidateQueries({
        queryKey: ["wishlist-item", userId],
      });
    },
  });

  return mutation;
};