import { z } from "zod";
import { Trash, Image, FileText } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { DatePicker } from "@/components/date-picker";
import { Select } from "@/components/select";
import { Textarea } from "@/components/ui/textarea";
import { AmountInput } from "@/components/account-input";
import { Separator } from "@/components/ui/separator";
import { useState, useEffect } from "react";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { insertTransactionSchema } from "@/db/schema";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { convertAmountToMiliunits } from "@/lib/utils";
import { FileInput } from "@/components/file-input";
import { MediaGallery } from "@/components/media-gallery";

const formSchema = z.object({
  date: z.coerce.date(),
  accountId: z.string(),
  categoryId: z.string().nullable().optional(),
  projectId: z.string().nullable().optional(),
  payee: z.string(),
  amount: z.string(),
  note: z.string().nullable().optional(),
});
const apiSchema = insertTransactionSchema.omit({
  id: true,
});

type FormValues = z.input<typeof formSchema>;
type ApiValues = z.input<typeof apiSchema> & { files?: FileList };

type Props = {
  id?: string;
  defaultValues?: FormValues;
  onSubmit: (values: ApiValues, files?: FileList) => void;
  onDelete?: () => void;
  disabled?: boolean;
  accountOptions: { label: string; value: string }[];
  categoryOptions: { label: string; value: string }[];
  projectOptions: { label: string; value: string }[];
  onCreateAccount: (name: string) => void;
  onCreateCategory: (name: string) => void;
};

export const TransactionForm = ({
  id,
  defaultValues,
  onSubmit,
  onDelete,
  disabled,
  accountOptions,
  categoryOptions,
  projectOptions,
  onCreateAccount,
  onCreateCategory,
}: Props) => {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: defaultValues,
  });

  const handleSubmit = (values: FormValues) => {
    const amount = parseFloat(values.amount);
    const amountInMiliunits = convertAmountToMiliunits(amount);
    
    // Convert File[] to FileList
    const fileList = uploadedFiles.length > 0 ? (() => {
      const dt = new DataTransfer();
      uploadedFiles.forEach(file => dt.items.add(file));
      return dt.files;
    })() : undefined;
    
    onSubmit({
      ...values,
      amount: amountInMiliunits,
    }, fileList);
  };

  const handleDelete = () => {
    onDelete?.();
  };

  const handleFilesUploaded = (files: any[]) => {
    // Files are automatically uploaded to the server, just refresh the gallery
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className="space-y-4 pt-4"
      >
        <FormField
          name="date"
          control={form.control}
          render={({ field }) => {
            return (
              <FormItem>
                <FormControl className="z-50">
                  <DatePicker
                    value={field.value}
                    onChange={field.onChange}
                    disabled={disabled}
                  />
                </FormControl>
              </FormItem>
            );
          }}
        />
        <FormField
          name="accountId"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Select
                  placeholder="Select an account"
                  options={accountOptions}
                  onCreate={onCreateAccount}
                  value={field.value}
                  onChange={field.onChange}
                  disabled={disabled}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          name="categoryId"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Category</FormLabel>
              <FormControl>
                <Select
                  placeholder="Select a category"
                  options={categoryOptions}
                  onCreate={onCreateCategory}
                  value={field.value}
                  onChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          name="projectId"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Project</FormLabel>
              <FormControl>
                <Select
                  placeholder="Select a project"
                  options={projectOptions}
                  value={field.value}
                  onChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          name="payee"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Payee</FormLabel>
              <FormControl>
                <Input
                  disabled={disabled}
                  placeholder="Add a payee"
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          name="amount"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Amount</FormLabel>
              <FormControl>
                <AmountInput
                  {...field}
                  disabled={disabled}
                  placeholder="0.00"
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          name="note"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Optional notes"
                  disabled={disabled}
                  {...field}
                  value={field.value ?? ""}
                />
              </FormControl>
            </FormItem>
          )}
        />
        
        <Separator className="my-4" />
        <div className="space-y-4">
          <FormLabel className="flex items-center gap-2">
            <Image className="h-4 w-4" />
            Transaction Media
          </FormLabel>
          
          {id ? (
            // Edit mode: Show existing files and allow new uploads
            <div className="space-y-4">
              <MediaGallery
                entityType="transaction"
                entityId={id}
                category="receipts"
                allowDelete={true}
                showViewToggle={false}
                compact={false}
              />
              <div className="border-t pt-4">
                <FileInput
                  onFilesSelected={setUploadedFiles}
                  maxFiles={10}
                  acceptedTypes={['image/jpeg', 'image/png', 'image/webp', 'application/pdf']}
                  allowCamera={true}
                  title="Add More Files"
                  description="Select additional receipt images and documents to upload with this transaction"
                />
              </div>
            </div>
          ) : (
            // Create mode: Allow file selection for upload after creation
            <FileInput
              onFilesSelected={setUploadedFiles}
              maxFiles={10}
              acceptedTypes={['image/jpeg', 'image/png', 'image/webp', 'application/pdf']}
              allowCamera={true}
              title="Select Receipt Images"
              description="Choose receipt images and documents to upload with this transaction"
            />
          )}
        </div>
        
        <Button className="w-full" disabled={disabled}>
          {id ? "Save changes" : "Create transaction"}
        </Button>
        {!!id && (
          <Button
            type="button"
            disabled={disabled}
            onClick={handleDelete}
            className="w-full"
            variant="outline"
          >
            <Trash className="size-4 mr-2" />
            Delete
          </Button>
        )}
      </form>
    </Form>
  );
};
