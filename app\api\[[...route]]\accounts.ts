import { Hono } from "hono";
import { db } from "@/db/drizzle";
import { accounts } from "@/db/schema";
import { createId } from "@paralleldrive/cuid2";
import { zValidator } from "@hono/zod-validator";
import { and, eq, inArray } from "drizzle-orm";
import { insertAccountSchema } from "@/db/schema";
import { z } from "zod";
import { getServerUserId } from "@/lib/utils";

const app = new Hono()
  .get("/", async (c) => {
    try {
      const userId = await getServerUserId(c.req.raw.headers);

      const data = await db
        .select({
          id: accounts.id,
          name: accounts.name,
        })
        .from(accounts)
        .where(eq(accounts.userId, userId));
      return c.json({ data });
    } catch (error) {
      return c.json({ error: "Unauthorized" }, 401);
    }
  })
  .get("/:id", zValidator("param", z.object({ id: z.string() })), async (c) => {
    try {
      const userId = await getServerUserId(c.req.raw.headers);
      const { id } = c.req.valid("param");

      const [data] = await db
        .select({
          id: accounts.id,
          name: accounts.name,
        })
        .from(accounts)
        .where(and(eq(accounts.userId, userId), eq(accounts.id, id)));

      if (!data) {
        return c.json(
          {
            error: "Not found",
          },
          404,
        );
      }

      return c.json({ data });
    } catch (error) {
      return c.json({ error: "Unauthorized" }, 401);
    }
  })
  .post(
    "/",
    zValidator(
      "json",
      insertAccountSchema.pick({
        name: true,
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { name } = c.req.valid("json");

        const [data] = await db
          .insert(accounts)
          .values({
            id: createId(),
            userId: userId,
            name: name,
          })
          .returning();

        return c.json({ data: { id: data.id, name: data.name } });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .post(
    "/bulk-delete",
    zValidator(
      "json",
      z.object({
        ids: z.array(z.string()),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const values = c.req.valid("json");

        const data = await db
          .delete(accounts)
          .where(
            and(eq(accounts.userId, userId), inArray(accounts.id, values.ids)),
          )
          .returning({
            id: accounts.id,
          });

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .patch(
    "/:id",
    zValidator(
      "param",
      z.object({
        id: z.string().optional(),
      }),
    ),
    zValidator(
      "json",
      insertAccountSchema.pick({
        name: true,
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");
        const values = c.req.valid("json");

        if (!id) {
          return c.json({ error: "Id not found" }, 400);
        }

        const [data] = await db
          .update(accounts)
          .set(values)
          .where(and(eq(accounts.userId, userId), eq(accounts.id, id)))
          .returning();

        if (!data) {
          return c.json({ error: "Not found" }, 404);
        }

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .delete(
    "/:id",
    zValidator(
      "param",
      z.object({
        id: z.string().optional(),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");

        if (!id) {
          return c.json(
            {
              error: "Missing id",
            },
            400,
          );
        }

        const [data] = await db
          .delete(accounts)
          .where(and(eq(accounts.userId, userId), eq(accounts.id, id)))
          .returning();

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  );

export default app;
