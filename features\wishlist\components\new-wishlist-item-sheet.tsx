import { z } from "zod";

import { <PERSON>listForm } from "./wishlist-form";
import {
  <PERSON>et,
  <PERSON>et<PERSON>ontent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { useNewWishlistItem } from "../hooks/use-new-wishlist-item";
import { useCreateWishlistItem } from "../api/use-create-wishlist-item";
import { useCreateWishlistWithFiles } from "../api/use-create-wishlist-with-files";

const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  category: z.string().min(1, "Category is required"),
  estimatedCost: z.number(),
  targetAmount: z.number().optional(),
  targetDate: z.coerce.date().optional(),
  priority: z.string().default("medium"),
  status: z.string().default("planned"),
  notes: z.string().optional(),
  links: z.string().optional(),
  imageUrl: z.string().optional(),
  motivation: z.string().optional(),
});

type FormValues = z.input<typeof formSchema>;

export const NewWishlistItemSheet = () => {
  const { isOpen, onClose } = useNewWishlistItem();
  const createMutation = useCreateWishlistItem();
  const createWithFilesMutation = useCreateWishlistWithFiles();

  const onSubmit = (values: FormValues, files?: FileList) => {
    if (files && files.length > 0) {
      createWithFilesMutation.mutate({ data: values, files }, {
        onSuccess: () => {
          onClose();
        },
      });
    } else {
      createMutation.mutate(values, {
        onSuccess: () => {
          onClose();
        },
      });
    }
  };

  const isPending = createMutation.isPending || createWithFilesMutation.isPending;

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="overflow-y-auto">
        <SheetHeader>
          <SheetTitle>Add New Wish</SheetTitle>
          <SheetDescription>
            Add something you want to buy to your wishlist
          </SheetDescription>
        </SheetHeader>
        <WishlistForm onSubmit={onSubmit} disabled={isPending} />
      </SheetContent>
    </Sheet>
  );
};