"use client";

import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Upload, Image as ImageIcon, FileText } from "lucide-react";
import { MediaUpload } from "@/components/media-upload";
import { MediaGallery } from "@/components/media-gallery";

interface MediaUploadWithPreviewProps {
  category?: "receipts" | "documents" | "images" | "general";
  entityType?: string;
  entityId?: string;
  title?: string;
  description?: string;
  maxFiles?: number;
  acceptedTypes?: string[];
  allowCamera?: boolean;
  onFilesUploaded?: (files: any[]) => void;
  onError?: (error: string) => void;
}

export function MediaUploadWithPreview({
  category = "general",
  entityType,
  entityId,
  title = "Media Management",
  description = "Upload and manage your files",
  maxFiles = 10,
  acceptedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'application/pdf', 'text/plain'],
  allowCamera = true,
  onFilesUploaded,
  onError,
}: MediaUploadWithPreviewProps) {
  const [activeTab, setActiveTab] = useState("upload");

  const getCategoryInfo = (cat: string) => {
    const info = {
      receipts: { 
        icon: FileText, 
        color: "bg-green-100 text-green-800 border-green-200",
        description: "Receipt images and documents"
      },
      documents: { 
        icon: FileText, 
        color: "bg-blue-100 text-blue-800 border-blue-200",
        description: "Important documents and files"
      },
      images: { 
        icon: ImageIcon, 
        color: "bg-purple-100 text-purple-800 border-purple-200",
        description: "Photos and images"
      },
      general: { 
        icon: Upload, 
        color: "bg-gray-100 text-gray-800 border-gray-200",
        description: "General files and documents"
      },
    };
    return info[cat as keyof typeof info] || info.general;
  };

  const categoryInfo = getCategoryInfo(category);
  const IconComponent = categoryInfo.icon;

  const handleFilesUploaded = (files: any[]) => {
    // Switch to gallery tab after upload
    setActiveTab("gallery");
    onFilesUploaded?.(files);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <IconComponent className="h-5 w-5" />
          {title}
          <Badge variant="outline" className={`ml-auto ${categoryInfo.color}`}>
            {category}
          </Badge>
        </CardTitle>
        <CardDescription>
          {description} • {categoryInfo.description}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upload" className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              Upload
            </TabsTrigger>
            <TabsTrigger value="gallery" className="flex items-center gap-2">
              <ImageIcon className="h-4 w-4" />
              Gallery
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="upload" className="mt-4">
            <MediaUpload
              category={category}
              entityType={entityType}
              entityId={entityId}
              maxFiles={maxFiles}
              acceptedTypes={acceptedTypes}
              allowCamera={allowCamera}
              title=""
              description=""
              onFilesUploaded={handleFilesUploaded}
              onError={onError}
            />
          </TabsContent>
          
          <TabsContent value="gallery" className="mt-4">
            <MediaGallery
              category={category}
              entityType={entityType}
              entityId={entityId}
              allowDelete={true}
              showViewToggle={true}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}