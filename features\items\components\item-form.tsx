"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { TagSelector } from "@/features/tags/components/tag-selector";
import { useGetCategories } from "@/features/categories/api/use-get-categories";
import { insertItemSchema } from "@/db/schema";

const formSchema = insertItemSchema.omit({
  id: true,
  userId: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  tagIds: z.array(z.string()).optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface ItemFormProps {
  id?: string;
  defaultValues?: Partial<FormValues>;
  onSubmit: (values: FormValues) => void;
  onCancel?: () => void;
  disabled?: boolean;
}

interface Tag {
  id: string;
  name: string;
  color?: string | null;
}

export const ItemForm = ({
  id,
  defaultValues,
  onSubmit,
  onCancel,
  disabled,
}: ItemFormProps) => {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      payee: "",
      defaultCategoryId: "none",
      barcode: "",
      tagIds: [],
      ...defaultValues,
      // Convert empty string to "none" for the form
      defaultCategoryId: defaultValues?.defaultCategoryId === "" || !defaultValues?.defaultCategoryId ? "none" : defaultValues.defaultCategoryId,
    },
  });

  const categoriesQuery = useGetCategories();
  const categories = categoriesQuery.data || [];

  const handleSubmit = (values: FormValues) => {
    // Convert "none" back to empty string for the API
    const processedValues = {
      ...values,
      defaultCategoryId: values.defaultCategoryId === "none" ? "" : values.defaultCategoryId,
    };
    onSubmit(processedValues);
  };

  // Convert tag IDs to tag objects for TagSelector
  const selectedTags: Tag[] = defaultValues?.tags?.map(tag => ({
    id: tag.id,
    name: tag.name,
    color: tag.color,
  })) || [];

  const handleTagsChange = (tags: Tag[]) => {
    form.setValue("tagIds", tags.map(tag => tag.id));
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Item Name</FormLabel>
              <FormControl>
                <Input
                  disabled={disabled}
                  placeholder="Enter item name"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  disabled={disabled}
                  placeholder="Enter item description (optional)"
                  {...field}
                  value={field.value || ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="payee"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Payee/Vendor</FormLabel>
              <FormControl>
                <Input
                  disabled={disabled}
                  placeholder="Enter payee or vendor (optional)"
                  {...field}
                  value={field.value || ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="defaultCategoryId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Default Category</FormLabel>
              <Select
                disabled={disabled}
                onValueChange={field.onChange}
                value={field.value || "none"}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category (optional)" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="none">No category</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}\n                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="barcode"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Barcode</FormLabel>
              <FormControl>
                <Input
                  disabled={disabled}
                  placeholder="Enter barcode (optional)"
                  {...field}
                  value={field.value || ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Tags */}
        <div className="space-y-2">
          <TagSelector
            selectedTags={selectedTags}
            onTagsChange={handleTagsChange}
            placeholder="Add tags to organize this item..."
          />
        </div>

        <div className="flex flex-col gap-2 pt-2">
          <Button type="submit" disabled={disabled} className="w-full">
            {id ? "Update" : "Create"} Item
          </Button>
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={disabled}
              className="w-full"
            >
              Cancel
            </Button>
          )}
        </div>
      </form>
    </Form>
  );
};