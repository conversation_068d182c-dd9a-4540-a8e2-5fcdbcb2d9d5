CREATE TABLE IF NOT EXISTS "item_transactions" (
	"id" text PRIMARY KEY NOT NULL,
	"item_id" text NOT NULL,
	"transaction_id" text NOT NULL,
	"quantity" real,
	"unit_price" integer,
	"amount" integer NOT NULL,
	"category_id" text,
	"project_id" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "items" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"default_category_id" text,
	"barcode" text,
	"user_id" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "item_transactions" ADD CONSTRAINT "item_transactions_item_id_items_id_fk" FOREIGN KEY ("item_id") REFERENCES "items"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "item_transactions" ADD CONSTRAINT "item_transactions_transaction_id_transactions_id_fk" FOREIGN KEY ("transaction_id") REFERENCES "transactions"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "item_transactions" ADD CONSTRAINT "item_transactions_category_id_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "categories"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "item_transactions" ADD CONSTRAINT "item_transactions_project_id_project_id_fk" FOREIGN KEY ("project_id") REFERENCES "project"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "items" ADD CONSTRAINT "items_default_category_id_categories_id_fk" FOREIGN KEY ("default_category_id") REFERENCES "categories"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
-- Data migration: Extract unique items from existing detailsTransactions
-- Only migrate items that have names (not null/empty)
INSERT INTO "items" ("id", "name", "description", "default_category_id", "user_id", "created_at", "updated_at")
SELECT 
    md5(random()::text || clock_timestamp()::text) as "id",
    TRIM(unique_items."name") as "name",
    NULL as "description",
    NULL as "default_category_id", -- We'll let users set this later
    a.user_id as "user_id",
    NOW() as "created_at",
    NOW() as "updated_at"
FROM (
    SELECT DISTINCT 
        TRIM(dt."name") as "name",
        t.account_id
    FROM "detailsTransaction" dt
    INNER JOIN "transactions" t ON dt.transaction_id = t.id
    WHERE dt."name" IS NOT NULL 
        AND TRIM(dt."name") != ''
        AND TRIM(dt."name") != 'undefined'
) unique_items
INNER JOIN "accounts" a ON unique_items.account_id = a.id
ON CONFLICT DO NOTHING;
--> statement-breakpoint
-- Data migration: Migrate existing detailsTransactions to itemTransactions
-- Link with items by name (case-insensitive)
INSERT INTO "item_transactions" (
    "id", 
    "item_id", 
    "transaction_id", 
    "quantity", 
    "unit_price", 
    "amount", 
    "category_id", 
    "project_id", 
    "created_at", 
    "updated_at"
)
SELECT 
    dt."id",
    i."id" as "item_id",
    dt."transaction_id",
    dt."quantity",
    dt."unit_price",
    dt."amount",
    dt."category_id",
    dt."project_id",
    NOW() as "created_at",
    NOW() as "updated_at"
FROM "detailsTransaction" dt
INNER JOIN "transactions" t ON dt.transaction_id = t.id
INNER JOIN "accounts" a ON t.account_id = a.id
LEFT JOIN "items" i ON TRIM(LOWER(i."name")) = TRIM(LOWER(dt."name")) 
    AND i.user_id = a.user_id
WHERE dt."name" IS NOT NULL 
    AND TRIM(dt."name") != ''
    AND TRIM(dt."name") != 'undefined'
    AND i."id" IS NOT NULL
ON CONFLICT ("id") DO NOTHING;
