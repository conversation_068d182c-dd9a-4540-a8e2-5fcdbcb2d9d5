import { InferRequestType, InferResponseType } from "hono";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";

type ResponseType = InferResponseType<typeof client.api.wishlist["bulk-delete"]["$post"]>;
type RequestType = InferRequestType<typeof client.api.wishlist["bulk-delete"]["$post"]>["json"];

export const useBulkDeleteWishlistItems = () => {
  const userId = useCurrentUserId();
  const queryClient = useQueryClient();
  
  const mutation = useMutation<ResponseType, Error, RequestType>({
    mutationFn: async (json) => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const response = await client.api.wishlist["bulk-delete"].$post(
        { json },
        {
          headers: {
            "X-User-ID": userId,
          },
        },
      );
      const data = await response.json();

      return data;
    },
    onSuccess: () => {
      toast.success("Wishlist items deleted");
      queryClient.invalidateQueries({ queryKey: ["wishlist-items", userId] });
    },
    onError: () => {
      toast.error("Failed to delete wishlist items");
    },
  });

  return mutation;
};