[{"categories": [{"name": "Rent", "goal": 1100}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 150}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 120}, {"name": "Education", "goal": 150}, {"name": "Entertainment", "goal": 80}, {"name": "Healthcare", "goal": 100}, {"name": "Savings", "goal": 1200}], "accounts": [{"name": "Deutsche Bank Hauptkonto"}], "projects": [{"name": "Children's Education Fund", "budget": 10000, "startDate": "2024-01-01", "endDate": "2030-12-31", "description": "Savings for children's higher education in Germany."}], "transactions": [{"amount": -1100, "detailsTransactions": [{"name": "Monthly Rent - Sunrise Apartments", "quantity": 1, "unitPrice": 1100, "amount": -1100, "categoryId": "Rent", "projectId": null}], "payee": "Sunrise Apartments Verwaltung", "notes": "Monthly rent payment for October", "date": "2024-10-01", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Rent"}, {"amount": -120, "detailsTransactions": [{"name": "Monthly Electricity Bill", "quantity": 1, "unitPrice": 70, "amount": -70, "categoryId": "Utilities", "projectId": null}, {"name": "Water Supply Charges", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Utilities", "projectId": null}], "payee": "Strom & Wasser GmbH", "notes": "Monthly utility bill", "date": "2024-10-02", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Utilities"}, {"amount": -75, "detailsTransactions": [{"name": "Organic Vegetables - GrünHof", "quantity": 8, "unitPrice": 3, "amount": -24, "categoryId": "Groceries", "projectId": null}, {"name": "Farm-raised Chicken - Bauerlust", "quantity": 2, "unitPrice": 15, "amount": -30, "categoryId": "Groceries", "projectId": null}, {"name": "Basmati Rice 1kg - Reiskönig", "quantity": 1, "unitPrice": 8, "amount": -8, "categoryId": "Groceries", "projectId": null}, {"name": "Bio Milk 1L - Milchland", "quantity": 3, "unitPrice": 4, "amount": -12, "categoryId": "Groceries", "projectId": null}, {"name": "Free-range Eggs - EiSpezial", "quantity": 1, "unitPrice": 6, "amount": -6, "categoryId": "Groceries", "projectId": null}], "payee": "Lidl Supermarkt", "notes": "Weekly grocery shopping", "date": "2024-10-03", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}, {"amount": -50, "detailsTransactions": [{"name": "Premium Unleaded Fuel", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Transportation", "projectId": null}], "payee": "Shell Tankstelle", "notes": "Fuel for the car", "date": "2024-10-04", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -30, "detailsTransactions": [{"name": "Dinner Combo - Landgut", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Dining Out", "projectId": null}], "payee": "Das Gute Restaurant", "notes": "Family dinner out", "date": "2024-10-05", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Dining Out"}, {"amount": -60, "detailsTransactions": [{"name": "Deluxe Chess Set", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Entertainment", "projectId": null}, {"name": "Classic Board Game", "quantity": 1, "unitPrice": 40, "amount": -40, "categoryId": "Entertainment", "projectId": null}], "payee": "Freizeit Spielwaren GmbH", "notes": "Family game night supplies", "date": "2024-10-06", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Entertainment"}, {"amount": -20, "detailsTransactions": [{"name": "Children's Education Fund Deposit", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Savings", "projectId": "Children's Education Fund"}], "payee": "Bildungsschatzkonto", "notes": "Contribution towards children's education fund", "date": "2024-10-07", "projectId": "Children's Education Fund", "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Savings"}]}, {"categories": [{"name": "Rent", "goal": 1100}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 150}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 120}, {"name": "Education", "goal": 150}, {"name": "Entertainment", "goal": 80}, {"name": "Healthcare", "goal": 100}, {"name": "Savings", "goal": 1200}], "accounts": [{"name": "Deutsche Bank Hauptkonto"}], "projects": [{"name": "Children's Education Fund", "budget": 10000, "startDate": "2024-01-01", "endDate": "2030-12-31", "description": "Savings for children's higher education in Germany."}], "transactions": [{"amount": -80, "detailsTransactions": [{"name": "Organic Vegetables Bundle", "quantity": 10, "unitPrice": 3, "amount": -30, "categoryId": "Groceries", "projectId": null}, {"name": "Grass-fed Beef", "quantity": 2, "unitPrice": 20, "amount": -40, "categoryId": "Groceries", "projectId": null}, {"name": "Sourdough Artisan Bread", "quantity": 1, "unitPrice": 10, "amount": -10, "categoryId": "Groceries", "projectId": null}], "payee": "Aldi Bio-Markt", "notes": "Weekly grocery shopping", "date": "2024-10-08", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}, {"amount": -30, "detailsTransactions": [{"name": "Taxi Ride to Airport - Limousine Service", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Transportation", "projectId": null}], "payee": "<PERSON><PERSON> König", "notes": "Ride to the airport", "date": "2024-10-09", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -100, "detailsTransactions": [{"name": "Complete Medical Examination", "quantity": 1, "unitPrice": 100, "amount": -100, "categoryId": "Healthcare", "projectId": null}], "payee": "Städtisches Krankenhaus", "notes": "Routine health check-up", "date": "2024-10-10", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Healthcare"}, {"amount": -40, "detailsTransactions": [{"name": "Advanced Mathematics Textbook", "quantity": 1, "unitPrice": 40, "amount": -40, "categoryId": "Education", "projectId": null}], "payee": "Bücherladen am Markt", "notes": "Purchase of educational materials", "date": "2024-10-11", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Education"}, {"amount": -50, "detailsTransactions": [{"name": "Romantic Italian Dinner", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Dining Out", "projectId": null}], "payee": "Trattoria Bella Notte", "notes": "Dinner with spouse", "date": "2024-10-12", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Dining Out"}, {"amount": -20, "detailsTransactions": [{"name": "Blockbuster Movie Experience", "quantity": 2, "unitPrice": 10, "amount": -20, "categoryId": "Entertainment", "projectId": null}], "payee": "Cinemaxx", "notes": "Weekend movie outing", "date": "2024-10-13", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Entertainment"}, {"amount": -30, "detailsTransactions": [{"name": "Digital Learning Hub Subscription", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Education", "projectId": null}], "payee": "Online Lernzentrum", "notes": "Monthly subscription for online courses", "date": "2024-10-14", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Education"}]}, {"categories": [{"name": "Rent", "goal": 1100}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 150}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 120}, {"name": "Education", "goal": 150}, {"name": "Entertainment", "goal": 80}, {"name": "Healthcare", "goal": 100}, {"name": "Savings", "goal": 1200}], "accounts": [{"name": "Deutsche Bank Hauptkonto"}], "projects": [{"name": "Children's Education Fund", "budget": 10000, "startDate": "2024-01-01", "endDate": "2030-12-31", "description": "Savings for children's higher education in Germany."}], "transactions": [{"amount": -70, "detailsTransactions": [{"name": "Seasonal Fruits Assortment", "quantity": 7, "unitPrice": 3, "amount": -21, "categoryId": "Groceries", "projectId": null}, {"name": "Free-range Chicken", "quantity": 3, "unitPrice": 10, "amount": -30, "categoryId": "Groceries", "projectId": null}, {"name": "Organic Eggs", "quantity": 2, "unitPrice": 9, "amount": -18, "categoryId": "Groceries", "projectId": null}], "payee": "Kaufland Bio-Marktplatz", "notes": "Weekly grocery shopping", "date": "2024-10-15", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}, {"amount": -10, "detailsTransactions": [{"name": "Weekly Bus Travel Pass", "quantity": 1, "unitPrice": 10, "amount": -10, "categoryId": "Transportation", "projectId": null}], "payee": "ÖPNV-Verkehrsbetriebe", "notes": "Weekly bus pass", "date": "2024-10-16", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -50, "detailsTransactions": [{"name": "Teeth Cleaning Service", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Healthcare", "projectId": null}], "payee": "Zahnarztpraxi<PERSON> Smiley", "notes": "Dental cleaning appointment", "date": "2024-10-17", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Healthcare"}, {"amount": -1000, "detailsTransactions": [{"name": "Education Fund Savings Contribution", "quantity": 1, "unitPrice": 1000, "amount": -1000, "categoryId": "Savings", "projectId": "Children's Education Fund"}], "payee": "Bildungsschatzkonto", "notes": "Contribution to children's education fund", "date": "2024-10-18", "projectId": "Children's Education Fund", "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Savings"}, {"amount": -25, "detailsTransactions": [{"name": "Business Lunch", "quantity": 1, "unitPrice": 15, "amount": -15, "categoryId": "Dining Out", "projectId": null}, {"name": "Espresso Shot", "quantity": 1, "unitPrice": 10, "amount": -10, "categoryId": "Dining Out", "projectId": null}], "payee": "Café an der Ecke", "notes": "Lunch and coffee at work", "date": "2024-10-19", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Dining Out"}, {"amount": -15, "detailsTransactions": [{"name": "Premium Streaming Service", "quantity": 1, "unitPrice": 15, "amount": -15, "categoryId": "Entertainment", "projectId": null}], "payee": "Netflix Deutschland", "notes": "Monthly subscription fee", "date": "2024-10-20", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Entertainment"}, {"amount": -60, "detailsTransactions": [{"name": "Organic Produce Assortment", "quantity": 1, "unitPrice": 60, "amount": -60, "categoryId": "Groceries", "projectId": null}], "payee": "Lokaler Bauernmarkt", "notes": "Organic vegetables and fruits for the weekend", "date": "2024-10-21", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}]}, {"categories": [{"name": "Rent", "goal": 1100}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 150}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 120}, {"name": "Education", "goal": 150}, {"name": "Entertainment", "goal": 80}, {"name": "Healthcare", "goal": 100}, {"name": "Savings", "goal": 1200}], "accounts": [{"name": "Deutsche Bank Hauptkonto"}], "projects": [{"name": "Children's Education Fund", "budget": 10000, "startDate": "2024-01-01", "endDate": "2030-12-31", "description": "Savings for children's higher education in Germany."}], "transactions": [{"amount": -60, "detailsTransactions": [{"name": "Assorted Fresh Fruits", "quantity": 6, "unitPrice": 3, "amount": -18, "categoryId": "Groceries", "projectId": null}, {"name": "Grass-fed Beef Slices", "quantity": 2, "unitPrice": 15, "amount": -30, "categoryId": "Groceries", "projectId": null}, {"name": "Almond Milk 1L", "quantity": 3, "unitPrice": 4, "amount": -12, "categoryId": "Groceries", "projectId": null}], "payee": "<PERSON><PERSON>", "notes": "Weekly grocery shopping", "date": "2024-10-22", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}, {"amount": -20, "detailsTransactions": [{"name": "Weekly Taxi Expenses", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Transportation", "projectId": null}], "payee": "City Taxi Service", "notes": "Taxi rides for the week", "date": "2024-10-23", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -50, "detailsTransactions": [{"name": "Gourmet Dining Experience", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Dining Out", "projectId": null}], "payee": "Local Gourmet Bistro", "notes": "Dinner with friends", "date": "2024-10-24", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Dining Out"}, {"amount": -30, "detailsTransactions": [{"name": "Children's Literature Set", "quantity": 2, "unitPrice": 15, "amount": -30, "categoryId": "Education", "projectId": null}], "payee": "Buchladen am Platz", "notes": "Books for children", "date": "2024-10-25", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Education"}, {"amount": -25, "detailsTransactions": [{"name": "Board Game Collection Rental", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Entertainment", "projectId": null}], "payee": "Spieleparadi<PERSON>", "notes": "Weekend game rental", "date": "2024-10-26", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Entertainment"}, {"amount": -12, "detailsTransactions": [{"name": "Morning Brew Coffee", "quantity": 1, "unitPrice": 5, "amount": -5, "categoryId": "Dining Out", "projectId": null}, {"name": "Chocolate Croissant", "quantity": 1, "unitPrice": 7, "amount": -7, "categoryId": "Dining Out", "projectId": null}], "payee": "Café Central", "notes": "Coffee and pastry before work", "date": "2024-10-27", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Dining Out"}, {"amount": -100, "detailsTransactions": [{"name": "Over-the-counter Medications", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Healthcare", "projectId": null}, {"name": "Health Supplies Package", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Healthcare", "projectId": null}], "payee": "Apotheke GesundLeben", "notes": "Purchase of over-the-counter medications and health supplies", "date": "2024-10-28", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Healthcare"}]}, {"categories": [{"name": "Rent", "goal": 1100}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 150}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 120}, {"name": "Education", "goal": 150}, {"name": "Entertainment", "goal": 80}, {"name": "Healthcare", "goal": 100}, {"name": "Savings", "goal": 1200}], "accounts": [{"name": "Deutsche Bank Hauptkonto"}], "projects": [{"name": "Children's Education Fund", "budget": 10000, "startDate": "2024-01-01", "endDate": "2030-12-31", "description": "Savings for children's higher education in Germany."}], "transactions": [{"amount": -55, "detailsTransactions": [{"name": "Farm Fresh Vegetables & Seasonal Fruits", "quantity": 10, "unitPrice": 3.5, "amount": -35, "categoryId": "Groceries", "projectId": null}, {"name": "Gourmet Cheese & Artisan Bread", "quantity": 2, "unitPrice": 10, "amount": -20, "categoryId": "Groceries", "projectId": null}], "payee": "Edeka BioMarkt", "notes": "Weekly grocery shopping", "date": "2024-10-29", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}, {"amount": -15, "detailsTransactions": [{"name": "Monthly Urban Bus Pass", "quantity": 1, "unitPrice": 15, "amount": -15, "categoryId": "Transportation", "projectId": null}], "payee": "Stadtverkehrsbetriebe", "notes": "Renewal of bus pass", "date": "2024-10-30", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -1100, "detailsTransactions": [{"name": "November Rent Payment", "quantity": 1, "unitPrice": 1100, "amount": -1100, "categoryId": "Rent", "projectId": null}], "payee": "Sunrise Apartments Verwaltung", "notes": "Monthly rent payment for November", "date": "2024-11-01", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Rent"}, {"amount": -80, "detailsTransactions": [{"name": "Monthly Electricity Charges", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Utilities", "projectId": null}, {"name": "Monthly Water Service Fees", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Utilities", "projectId": null}], "payee": "Dienstleister für Versorgung", "notes": "Monthly utility bills", "date": "2024-11-02", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Utilities"}, {"amount": -25, "detailsTransactions": [{"name": "Netflix Subscription Renewal", "quantity": 1, "unitPrice": 15, "amount": -15, "categoryId": "Entertainment", "projectId": null}, {"name": "Premium Gaming App Subscription", "quantity": 1, "unitPrice": 10, "amount": -10, "categoryId": "Entertainment", "projectId": null}], "payee": "Digitale Dienstleistungen AG", "notes": "Monthly digital subscriptions", "date": "2024-11-03", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Entertainment"}, {"amount": -45, "detailsTransactions": [{"name": "Colleague Dinner at Gourmet Bistro", "quantity": 1, "unitPrice": 45, "amount": -45, "categoryId": "Dining Out", "projectId": null}], "payee": "Feinkost <PERSON>", "notes": "Dinner with colleagues", "date": "2024-11-04", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Dining Out"}]}, {"categories": [{"name": "Rent", "goal": 1100}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 150}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 120}, {"name": "Education", "goal": 150}, {"name": "Entertainment", "goal": 80}, {"name": "Healthcare", "goal": 100}, {"name": "Savings", "goal": 1200}, {"name": "Health & Wellness", "goal": 100}], "accounts": [{"name": "Deutsche Bank Hauptkonto"}], "projects": [{"name": "Children's Education Fund", "budget": 10000, "startDate": "2024-01-01", "endDate": "2030-12-31", "description": "Savings for children's higher education in Germany."}], "transactions": [{"amount": -75, "detailsTransactions": [{"name": "Locally-Sourced Organic Produce", "quantity": 10, "unitPrice": 4, "amount": -40, "categoryId": "Groceries", "projectId": null}, {"name": "Free-Range Chicken Thighs", "quantity": 2, "unitPrice": 15, "amount": -30, "categoryId": "Groceries", "projectId": null}, {"name": "Greek Yogurt", "quantity": 1, "unitPrice": 5, "amount": -5, "categoryId": "Groceries", "projectId": null}], "payee": "Lokaler Bauernmarkt", "notes": "Weekly grocery shopping", "date": "2024-11-05", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}, {"amount": -25, "detailsTransactions": [{"name": "RideShare Commute Costs", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Transportation", "projectId": null}], "payee": "<PERSON><PERSON><PERSON>enst", "notes": "Commute to client meetings", "date": "2024-11-06", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -20, "detailsTransactions": [{"name": "Team Lunch Special", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Dining Out", "projectId": null}], "payee": "<PERSON><PERSON><PERSON> Diner", "notes": "Team bonding lunch", "date": "2024-11-07", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Dining Out"}, {"amount": -60, "detailsTransactions": [{"name": "Annual Museum Pass Renewal", "quantity": 1, "unitPrice": 60, "amount": -60, "categoryId": "Education", "projectId": null}], "payee": "Stadtmuseum", "notes": "Annual membership renewal", "date": "2024-11-08", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Education"}, {"amount": -55, "detailsTransactions": [{"name": "Fit Life Gym Membership", "quantity": 1, "unitPrice": 55, "amount": -55, "categoryId": "Health & Wellness", "projectId": null}], "payee": "Fitness Leben Studio", "notes": "Monthly gym membership", "date": "2024-11-09", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Health & Wellness"}, {"amount": -30, "detailsTransactions": [{"name": "Interactive Toy Set for Birthday", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Entertainment", "projectId": null}], "payee": "Spielzeugladen", "notes": "Purchase of birthday gift", "date": "2024-11-10", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Entertainment"}, {"amount": -10, "detailsTransactions": [{"name": "Local Charity Donation", "quantity": 1, "unitPrice": 10, "amount": -10, "categoryId": "Savings", "projectId": null}], "payee": "Lokale Wohltätigkeitsorganisation", "notes": "Monthly charity donation", "date": "2024-11-11", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Savings"}]}, {"categories": [{"name": "Rent", "goal": 1100}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 150}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 120}, {"name": "Education", "goal": 150}, {"name": "Entertainment", "goal": 80}, {"name": "Healthcare", "goal": 100}, {"name": "Savings", "goal": 1200}], "accounts": [{"name": "Deutsche Bank Hauptkonto"}], "projects": [{"name": "Children's Education Fund", "budget": 10000, "startDate": "2024-01-01", "endDate": "2030-12-31", "description": "Savings for children's higher education in Germany."}], "transactions": [{"amount": -65, "detailsTransactions": [{"name": "Organic Vegetables Selection", "quantity": 6, "unitPrice": 3.5, "amount": -21, "categoryId": "Groceries", "projectId": null}, {"name": "Alaskan Fish Fillets", "quantity": 2, "unitPrice": 18, "amount": -36, "categoryId": "Groceries", "projectId": null}, {"name": "Whole Wheat Bread", "quantity": 2, "unitPrice": 4, "amount": -8, "categoryId": "Groceries", "projectId": null}], "payee": "Biomarkt", "notes": "Weekly grocery shopping", "date": "2024-11-12", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}, {"amount": -20, "detailsTransactions": [{"name": "Weekly Taxi Expense", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Transportation", "projectId": null}], "payee": "Lokal Taxi Service", "notes": "Transportation for the week", "date": "2024-11-13", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -50, "detailsTransactions": [{"name": "Plant-based Evening Dining", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Dining Out", "projectId": null}], "payee": "<PERSON><PERSON><PERSON><PERSON>", "notes": "Dinner with family", "date": "2024-11-14", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Dining Out"}, {"amount": -30, "detailsTransactions": [{"name": "Capstone Course Enrollment", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Education", "projectId": null}], "payee": "<PERSON><PERSON><PERSON><PERSON>", "notes": "Upcoming Capstone Course", "date": "2024-11-15", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Education"}, {"amount": -40, "detailsTransactions": [{"name": "Routine Medical Evaluation", "quantity": 1, "unitPrice": 40, "amount": -40, "categoryId": "Healthcare", "projectId": null}], "payee": "Zentrales Gesundheitszentrum", "notes": "Routine health examination", "date": "2024-11-16", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Healthcare"}, {"amount": -15, "detailsTransactions": [{"name": "Online Movie Streaming Rental", "quantity": 1, "unitPrice": 15, "amount": -15, "categoryId": "Entertainment", "projectId": null}], "payee": "Digitale Kino Plattform", "notes": "Weekend movie rental", "date": "2024-11-17", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Entertainment"}, {"amount": -20, "detailsTransactions": [{"name": "Extra Savings Investment", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Savings", "projectId": "Children's Education Fund"}], "payee": "Sparschatzkonto", "notes": "Additional contribution for children's education", "date": "2024-11-18", "projectId": "Children's Education Fund", "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Savings"}]}, {"categories": [{"name": "Rent", "goal": 1100}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 150}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 120}, {"name": "Education", "goal": 150}, {"name": "Entertainment", "goal": 80}, {"name": "Healthcare", "goal": 100}, {"name": "Savings", "goal": 1200}, {"name": "Health & Wellness", "goal": 100}], "accounts": [{"name": "Deutsche Bank Hauptkonto"}], "projects": [{"name": "Children's Education Fund", "budget": 10000, "startDate": "2024-01-01", "endDate": "2030-12-31", "description": "Savings for children's higher education in Germany."}], "transactions": [{"amount": -70, "detailsTransactions": [{"name": "Apple and Orange Selection", "quantity": 15, "unitPrice": 2, "amount": -30, "categoryId": "Groceries", "projectId": null}, {"name": "Assorted Dairy Delights", "quantity": 5, "unitPrice": 8, "amount": -40, "categoryId": "Groceries", "projectId": null}], "payee": "Supermarkt Hirsch", "notes": "Weekly grocery shopping", "date": "2024-11-19", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}, {"amount": -10, "detailsTransactions": [{"name": "Public Transport Weekly Pass", "quantity": 1, "unitPrice": 10, "amount": -10, "categoryId": "Transportation", "projectId": null}], "payee": "Städtische Verkehrsbetriebe", "notes": "Public transport for the week", "date": "2024-11-20", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -60, "detailsTransactions": [{"name": "Colleagues' Dinner Gala", "quantity": 1, "unitPrice": 60, "amount": -60, "categoryId": "Dining Out", "projectId": null}], "payee": "Bayerische Delikatessen", "notes": "Dinner with colleagues", "date": "2024-11-21", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Dining Out"}, {"amount": -45, "detailsTransactions": [{"name": "Zen Yoga Monthly Subscription", "quantity": 1, "unitPrice": 45, "amount": -45, "categoryId": "Health & Wellness", "projectId": null}], "payee": "Yoga Oase Studio", "notes": "Monthly yoga classes", "date": "2024-11-22", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Health & Wellness"}, {"amount": -20, "detailsTransactions": [{"name": "Children's Learning Game Set", "quantity": 2, "unitPrice": 10, "amount": -20, "categoryId": "Education", "projectId": null}], "payee": "<PERSON><PERSON><PERSON>", "notes": "Games for educational purposes", "date": "2024-11-23", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Education"}, {"amount": -15, "detailsTransactions": [{"name": "Streaming Family Movie Night Package", "quantity": 1, "unitPrice": 15, "amount": -15, "categoryId": "Entertainment", "projectId": null}], "payee": "Heimkino Streaming Dienst", "notes": "Family movie night", "date": "2024-11-24", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Entertainment"}, {"amount": -100, "detailsTransactions": [{"name": "Education Fund Monthly Contribution", "quantity": 1, "unitPrice": 100, "amount": -100, "categoryId": "Savings", "projectId": "Children's Education Fund"}], "payee": "Bildungskonto", "notes": "Monthly contribution to education fund", "date": "2024-11-25", "projectId": "Children's Education Fund", "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Savings"}]}, {"categories": [{"name": "Rent", "goal": 1100}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 150}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 120}, {"name": "Education", "goal": 150}, {"name": "Entertainment", "goal": 80}, {"name": "Healthcare", "goal": 100}, {"name": "Savings", "goal": 1200}], "accounts": [{"name": "Deutsche Bank Hauptkonto"}], "projects": [{"name": "Children's Education Fund", "budget": 10000, "startDate": "2024-01-01", "endDate": "2030-12-31", "description": "Savings for children's higher education in Germany."}], "transactions": [{"amount": -68, "detailsTransactions": [{"name": "Fresh Green Vegetables", "quantity": 12, "unitPrice": 2.5, "amount": -30, "categoryId": "Groceries", "projectId": null}, {"name": "Organic Chicken Breast", "quantity": 3, "unitPrice": 10, "amount": -30, "categoryId": "Groceries", "projectId": null}, {"name": "Fruit Juice Packs", "quantity": 2, "unitPrice": 4, "amount": -8, "categoryId": "Groceries", "projectId": null}], "payee": "<PERSON><PERSON><PERSON>", "notes": "Grocery shopping for the week", "date": "2024-11-26", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}, {"amount": -15, "detailsTransactions": [{"name": "Monthly Tram Card", "quantity": 1, "unitPrice": 15, "amount": -15, "categoryId": "Transportation", "projectId": null}], "payee": "Öffentlicher Nahverkehr", "notes": "Weekly tram pass", "date": "2024-11-27", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -45, "detailsTransactions": [{"name": "Dinner at Family Bistro", "quantity": 1, "unitPrice": 45, "amount": -45, "categoryId": "Dining Out", "projectId": null}], "payee": "Familien <PERSON>", "notes": "Monthly family dinner out", "date": "2024-11-28", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Dining Out"}, {"amount": -75, "detailsTransactions": [{"name": "Electricity Bill - Winter", "quantity": 1, "unitPrice": 40, "amount": -40, "categoryId": "Utilities", "projectId": null}, {"name": "Gas Bill - Winter Season", "quantity": 1, "unitPrice": 35, "amount": -35, "categoryId": "Utilities", "projectId": null}], "payee": "Energieversorgungsgesellschaft", "notes": "Utility bill for electricity and gas", "date": "2024-11-29", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Utilities"}, {"amount": -35, "detailsTransactions": [{"name": "Monthly German Course Subscription", "quantity": 1, "unitPrice": 35, "amount": -35, "categoryId": "Education", "projectId": null}], "payee": "Sprachlern Plattform", "notes": "Monthly subscription for online German classes", "date": "2024-11-30", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Education"}, {"amount": -60, "detailsTransactions": [{"name": "Premium Health Club Membership", "quantity": 1, "unitPrice": 60, "amount": -60, "categoryId": "Healthcare", "projectId": null}], "payee": "Fitness Zentrum", "notes": "Monthly health and wellness", "date": "2024-12-01", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Healthcare"}, {"amount": -1200, "detailsTransactions": [{"name": "December Rent Payment", "quantity": 1, "unitPrice": 1200, "amount": -1200, "categoryId": "Rent", "projectId": null}], "payee": "Sunrise Apartments Verwaltung", "notes": "Rent payment for December", "date": "2024-12-02", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Rent"}]}, {"categories": [{"name": "Rent", "goal": 1100}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 150}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 120}, {"name": "Education", "goal": 150}, {"name": "Entertainment", "goal": 80}, {"name": "Healthcare", "goal": 100}, {"name": "Savings", "goal": 1200}], "accounts": [{"name": "Deutsche Bank Hauptkonto"}], "projects": [{"name": "Children's Education Fund", "budget": 10000, "startDate": "2024-01-01", "endDate": "2030-12-31", "description": "Savings for children's higher education in Germany."}], "transactions": [{"amount": -72, "detailsTransactions": [{"name": "Assorted Organic Veggies", "quantity": 14, "unitPrice": 3, "amount": -42, "categoryId": "Groceries", "projectId": null}, {"name": "<PERSON><PERSON><PERSON>a", "quantity": 2, "unitPrice": 5, "amount": -10, "categoryId": "Groceries", "projectId": null}, {"name": "Imported Aged Cheese", "quantity": 2, "unitPrice": 10, "amount": -20, "categoryId": "Groceries", "projectId": null}], "payee": "Bio Laden Natur", "notes": "Weekly grocery purchase", "date": "2024-12-03", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}, {"amount": -12, "detailsTransactions": [{"name": "Integrated Public Transport Pass", "quantity": 1, "unitPrice": 12, "amount": -12, "categoryId": "Transportation", "projectId": null}], "payee": "Städtische Verkehrsdienste", "notes": "Week's transport card", "date": "2024-12-04", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -50, "detailsTransactions": [{"name": "Networking Lunch with Colleagues", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Dining Out", "projectId": null}, {"name": "Café Meetup with Friends", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Dining Out", "projectId": null}], "payee": "Kaffee & Bistro", "notes": "Social lunches during the week", "date": "2024-12-05", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Dining Out"}, {"amount": -45, "detailsTransactions": [{"name": "Wellness Checkup Fee", "quantity": 1, "unitPrice": 45, "amount": -45, "categoryId": "Healthcare", "projectId": null}], "payee": "Gesundheitsdienstleister", "notes": "Routine appointment charge", "date": "2024-12-06", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Healthcare"}, {"amount": -30, "detailsTransactions": [{"name": "Classic Family Board Game", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Entertainment", "projectId": null}], "payee": "Spielwarengeschäft", "notes": "New game for family night", "date": "2024-12-07", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Entertainment"}, {"amount": -25, "detailsTransactions": [{"name": "Live Concert Entry Tickets", "quantity": 2, "unitPrice": 12.5, "amount": -25, "categoryId": "Entertainment", "projectId": null}], "payee": "Konzert Arena", "notes": "Tickets to music concert", "date": "2024-12-08", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Entertainment"}, {"amount": -110, "detailsTransactions": [{"name": "Educational Fund Monthly Savings", "quantity": 1, "unitPrice": 110, "amount": -110, "categoryId": "Savings", "projectId": "Children's Education Fund"}], "payee": "Sparanstalt", "notes": "Regular savings for education fund", "date": "2024-12-09", "projectId": "Children's Education Fund", "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Savings"}]}, {"categories": [{"name": "Rent", "goal": 1100}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 150}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 120}, {"name": "Education", "goal": 150}, {"name": "Entertainment", "goal": 80}, {"name": "Healthcare", "goal": 100}, {"name": "Savings", "goal": 1200}, {"name": "Miscellaneous", "goal": 100}], "accounts": [{"name": "Deutsche Bank Hauptkonto"}], "projects": [{"name": "Children's Education Fund", "budget": 10000, "startDate": "2024-01-01", "endDate": "2030-12-31", "description": "Savings for children's higher education in Germany."}], "transactions": [{"amount": -67, "detailsTransactions": [{"name": "Organic Farm Produce", "quantity": 14, "unitPrice": 3, "amount": -42, "categoryId": "Groceries", "projectId": null}, {"name": "Whole Chicken Roast", "quantity": 1, "unitPrice": 15, "amount": -15, "categoryId": "Groceries", "projectId": null}, {"name": "Almond Milk Packs", "quantity": 2, "unitPrice": 5, "amount": -10, "categoryId": "Groceries", "projectId": null}], "payee": "<PERSON><PERSON><PERSON>", "notes": "Grocery run for the week", "date": "2024-12-10", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}, {"amount": -20, "detailsTransactions": [{"name": "Weekly Travel Card", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Transportation", "projectId": null}], "payee": "Verkehrsnetzwerk", "notes": "Weekly metro ticket", "date": "2024-12-11", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -40, "detailsTransactions": [{"name": "Wednesday Lunch Indulgence", "quantity": 1, "unitPrice": 40, "amount": -40, "categoryId": "Dining Out", "projectId": null}], "payee": "Innenstadt Esslokal", "notes": "Midweek lunch treat", "date": "2024-12-12", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Dining Out"}, {"amount": -30, "detailsTransactions": [{"name": "E-Learning Subscription Renewal", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Education", "projectId": null}], "payee": "Digitale Lernplattform", "notes": "Monthly subscription renewal", "date": "2024-12-13", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Education"}, {"amount": -50, "detailsTransactions": [{"name": "Tickets for Children's Winter Show", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Entertainment", "projectId": null}], "payee": "Städtisches Theater", "notes": "Booking for children's holiday show", "date": "2024-12-14", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Entertainment"}, {"amount": -35, "detailsTransactions": [{"name": "Festive Gift Purchases", "quantity": 1, "unitPrice": 35, "amount": -35, "categoryId": "Miscellaneous", "projectId": null}], "payee": "<PERSON><PERSON><PERSON>", "notes": "Gifts for upcoming celebrations", "date": "2024-12-15", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Miscellaneous"}, {"amount": -120, "detailsTransactions": [{"name": "December Savings Boost", "quantity": 1, "unitPrice": 120, "amount": -120, "categoryId": "Savings", "projectId": "Children's Education Fund"}], "payee": "Sparbank", "notes": "Extra savings for education fund", "date": "2024-12-16", "projectId": "Children's Education Fund", "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Savings"}]}, {"categories": [{"name": "Rent", "goal": 1100}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 150}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 120}, {"name": "Education", "goal": 150}, {"name": "Entertainment", "goal": 80}, {"name": "Healthcare", "goal": 100}, {"name": "Savings", "goal": 1200}], "accounts": [{"name": "Deutsche Bank Hauptkonto"}], "projects": [{"name": "Children's Education Fund", "budget": 10000, "startDate": "2024-01-01", "endDate": "2030-12-31", "description": "Savings for children's higher education in Germany."}], "transactions": [{"amount": -70, "detailsTransactions": [{"name": "Winter Harvest Vegetables", "quantity": 10, "unitPrice": 3, "amount": -30, "categoryId": "Groceries", "projectId": null}, {"name": "Grass-Fed Beef Steaks", "quantity": 2, "unitPrice": 20, "amount": -40, "categoryId": "Groceries", "projectId": null}], "payee": "Lidl Bio-Markt", "notes": "Weekly groceries for holidays", "date": "2024-12-17", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}, {"amount": -25, "detailsTransactions": [{"name": "Weekly Public Transport Pass", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Transportation", "projectId": null}], "payee": "Verkehrsbehörde", "notes": "Week's public transit pass", "date": "2024-12-18", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -60, "detailsTransactions": [{"name": "Festive Family Dinner", "quantity": 1, "unitPrice": 60, "amount": -60, "categoryId": "Dining Out", "projectId": null}], "payee": "<PERSON><PERSON>", "notes": "Dinner with extended family", "date": "2024-12-19", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Dining Out"}, {"amount": -55, "detailsTransactions": [{"name": "Monthly Health Insurance Fee", "quantity": 1, "unitPrice": 55, "amount": -55, "categoryId": "Healthcare", "projectId": null}], "payee": "Gesundheitsversicherung GmbH", "notes": "Monthly premium", "date": "2024-12-20", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Healthcare"}, {"amount": -50, "detailsTransactions": [{"name": "Christmas Piano Concert Seats", "quantity": 2, "unitPrice": 25, "amount": -50, "categoryId": "Entertainment", "projectId": null}], "payee": "Konzertsaal", "notes": "Holiday recital tickets for family", "date": "2024-12-21", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Entertainment"}, {"amount": -90, "detailsTransactions": [{"name": "Combined Electricity Bill", "quantity": 1, "unitPrice": 45, "amount": -45, "categoryId": "Utilities", "projectId": null}, {"name": "Combined Water Bill", "quantity": 1, "unitPrice": 45, "amount": -45, "categoryId": "Utilities", "projectId": null}], "payee": "Versorgungsdienste", "notes": "Combined utility bills for electricity and water", "date": "2024-12-22", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Utilities"}, {"amount": -115, "detailsTransactions": [{"name": "End of Year Savings for Education", "quantity": 1, "unitPrice": 115, "amount": -115, "categoryId": "Savings", "projectId": "Children's Education Fund"}], "payee": "<PERSON><PERSON>", "notes": "Year-end contribution to education fund", "date": "2024-12-23", "projectId": "Children's Education Fund", "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Savings"}]}, {"categories": [{"name": "Rent", "goal": 1100}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 150}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 120}, {"name": "Education", "goal": 150}, {"name": "Entertainment", "goal": 80}, {"name": "Healthcare", "goal": 100}, {"name": "Savings", "goal": 1200}, {"name": "Miscellaneous", "goal": 100}], "accounts": [{"name": "Deutsche Bank Hauptkonto"}], "projects": [{"name": "Children's Education Fund", "budget": 10000, "startDate": "2024-01-01", "endDate": "2030-12-31", "description": "Savings for children's higher education in Germany."}], "transactions": [{"amount": -100, "detailsTransactions": [{"name": "Holiday Feast Essentials", "quantity": 1, "unitPrice": 100, "amount": -100, "categoryId": "Groceries", "projectId": null}], "payee": "Supermarkt", "notes": "Grocery shopping for holiday celebrations", "date": "2024-12-24", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}, {"amount": -10, "detailsTransactions": [{"name": "Holiday Taxi Service", "quantity": 1, "unitPrice": 10, "amount": -10, "categoryId": "Transportation", "projectId": null}], "payee": "Lokal Taxi Service", "notes": "Holiday travel around town", "date": "2024-12-25", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -20, "detailsTransactions": [{"name": "Christmas Gift Vouchers", "quantity": 4, "unitPrice": 5, "amount": -20, "categoryId": "Miscellaneous", "projectId": null}], "payee": "<PERSON><PERSON><PERSON>", "notes": "Last-minute gift cards", "date": "2024-12-26", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Miscellaneous"}, {"amount": -45, "detailsTransactions": [{"name": "Decorations for New Year’s Eve", "quantity": 1, "unitPrice": 45, "amount": -45, "categoryId": "Entertainment", "projectId": null}], "payee": "Partybedarfgeschäft", "notes": "New Year’s party decorations", "date": "2024-12-27", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Entertainment"}, {"amount": -90, "detailsTransactions": [{"name": "Yoga and Wellness Package", "quantity": 1, "unitPrice": 90, "amount": -90, "categoryId": "Healthcare", "projectId": null}], "payee": "<PERSON><PERSON><PERSON><PERSON>", "notes": "Renewal for wellness into the new year", "date": "2024-12-28", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Healthcare"}, {"amount": -150, "detailsTransactions": [{"name": "End-of-Year Donation", "quantity": 1, "unitPrice": 150, "amount": -150, "categoryId": "Savings", "projectId": null}], "payee": "Wohltätigkeitsorganisation", "notes": "Year-end charity contribution", "date": "2024-12-29", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Savings"}, {"amount": -85, "detailsTransactions": [{"name": "Holiday Season Savings Contribution", "quantity": 1, "unitPrice": 85, "amount": -85, "categoryId": "Savings", "projectId": "Children's Education Fund"}], "payee": "<PERSON><PERSON>", "notes": "Extra holiday season savings", "date": "2024-12-30", "projectId": "Children's Education Fund", "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Savings"}]}, {"categories": [{"name": "Rent", "goal": 1100}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 150}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 120}, {"name": "Education", "goal": 150}, {"name": "Entertainment", "goal": 80}, {"name": "Healthcare", "goal": 100}, {"name": "Savings", "goal": 1200}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Deutsche Bank Hauptkonto"}], "projects": [{"name": "Children's Education Fund", "budget": 10000, "startDate": "2024-01-01", "endDate": "2030-12-31", "description": "Savings for children's higher education in Germany."}], "transactions": [{"amount": -85, "detailsTransactions": [{"name": "New Year's Celebration Kit", "quantity": 1, "unitPrice": 85, "amount": -85, "categoryId": "Entertainment", "projectId": null}], "payee": "<PERSON><PERSON><PERSON><PERSON>", "notes": "Celebration essentials for New Year's Eve", "date": "2024-12-31", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Entertainment"}, {"amount": -1100, "detailsTransactions": [{"name": "January Rent Payment", "quantity": 1, "unitPrice": 1100, "amount": -1100, "categoryId": "Rent", "projectId": null}], "payee": "Sunrise Apartments Verwaltung", "notes": "Rent payment for January", "date": "2025-01-01", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Rent"}, {"amount": -120, "detailsTransactions": [{"name": "January's Combined Utility Bills", "quantity": 1, "unitPrice": 120, "amount": -120, "categoryId": "Utilities", "projectId": null}], "payee": "Versorgungsgesellschaft", "notes": "Monthly utility bills", "date": "2025-01-02", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Utilities"}, {"amount": -65, "detailsTransactions": [{"name": "First Week Groceries for New Year", "quantity": 1, "unitPrice": 65, "amount": -65, "categoryId": "Groceries", "projectId": null}], "payee": "Supermarkt Lebensmittel", "notes": "Groceries for the first week of the New Year", "date": "2025-01-03", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}, {"amount": -15, "detailsTransactions": [{"name": "January Week Travel Card", "quantity": 1, "unitPrice": 15, "amount": -15, "categoryId": "Transportation", "projectId": null}], "payee": "Verkehrsbehörde", "notes": "Weekly travel for public transportation", "date": "2025-01-04", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -20, "detailsTransactions": [{"name": "Weekend Lunch at Café", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Dining Out", "projectId": null}], "payee": "Ecks Café", "notes": "Lunch during weekend outing", "date": "2025-01-05", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Dining Out"}, {"amount": -100, "detailsTransactions": [{"name": "January Savings for Education Fund", "quantity": 1, "unitPrice": 100, "amount": -100, "categoryId": "Savings", "projectId": "Children's Education Fund"}], "payee": "<PERSON><PERSON>", "notes": "Annual savings contribution for children's education", "date": "2025-01-06", "projectId": "Children's Education Fund", "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Savings"}]}, {"categories": [{"name": "Rent", "goal": 1100}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 150}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 120}, {"name": "Education", "goal": 150}, {"name": "Entertainment", "goal": 80}, {"name": "Healthcare", "goal": 100}, {"name": "Savings", "goal": 1200}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Deutsche Bank Hauptkonto"}], "projects": [{"name": "Children's Education Fund", "budget": 10000, "startDate": "2024-01-01", "endDate": "2030-12-31", "description": "Savings for children's higher education in Germany."}], "transactions": [{"amount": -70, "detailsTransactions": [{"name": "Farm-Fresh Organic Groceries", "quantity": 1, "unitPrice": 70, "amount": -70, "categoryId": "Groceries", "projectId": null}], "payee": "Bio Markt", "notes": "Weekly groceries", "date": "2025-01-07", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}, {"amount": -10, "detailsTransactions": [{"name": "Weekly Bus Travel Tickets", "quantity": 1, "unitPrice": 10, "amount": -10, "categoryId": "Transportation", "projectId": null}], "payee": "Busgesellschaft", "notes": "Public transportation for the week", "date": "2025-01-08", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -60, "detailsTransactions": [{"name": "Automobile Fuel Refill", "quantity": 1, "unitPrice": 60, "amount": -60, "categoryId": "Transportation", "projectId": null}], "payee": "Tankstelle", "notes": "Refueling the car", "date": "2025-01-09", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -25, "detailsTransactions": [{"name": "Work Lunch with Colleagues", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Dining Out", "projectId": null}], "payee": "Zentrum Feinkostladen", "notes": "Lunch with colleagues", "date": "2025-01-10", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Dining Out"}, {"amount": -30, "detailsTransactions": [{"name": "E-Learning Monthly Access", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Education", "projectId": null}], "payee": "Digitale Bildungsplattform", "notes": "Monthly subscription renewal", "date": "2025-01-11", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Education"}, {"amount": -15, "detailsTransactions": [{"name": "Weekend Family Movie Streaming Rental", "quantity": 1, "unitPrice": 15, "amount": -15, "categoryId": "Entertainment", "projectId": null}], "payee": "Streaming Dienst", "notes": "Weekend family movie night rental", "date": "2025-01-12", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Entertainment"}, {"amount": -50, "detailsTransactions": [{"name": "Regular Medical Examination", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Healthcare", "projectId": null}], "payee": "Medizinisches Zentrum", "notes": "Routine health check-up", "date": "2025-01-13", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Healthcare"}]}, {"categories": [{"name": "Rent", "goal": 1100}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 150}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 120}, {"name": "Education", "goal": 150}, {"name": "Entertainment", "goal": 80}, {"name": "Healthcare", "goal": 100}, {"name": "Savings", "goal": 1200}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Deutsche Bank Hauptkonto"}], "projects": [{"name": "Children's Education Fund", "budget": 10000, "startDate": "2024-01-01", "endDate": "2030-12-31", "description": "Savings for children's higher education in Germany."}], "transactions": [{"amount": -65, "detailsTransactions": [{"name": "Weekly Vegetables and High-Quality Meats", "quantity": 1, "unitPrice": 65, "amount": -65, "categoryId": "Groceries", "projectId": null}], "payee": "Frische Lebensmittel Markt", "notes": "Weekly grocery shopping", "date": "2025-01-14", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}, {"amount": -12, "detailsTransactions": [{"name": "Weekly City Tram Pass", "quantity": 1, "unitPrice": 12, "amount": -12, "categoryId": "Transportation", "projectId": null}], "payee": "Städtische Verkehrsmittel", "notes": "Weekly tram pass", "date": "2025-01-15", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -50, "detailsTransactions": [{"name": "Family Birthday Celebration Dinner", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Dining Out", "projectId": null}], "payee": "Trattoria Italiano", "notes": "Family birthday celebration", "date": "2025-01-16", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Dining Out"}, {"amount": -80, "detailsTransactions": [{"name": "Heating and Electricity - Winter Season", "quantity": 1, "unitPrice": 80, "amount": -80, "categoryId": "Utilities", "projectId": null}], "payee": "Energieunternehmen", "notes": "Monthly utilities", "date": "2025-01-17", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Utilities"}, {"amount": -40, "detailsTransactions": [{"name": "Career Advancement Online Course", "quantity": 1, "unitPrice": 40, "amount": -40, "categoryId": "Education", "projectId": null}], "payee": "Berufliche Entwicklung GmbH", "notes": "Enrollment for career advancement", "date": "2025-01-18", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Education"}, {"amount": -25, "detailsTransactions": [{"name": "Entertainment Streaming Subscription", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Entertainment", "projectId": null}], "payee": "Streaming Online Dienst", "notes": "Monthly subscription charge", "date": "2025-01-19", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Entertainment"}, {"amount": -130, "detailsTransactions": [{"name": "Children's Education Fund Monthly Contribution", "quantity": 1, "unitPrice": 130, "amount": -130, "categoryId": "Savings", "projectId": "Children's Education Fund"}], "payee": "<PERSON><PERSON>", "notes": "Monthly allocation to savings fund", "date": "2025-01-20", "projectId": "Children's Education Fund", "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Savings"}]}, {"categories": [{"name": "Rent", "goal": 1100}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 150}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 120}, {"name": "Education", "goal": 150}, {"name": "Entertainment", "goal": 80}, {"name": "Healthcare", "goal": 100}, {"name": "Savings", "goal": 1200}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Deutsche Bank Hauptkonto"}], "projects": [{"name": "Children's Education Fund", "budget": 10000, "startDate": "2024-01-01", "endDate": "2030-12-31", "description": "Savings for children's higher education in Germany."}], "transactions": [{"amount": -75, "detailsTransactions": [{"name": "Organic Weekly Produce Selection", "quantity": 1, "unitPrice": 75, "amount": -75, "categoryId": "Groceries", "projectId": null}], "payee": "Fr<PERSON>r Markt", "notes": "Weekly grocery shopping", "date": "2025-01-21", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}, {"amount": -10, "detailsTransactions": [{"name": "Local Bus Transportation Ticket", "quantity": 1, "unitPrice": 10, "amount": -10, "categoryId": "Transportation", "projectId": null}], "payee": "Busverkehr", "notes": "Weekly bus fare", "date": "2025-01-22", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -35, "detailsTransactions": [{"name": "Family Night Dinner", "quantity": 1, "unitPrice": 35, "amount": -35, "categoryId": "Dining Out", "projectId": null}], "payee": "Familien <PERSON>", "notes": "Dinner out with family", "date": "2025-01-23", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Dining Out"}, {"amount": -25, "detailsTransactions": [{"name": "Monthly Household Water Charges", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Utilities", "projectId": null}], "payee": "Wasserwerke GmbH", "notes": "Monthly water utility payment", "date": "2025-01-24", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Utilities"}, {"amount": -90, "detailsTransactions": [{"name": "January Private Tutoring Sessions", "quantity": 1, "unitPrice": 90, "amount": -90, "categoryId": "Education", "projectId": null}], "payee": "Privater <PERSON><PERSON>", "notes": "Monthly tutoring for children", "date": "2025-01-25", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Education"}, {"amount": -30, "detailsTransactions": [{"name": "Access to Local Live Band Night", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Entertainment", "projectId": null}], "payee": "Konzertsaal", "notes": "Local music event tickets", "date": "2025-01-26", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Entertainment"}, {"amount": -15, "detailsTransactions": [{"name": "Supplemental Emergency Savings", "quantity": 1, "unitPrice": 15, "amount": -15, "categoryId": "Savings", "projectId": "Children's Education Fund"}], "payee": "<PERSON><PERSON>", "notes": "Additional contribution to emergency fund", "date": "2025-01-27", "projectId": "Children's Education Fund", "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Savings"}]}, {"categories": [{"name": "Rent", "goal": 1100}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 150}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 120}, {"name": "Education", "goal": 150}, {"name": "Entertainment", "goal": 80}, {"name": "Healthcare", "goal": 100}, {"name": "Savings", "goal": 1200}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Deutsche Bank Hauptkonto"}], "projects": [{"name": "Children's Education Fund", "budget": 10000, "startDate": "2024-01-01", "endDate": "2030-12-31", "description": "Savings for children's higher education in Germany."}], "transactions": [{"amount": -70, "detailsTransactions": [{"name": "Local Butcher Meats", "quantity": 1, "unitPrice": 40, "amount": -40, "categoryId": "Groceries", "projectId": null}, {"name": "Seasonal Vegetables Assortment", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Groceries", "projectId": null}], "payee": "BioMarkt", "notes": "Weekly grocery supply", "date": "2025-01-28", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}, {"amount": -20, "detailsTransactions": [{"name": "Monthly Metro Card Recharge", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Transportation", "projectId": null}], "payee": "Öffentliche Verkehrsmittel", "notes": "Monthly metro card refill", "date": "2025-01-29", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -50, "detailsTransactions": [{"name": "Colleagues' Lunch Gathering", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Dining Out", "projectId": null}], "payee": "<PERSON><PERSON><PERSON>", "notes": "Lunch with office team", "date": "2025-01-30", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Dining Out"}, {"amount": -90, "detailsTransactions": [{"name": "January Electricity Charges", "quantity": 1, "unitPrice": 90, "amount": -90, "categoryId": "Utilities", "projectId": null}], "payee": "Elektrizitätsgesellschaft", "notes": "Monthly electricity payment", "date": "2025-01-31", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Utilities"}, {"amount": -75, "detailsTransactions": [{"name": "Premium Streaming Service", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Entertainment", "projectId": null}, {"name": "Quarterly Digital Magazine", "quantity": 1, "unitPrice": 55, "amount": -55, "categoryId": "Education", "projectId": null}], "payee": "Digitale Dienste AG", "notes": "Monthly streaming and magazine subscription fee", "date": "2025-02-01", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Entertainment"}, {"amount": -25, "detailsTransactions": [{"name": "Fitness Studio Membership", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Healthcare", "projectId": null}], "payee": "Lokales Fitnesszentrum", "notes": "Monthly health and fitness expense", "date": "2025-02-02", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Healthcare"}, {"amount": -125, "detailsTransactions": [{"name": "Contribution to Education Savings Fund", "quantity": 1, "unitPrice": 125, "amount": -125, "categoryId": "Savings", "projectId": "Children's Education Fund"}], "payee": "<PERSON><PERSON>", "notes": "Monthly savings allocation for education fund", "date": "2025-02-03", "projectId": "Children's Education Fund", "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Savings"}]}, {"categories": [{"name": "Rent", "goal": 1100}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 150}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 120}, {"name": "Education", "goal": 150}, {"name": "Entertainment", "goal": 80}, {"name": "Healthcare", "goal": 100}, {"name": "Savings", "goal": 1200}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Deutsche Bank Hauptkonto"}], "projects": [{"name": "Children's Education Fund", "budget": 10000, "startDate": "2024-01-01", "endDate": "2030-12-31", "description": "Savings for children's higher education in Germany."}], "transactions": [{"amount": -80, "detailsTransactions": [{"name": "Farm Fresh Organic Groceries", "quantity": 1, "unitPrice": 80, "amount": -80, "categoryId": "Groceries", "projectId": null}], "payee": "Grünmarkt", "notes": "Weekly supplies of fruits and vegetables", "date": "2025-02-04", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}, {"amount": -18, "detailsTransactions": [{"name": "Weekly Commuter Train Pass", "quantity": 1, "unitPrice": 18, "amount": -18, "categoryId": "Transportation", "projectId": null}], "payee": "Bahnhof", "notes": "Public transport expenses for the week", "date": "2025-02-05", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -30, "detailsTransactions": [{"name": "Lunch Special with <PERSON>", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Dining Out", "projectId": null}], "payee": "Stadtcafé", "notes": "Lunch with college friends", "date": "2025-02-06", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Dining Out"}, {"amount": -15, "detailsTransactions": [{"name": "Monthly Charity Support Donation", "quantity": 1, "unitPrice": 15, "amount": -15, "categoryId": "Miscellaneous", "projectId": null}], "payee": "Wohltätigkeitsspende", "notes": "Monthly charitable donation", "date": "2025-02-07", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Miscellaneous"}, {"amount": -40, "detailsTransactions": [{"name": "Children's Math Enrichment Session", "quantity": 1, "unitPrice": 40, "amount": -40, "categoryId": "Education", "projectId": null}], "payee": "Privater <PERSON><PERSON>", "notes": "Weekly tutoring class for children", "date": "2025-02-08", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Education"}, {"amount": -50, "detailsTransactions": [{"name": "Family Film Festival Tickets", "quantity": 4, "unitPrice": 12.5, "amount": -50, "categoryId": "Entertainment", "projectId": null}], "payee": "<PERSON><PERSON>", "notes": "Outing with family to watch latest movie", "date": "2025-02-09", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Entertainment"}, {"amount": -100, "detailsTransactions": [{"name": "February Health Coverage Premium", "quantity": 1, "unitPrice": 100, "amount": -100, "categoryId": "Healthcare", "projectId": null}], "payee": "Gesundheitsversicherung AG", "notes": "Health insurance premium for February", "date": "2025-02-10", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Healthcare"}]}, {"categories": [{"name": "Rent", "goal": 1100}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 150}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 120}, {"name": "Education", "goal": 150}, {"name": "Entertainment", "goal": 80}, {"name": "Healthcare", "goal": 100}, {"name": "Savings", "goal": 1200}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Deutsche Bank Hauptkonto"}], "projects": [{"name": "Children's Education Fund", "budget": 10000, "startDate": "2024-01-01", "endDate": "2030-12-31", "description": "Savings for children's higher education in Germany."}], "transactions": [{"amount": -72, "detailsTransactions": [{"name": "Weekly Organic Green Groceries", "quantity": 1, "unitPrice": 72, "amount": -72, "categoryId": "Groceries", "projectId": null}], "payee": "Lokales Lebensmittelgeschäft", "notes": "Weekly grocery shopping", "date": "2025-02-11", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Groceries"}, {"amount": -15, "detailsTransactions": [{"name": "Public Transit Monthly Pass", "quantity": 1, "unitPrice": 15, "amount": -15, "categoryId": "Transportation", "projectId": null}], "payee": "Verkehrsbehörde", "notes": "Weekly bus travel expenses", "date": "2025-02-12", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Transportation"}, {"amount": -55, "detailsTransactions": [{"name": "Valentine's Day Couple's Dinner", "quantity": 1, "unitPrice": 55, "amount": -55, "categoryId": "Dining Out", "projectId": null}], "payee": "Romantisches Restaurant", "notes": "Dinner with spouse", "date": "2025-02-13", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Dining Out"}, {"amount": -60, "detailsTransactions": [{"name": "February Utility Satisfaction Services", "quantity": 1, "unitPrice": 60, "amount": -60, "categoryId": "Utilities", "projectId": null}], "payee": "Dienstleistungsanbieter", "notes": "Monthly utilities for February", "date": "2025-02-14", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Utilities"}, {"amount": -40, "detailsTransactions": [{"name": "Children's February Craft Workshop", "quantity": 1, "unitPrice": 40, "amount": -40, "categoryId": "Education", "projectId": null}], "payee": "Kunstwerkstatt", "notes": "Monthly art class fee for February", "date": "2025-02-15", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Education"}, {"amount": -45, "detailsTransactions": [{"name": "Assorted Collection of Novels", "quantity": 1, "unitPrice": 45, "amount": -45, "categoryId": "Miscellaneous", "projectId": null}], "payee": "Buchhandlung", "notes": "Purchase of new books for personal reading", "date": "2025-02-16", "projectId": null, "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Miscellaneous"}, {"amount": -120, "detailsTransactions": [{"name": "Monthly Investment to Education Fund", "quantity": 1, "unitPrice": 120, "amount": -120, "categoryId": "Savings", "projectId": "Children's Education Fund"}], "payee": "<PERSON><PERSON>", "notes": "Additional savings for education fund", "date": "2025-02-17", "projectId": "Children's Education Fund", "accountId": "Deutsche Bank Hauptkonto", "categoryId": "Savings"}]}, {"categories": [{"name": "Rent", "goal": 1200}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 200}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 100}, {"name": "Entertainment", "goal": 100}, {"name": "Health & Wellness", "goal": 50}, {"name": "Savings", "goal": 800}, {"name": "Professional Development", "goal": 100}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Yamabuki Banking Group"}], "projects": [{"name": "New Restaurant Startup", "budget": 30000, "startDate": "2024-10-01", "endDate": "2025-10-01", "description": "Start and operate a new fusion cuisine restaurant."}], "transactions": [{"amount": -1200, "detailsTransactions": [{"name": "Monthly Rent Payment — Shinjuku Tower Residences", "quantity": 1, "unitPrice": 1200, "amount": -1200, "categoryId": "Rent", "projectId": null}], "payee": "Shinjuku Tower Realty", "notes": "Monthly rent for apartment", "date": "2024-10-01", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Rent"}, {"amount": 2500, "detailsTransactions": [], "payee": "Tokyo Fusion Bistro - Salary", "notes": "Monthly Salary after tax", "date": "2024-10-01", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Savings"}, {"amount": -95, "detailsTransactions": [{"name": "Koshihikari Rice (1kg) - Premium Grade", "quantity": 5, "unitPrice": 5, "amount": -25, "categoryId": "Groceries", "projectId": null}, {"name": "Seasonal Vegetables - Organic Farm Selection", "quantity": 20, "unitPrice": 2, "amount": -40, "categoryId": "Groceries", "projectId": null}, {"name": "Assorted Seafood - Tsukiji Selection", "quantity": 3, "unitPrice": 10, "amount": -30, "categoryId": "Groceries", "projectId": null}], "payee": "Tsukiji Wonder Market", "notes": "Weekly grocery shopping", "date": "2024-10-02", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Groceries"}, {"amount": -20, "detailsTransactions": [{"name": "Matcha Espresso - Green Leaf Café", "quantity": 1, "unitPrice": 15, "amount": -15, "categoryId": "Dining Out", "projectId": null}, {"name": "Azuki Bean Pastry - Delight with Sweetness", "quantity": 1, "unitPrice": 5, "amount": -5, "categoryId": "Dining Out", "projectId": null}], "payee": "Green Leaf Café", "notes": "Coffee with colleagues", "date": "2024-10-03", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}, {"amount": -60, "detailsTransactions": [{"name": "<PERSON><PERSON>", "quantity": 1, "unitPrice": 60, "amount": -60, "categoryId": "Dining Out", "projectId": null}], "payee": "Sakura Sushi Heights", "notes": "Dinner with friends", "date": "2024-10-04", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}, {"amount": -15, "detailsTransactions": [{"name": "Tokyo Metro Pass - Comprehensive Access", "quantity": 1, "unitPrice": 15, "amount": -15, "categoryId": "Transportation", "projectId": null}], "payee": "Tokyo Metro", "notes": "Monthly transportation pass", "date": "2024-10-05", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Transportation"}, {"amount": -35, "detailsTransactions": [{"name": "Advanced Class: Molecular Gastronomy", "quantity": 1, "unitPrice": 35, "amount": -35, "categoryId": "Professional Development", "projectId": null}], "payee": "Gourmet Academy Japan", "notes": "Participating in an advanced cooking workshop", "date": "2024-10-06", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Professional Development"}, {"amount": -25, "detailsTransactions": [{"name": "Weekly Yoga Session - Serenity Flow", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Health & Wellness", "projectId": null}], "payee": "Tokyo Zen Yoga Studio", "notes": "Weekly yoga class", "date": "2024-10-07", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Health & Wellness"}]}, {"categories": [{"name": "Rent", "goal": 1200}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 200}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 100}, {"name": "Entertainment", "goal": 100}, {"name": "Health & Wellness", "goal": 50}, {"name": "Savings", "goal": 800}, {"name": "Professional Development", "goal": 100}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Yamabuki Banking Group"}], "projects": [{"name": "New Restaurant Startup", "budget": 30000, "startDate": "2024-10-01", "endDate": "2025-10-01", "description": "Start and operate a new fusion cuisine restaurant."}], "transactions": [{"amount": -85, "detailsTransactions": [{"name": "Organic Fuji Apples (1kg)", "quantity": 5, "unitPrice": 3, "amount": -15, "categoryId": "Groceries", "projectId": null}, {"name": "Soya Milk - Fresh Harvest", "quantity": 8, "unitPrice": 5, "amount": -40, "categoryId": "Groceries", "projectId": null}, {"name": "Gourmet Spice Blend", "quantity": 5, "unitPrice": 3, "amount": -15, "categoryId": "Groceries", "projectId": null}], "payee": "Green Tokyo Market", "notes": "Weekly groceries including organic products", "date": "2024-10-08", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Groceries"}, {"amount": -45, "detailsTransactions": [{"name": "Petrol Full Tank - EcoDrive Fuel", "quantity": 1, "unitPrice": 45, "amount": -45, "categoryId": "Transportation", "projectId": null}], "payee": "EcoDrive Fuel Station", "notes": "Refill for the week's commute", "date": "2024-10-09", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Transportation"}, {"amount": -40, "detailsTransactions": [{"name": "Porcelain Dining Set - Five Piece", "quantity": 1, "unitPrice": 40, "amount": -40, "categoryId": "Miscellaneous", "projectId": "New Restaurant Startup"}], "payee": "Home Regal", "notes": "Purchase of crockery for restaurant use", "date": "2024-10-10", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Miscellaneous"}, {"amount": -10, "detailsTransactions": [{"name": "Monthly Subscription - Cineflix", "quantity": 1, "unitPrice": 10, "amount": -10, "categoryId": "Entertainment", "projectId": null}], "payee": "Cineflix", "notes": "Monthly movie subscription", "date": "2024-10-10", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Entertainment"}, {"amount": -70, "detailsTransactions": [{"name": "Gourmet Dinner - Truffle Extravaganza", "quantity": 1, "unitPrice": 70, "amount": -70, "categoryId": "Dining Out", "projectId": "New Restaurant Startup"}], "payee": "Tuscan Table", "notes": "Business dinner meeting", "date": "2024-10-11", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}, {"amount": -25, "detailsTransactions": [{"name": "Weekly Fitness Membership - Full Access", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Health & Wellness", "projectId": null}], "payee": "FitWell Tokyo", "notes": "Weekly gym membership", "date": "2024-10-12", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Health & Wellness"}, {"amount": -30, "detailsTransactions": [{"name": "Electricity Bill - Tokyo Energy Company", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Utilities", "projectId": null}], "payee": "Tokyo Energy Company", "notes": "Monthly electricity bill payment", "date": "2024-10-13", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Utilities"}, {"amount": -20, "detailsTransactions": [{"name": "Gourmet Recipe Book - Culinary Arts Collection", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Professional Development", "projectId": null}], "payee": "Tokyo Bookstore", "notes": "Purchase of culinary guide", "date": "2024-10-14", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Professional Development"}]}, {"categories": [{"name": "Rent", "goal": 1200}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 200}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 100}, {"name": "Entertainment", "goal": 100}, {"name": "Health & Wellness", "goal": 50}, {"name": "Savings", "goal": 800}, {"name": "Professional Development", "goal": 100}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Yamabuki Banking Group"}], "projects": [{"name": "New Restaurant Startup", "budget": 30000, "startDate": "2024-10-01", "endDate": "2025-10-01", "description": "Start and operate a new fusion cuisine restaurant."}], "transactions": [{"amount": -90, "detailsTransactions": [{"name": "Organic Seasonal Produce", "quantity": 7, "unitPrice": 5, "amount": -35, "categoryId": "Groceries", "projectId": null}, {"name": "Ancient Grains Assortment", "quantity": 4, "unitPrice": 5, "amount": -20, "categoryId": "Groceries", "projectId": null}, {"name": "Artisan Butcher Meats", "quantity": 3, "unitPrice": 10, "amount": -30, "categoryId": "Groceries", "projectId": null}], "payee": "Green Acre Market", "notes": "Weekly shopping for essential groceries", "date": "2024-10-15", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Groceries"}, {"amount": -50, "detailsTransactions": [{"name": "Client Meeting Rides", "quantity": 5, "unitPrice": 10, "amount": -50, "categoryId": "Transportation", "projectId": "New Restaurant Startup"}], "payee": "RideShare Express", "notes": "Rides for client meetings", "date": "2024-10-16", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Transportation"}, {"amount": -20, "detailsTransactions": [{"name": "Premium Tunes Subscription", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Entertainment", "projectId": null}], "payee": "Melody Stream Plus", "notes": "Monthly music subscription fee", "date": "2024-10-17", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Entertainment"}, {"amount": -60, "detailsTransactions": [{"name": "Chef Networking Gala Pass", "quantity": 1, "unitPrice": 60, "amount": -60, "categoryId": "Professional Development", "projectId": null}], "payee": "Culinary Alliance of Tokyo", "notes": "Attendance at a chef's networking event", "date": "2024-10-18", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Professional Development"}, {"amount": -45, "detailsTransactions": [{"name": "Family Brunch Delight - Breakfast Set", "quantity": 3, "unitPrice": 15, "amount": -45, "categoryId": "Dining Out", "projectId": null}], "payee": "Brunch & Mingle", "notes": "Brunch with family", "date": "2024-10-19", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}, {"amount": -20, "detailsTransactions": [{"name": "<PERSON><PERSON> - Essential Wellness", "quantity": 4, "unitPrice": 5, "amount": -20, "categoryId": "Health & Wellness", "projectId": null}], "payee": "Wellness Pharmacy", "notes": "Purchase of vitamins and supplements", "date": "2024-10-20", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Health & Wellness"}, {"amount": -25, "detailsTransactions": [{"name": "Weekly Laundry & Press Service", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Miscellaneous", "projectId": null}], "payee": "Tokyo Wash & Press", "notes": "Weekly laundry", "date": "2024-10-21", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Miscellaneous"}]}, {"categories": [{"name": "Rent", "goal": 1200}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 200}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 100}, {"name": "Entertainment", "goal": 100}, {"name": "Health & Wellness", "goal": 50}, {"name": "Savings", "goal": 800}, {"name": "Professional Development", "goal": 100}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Yamabuki Banking Group"}], "projects": [{"name": "New Restaurant Startup", "budget": 30000, "startDate": "2024-10-01", "endDate": "2025-10-01", "description": "Start and operate a new fusion cuisine restaurant."}], "transactions": [{"amount": -100, "detailsTransactions": [{"name": "Fresh Farmer's Harvest", "quantity": 10, "unitPrice": 3, "amount": -30, "categoryId": "Groceries", "projectId": null}, {"name": "Gourmet Cheese Selection", "quantity": 5, "unitPrice": 8, "amount": -40, "categoryId": "Groceries", "projectId": null}, {"name": "Artisan Bakery Goods", "quantity": 3, "unitPrice": 10, "amount": -30, "categoryId": "Groceries", "projectId": null}], "payee": "Delight Food Market", "notes": "Weekly grocery replenishment", "date": "2024-10-22", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Groceries"}, {"amount": -50, "detailsTransactions": [{"name": "Personal & Business Taxi Rides", "quantity": 5, "unitPrice": 10, "amount": -50, "categoryId": "Transportation", "projectId": null}], "payee": "CityCab Services", "notes": "Transportation for personal and business errands", "date": "2024-10-23", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Transportation"}, {"amount": -70, "detailsTransactions": [{"name": "Supplier Engagement Dinner - <PERSON>u", "quantity": 1, "unitPrice": 70, "amount": -70, "categoryId": "Dining Out", "projectId": "New Restaurant Startup"}], "payee": "Gourmet Delight", "notes": "Business dinner with suppliers", "date": "2024-10-24", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}, {"amount": -30, "detailsTransactions": [{"name": "Monthly Water Service Fee", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Utilities", "projectId": null}], "payee": "HydroFlow Services", "notes": "Monthly water bill payment", "date": "2024-10-25", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Utilities"}, {"amount": -25, "detailsTransactions": [{"name": "Complete Relaxation Spa Package", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Health & Wellness", "projectId": null}], "payee": "Zen Retreat Spa", "notes": "Weekend stress relief session", "date": "2024-10-26", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Health & Wellness"}, {"amount": -15, "detailsTransactions": [{"name": "Painting Essentials - Brushes & Colors", "quantity": 1, "unitPrice": 15, "amount": -15, "categoryId": "Miscellaneous", "projectId": null}], "payee": "Creative Expressions Store", "notes": "Buying paint and brushes", "date": "2024-10-27", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Miscellaneous"}, {"amount": -40, "detailsTransactions": [{"name": "Movie & Series Access Pass", "quantity": 1, "unitPrice": 40, "amount": -40, "categoryId": "Entertainment", "projectId": null}], "payee": "StreamFlix Plus", "notes": "Annual subscription renewal for movies and series", "date": "2024-10-28", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Entertainment"}]}, {"categories": [{"name": "Rent", "goal": 1200}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 200}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 100}, {"name": "Entertainment", "goal": 100}, {"name": "Health & Wellness", "goal": 50}, {"name": "Savings", "goal": 800}, {"name": "Professional Development", "goal": 100}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Yamabuki Banking Group"}], "projects": [{"name": "New Restaurant Startup", "budget": 30000, "startDate": "2024-10-01", "endDate": "2025-10-01", "description": "Start and operate a new fusion cuisine restaurant."}], "transactions": [{"amount": -95, "detailsTransactions": [{"name": "Organic Leafy Greens (1kg)", "quantity": 8, "unitPrice": 5, "amount": -40, "categoryId": "Groceries", "projectId": null}, {"name": "Free-range Brown Eggs (Dozen)", "quantity": 3, "unitPrice": 5, "amount": -15, "categoryId": "Groceries", "projectId": null}, {"name": "Gourmet Spice Rack Collection", "quantity": 4, "unitPrice": 10, "amount": -40, "categoryId": "Groceries", "projectId": null}], "payee": "Harvest Organic Market", "notes": "Weekly grocery shopping focusing on organic produce", "date": "2024-10-29", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Groceries"}, {"amount": -50, "detailsTransactions": [{"name": "Monthly Metro Bus Pass - City Unlimited", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Transportation", "projectId": null}], "payee": "Metro Transit Authority", "notes": "Monthly bus pass for local transportation", "date": "2024-10-30", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Transportation"}, {"amount": -1200, "detailsTransactions": [{"name": "November Apartment Rent", "quantity": 1, "unitPrice": 1200, "amount": -1200, "categoryId": "Rent", "projectId": null}], "payee": "Estate Management Inc.", "notes": "Monthly rent for November", "date": "2024-11-01", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Rent"}, {"amount": 2500, "detailsTransactions": [], "payee": "Gourmet Street - Monthly Income", "notes": "Monthly salary received", "date": "2024-11-01", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Savings"}, {"amount": -75, "detailsTransactions": [{"name": "Cutting-Edge Culinary Techniques Workshop", "quantity": 1, "unitPrice": 75, "amount": -75, "categoryId": "Professional Development", "projectId": null}], "payee": "Tokyo Culinary Innovation Center", "notes": "Participating in a professional development workshop", "date": "2024-11-02", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Professional Development"}, {"amount": -60, "detailsTransactions": [{"name": "Family Dinner Feast - Classic Set Menu", "quantity": 1, "unitPrice": 60, "amount": -60, "categoryId": "Dining Out", "projectId": null}], "payee": "Mughal Delights", "notes": "Family dinner gathering", "date": "2024-11-03", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}, {"amount": -20, "detailsTransactions": [{"name": "Weekly Gym Usage Fee - Premium Access", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Health & Wellness", "projectId": null}], "payee": "Fitness Hub Tokyo", "notes": "Weekly access fee for fitness equipment", "date": "2024-11-04", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Health & Wellness"}]}, {"categories": [{"name": "Rent", "goal": 1200}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 200}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 100}, {"name": "Entertainment", "goal": 100}, {"name": "Health & Wellness", "goal": 50}, {"name": "Savings", "goal": 800}, {"name": "Professional Development", "goal": 100}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Yamabuki Banking Group"}], "projects": [{"name": "New Restaurant Startup", "budget": 30000, "startDate": "2024-10-01", "endDate": "2025-10-01", "description": "Start and operate a new fusion cuisine restaurant."}], "transactions": [{"amount": -110, "detailsTransactions": [{"name": "Italian Pasta Variety Pack", "quantity": 10, "unitPrice": 3, "amount": -30, "categoryId": "Groceries", "projectId": null}, {"name": "Grass-Fed Beef Cuts", "quantity": 5, "unitPrice": 10, "amount": -50, "categoryId": "Groceries", "projectId": null}, {"name": "Exotic Spice Collection", "quantity": 3, "unitPrice": 10, "amount": -30, "categoryId": "Groceries", "projectId": null}], "payee": "Artisan Food Market", "notes": "Weekly grocery shopping", "date": "2024-11-05", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Groceries"}, {"amount": -40, "detailsTransactions": [{"name": "UberX Rides - Client Meetings", "quantity": 4, "unitPrice": 10, "amount": -40, "categoryId": "Transportation", "projectId": "New Restaurant Startup"}], "payee": "CityCar Services", "notes": "<PERSON><PERSON> rides for meetings", "date": "2024-11-06", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Transportation"}, {"amount": -25, "detailsTransactions": [{"name": "Documentary Film Rentals - Inspiration Series", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Entertainment", "projectId": null}], "payee": "Streamland Rentals", "notes": "Rental of documentaries for inspirational cooking ideas", "date": "2024-11-07", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Entertainment"}, {"amount": -15, "detailsTransactions": [{"name": "Late Return Fee - Culinary Books", "quantity": 1, "unitPrice": 15, "amount": -15, "categoryId": "Miscellaneous", "projectId": null}], "payee": "Tokyo City Library", "notes": "Late fee for cookbook returns", "date": "2024-11-08", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Miscellaneous"}, {"amount": -80, "detailsTransactions": [{"name": "Supplier Networking Lunch - <PERSON>", "quantity": 1, "unitPrice": 80, "amount": -80, "categoryId": "Dining Out", "projectId": "New Restaurant Startup"}], "payee": "Cuisine Culture Café", "notes": "Business lunch meeting", "date": "2024-11-09", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}, {"amount": -30, "detailsTransactions": [{"name": "<PERSON><PERSON><PERSON> - Health Kit", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Health & Wellness", "projectId": null}], "payee": "WellBeing Health Store", "notes": "Purchase of wellness supplements", "date": "2024-11-10", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Health & Wellness"}, {"amount": -35, "detailsTransactions": [{"name": "Weekly Laundry & Ironing Service", "quantity": 1, "unitPrice": 35, "amount": -35, "categoryId": "Miscellaneous", "projectId": null}], "payee": "Tokyo Clean & Press", "notes": "Weekly laundry services", "date": "2024-11-11", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Miscellaneous"}]}, {"categories": [{"name": "Rent", "goal": 1200}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 200}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 100}, {"name": "Entertainment", "goal": 100}, {"name": "Health & Wellness", "goal": 50}, {"name": "Savings", "goal": 800}, {"name": "Professional Development", "goal": 100}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Yamabuki Banking Group"}], "projects": [{"name": "New Restaurant Startup", "budget": 30000, "startDate": "2024-10-01", "endDate": "2025-10-01", "description": "Start and operate a new fusion cuisine restaurant."}], "transactions": [{"amount": -105, "detailsTransactions": [{"name": "Organic Market Fruits", "quantity": 10, "unitPrice": 3, "amount": -30, "categoryId": "Groceries", "projectId": null}, {"name": "Seasonal Veggies Bundle", "quantity": 5, "unitPrice": 3, "amount": -15, "categoryId": "Groceries", "projectId": null}, {"name": "Fresh Ocean Catch - Deluxe", "quantity": 3, "unitPrice": 20, "amount": -60, "categoryId": "Groceries", "projectId": null}], "payee": "Green Valley Market", "notes": "Weekly grocery haul including seafood", "date": "2024-11-12", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Groceries"}, {"amount": -60, "detailsTransactions": [{"name": "City Metro Monthly Pass", "quantity": 1, "unitPrice": 60, "amount": -60, "categoryId": "Transportation", "projectId": null}], "payee": "Urban Metro Services", "notes": "Monthly metro pass for city travel", "date": "2024-11-13", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Transportation"}, {"amount": -50, "detailsTransactions": [{"name": "Evening Theatre Performance Tickets", "quantity": 2, "unitPrice": 25, "amount": -50, "categoryId": "Entertainment", "projectId": null}], "payee": "Dramatic Arts Theatre", "notes": "Tickets for weekend play", "date": "2024-11-14", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Entertainment"}, {"amount": -20, "detailsTransactions": [{"name": "Home Cleaning Deluxe Package", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Miscellaneous", "projectId": null}], "payee": "Clean & Shine Services", "notes": "Weekly home cleaning service", "date": "2024-11-15", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Miscellaneous"}, {"amount": -90, "detailsTransactions": [{"name": "Gourmet Investor Dinner Set", "quantity": 1, "unitPrice": 90, "amount": -90, "categoryId": "Dining Out", "projectId": "New Restaurant Startup"}], "payee": "Gastronomy Hub", "notes": "Business dinner with potential investors", "date": "2024-11-16", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}, {"amount": -25, "detailsTransactions": [{"name": "Weekly Aquatic Exercise Fee", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Health & Wellness", "projectId": null}], "payee": "Tokyo Aquatic Center", "notes": "Weekly swimming activity", "date": "2024-11-17", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Health & Wellness"}, {"amount": -25, "detailsTransactions": [{"name": "Culinary Insights Monthly Magazine", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Professional Development", "projectId": null}], "payee": "Page Turner Bookstore", "notes": "Purchase of culinary magazine", "date": "2024-11-18", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Professional Development"}]}, {"categories": [{"name": "Rent", "goal": 1200}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 200}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 100}, {"name": "Entertainment", "goal": 100}, {"name": "Health & Wellness", "goal": 50}, {"name": "Savings", "goal": 800}, {"name": "Professional Development", "goal": 100}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Yamabuki Banking Group"}], "projects": [{"name": "New Restaurant Startup", "budget": 30000, "startDate": "2024-10-01", "endDate": "2025-10-01", "description": "Start and operate a new fusion cuisine restaurant."}], "transactions": [{"amount": -95, "detailsTransactions": [{"name": "Farm Fresh Veggie Box", "quantity": 8, "unitPrice": 4, "amount": -32, "categoryId": "Groceries", "projectId": null}, {"name": "Organic Chicken Fillets", "quantity": 5, "unitPrice": 10, "amount": -50, "categoryId": "Groceries", "projectId": null}, {"name": "Artisan Beverages Collection", "quantity": 5, "unitPrice": 2.6, "amount": -13, "categoryId": "Groceries", "projectId": null}], "payee": "Fresh Fields Market", "notes": "Weekly groceries, including organic and protein essentials", "date": "2024-11-19", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Groceries"}, {"amount": -45, "detailsTransactions": [{"name": "Monthly City Metro Card Renewal", "quantity": 1, "unitPrice": 45, "amount": -45, "categoryId": "Transportation", "projectId": null}], "payee": "City Transit Authority", "notes": "Monthly metro card renewal", "date": "2024-11-20", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Transportation"}, {"amount": -40, "detailsTransactions": [{"name": "Mindfulness & Relaxation Workshop", "quantity": 1, "unitPrice": 40, "amount": -40, "categoryId": "Health & Wellness", "projectId": null}], "payee": "Serenity Wellness Center", "notes": "Participation in a one-day wellness retreat", "date": "2024-11-21", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Health & Wellness"}, {"amount": -30, "detailsTransactions": [{"name": "Monthly Electric Utility Payment", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Utilities", "projectId": null}], "payee": "Tokyo Electric Power Co.", "notes": "Monthly electric bill", "date": "2024-11-22", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Utilities"}, {"amount": -85, "detailsTransactions": [{"name": "Family Dinner Celebration - Complete Feast", "quantity": 1, "unitPrice": 85, "amount": -85, "categoryId": "Dining Out", "projectId": null}], "payee": "Home-Style Dining Hub", "notes": "Weekend family dinner outing", "date": "2024-11-23", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}, {"amount": -20, "detailsTransactions": [{"name": "Annual Library Membership Fee", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Miscellaneous", "projectId": null}], "payee": "Central Library Association", "notes": "Yearly membership renewal", "date": "2024-11-24", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Miscellaneous"}, {"amount": -60, "detailsTransactions": [{"name": "Advanced Culinary Mastery Program", "quantity": 1, "unitPrice": 60, "amount": -60, "categoryId": "Professional Development", "projectId": null}], "payee": "Global Culinary Institute", "notes": "Enrollment in advanced cooking techniques course", "date": "2024-11-25", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Professional Development"}]}, {"categories": [{"name": "Rent", "goal": 1200}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 200}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 100}, {"name": "Entertainment", "goal": 100}, {"name": "Health & Wellness", "goal": 50}, {"name": "Savings", "goal": 800}, {"name": "Professional Development", "goal": 100}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Yamabuki Banking Group"}], "projects": [{"name": "New Restaurant Startup", "budget": 30000, "startDate": "2024-10-01", "endDate": "2025-10-01", "description": "Start and operate a new fusion cuisine restaurant."}], "transactions": [{"amount": -98, "detailsTransactions": [{"name": "Organic Veggie Medley", "quantity": 10, "unitPrice": 4, "amount": -40, "categoryId": "Groceries", "projectId": null}, {"name": "Wild Caught Salmon Steaks", "quantity": 4, "unitPrice": 12, "amount": -48, "categoryId": "Groceries", "projectId": null}, {"name": "Culinary Fresh Herbs Pack", "quantity": 5, "unitPrice": 2, "amount": -10, "categoryId": "Groceries", "projectId": null}], "payee": "Harvest Harmony Co-op", "notes": "Weekly grocery shopping for fresh and organic products", "date": "2024-11-26", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Groceries"}, {"amount": -80, "detailsTransactions": [{"name": "Rideshare Services for Meetings", "quantity": 8, "unitPrice": 10, "amount": -80, "categoryId": "Transportation", "projectId": "New Restaurant Startup"}], "payee": "Urban Transit Solutions", "notes": "Rides to various meetings and events", "date": "2024-11-27", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Transportation"}, {"amount": -25, "detailsTransactions": [{"name": "New Movie Release - Streaming Rental", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Entertainment", "projectId": null}], "payee": "Flicks Online", "notes": "Rental of new release movies", "date": "2024-11-28", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Entertainment"}, {"amount": -30, "detailsTransactions": [{"name": "Natural Gas Utility Fee", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Utilities", "projectId": null}], "payee": "Tokyo Gas Company", "notes": "Monthly gas bill payment", "date": "2024-11-29", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Utilities"}, {"amount": -75, "detailsTransactions": [{"name": "Friendsgiving Feast", "quantity": 1, "unitPrice": 75, "amount": -75, "categoryId": "Dining Out", "projectId": null}], "payee": "Warm Hearth Bistro", "notes": "Special dinner with friends for Friendsgiving", "date": "2024-11-30", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}, {"amount": -20, "detailsTransactions": [{"name": "Weekly Gym Access Pass", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Health & Wellness", "projectId": null}], "payee": "Vitality Gym", "notes": "Weekly gym session fee", "date": "2024-12-01", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Health & Wellness"}, {"amount": -1200, "detailsTransactions": [{"name": "Monthly Rent Payment for December", "quantity": 1, "unitPrice": 1200, "amount": -1200, "categoryId": "Rent", "projectId": null}], "payee": "Estate Management Services", "notes": "Monthly rent for December", "date": "2024-12-02", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Rent"}, {"amount": 2500, "detailsTransactions": [], "payee": "Gastronomy Street - November Salary", "notes": "Monthly salary received for November", "date": "2024-12-02", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Savings"}]}, {"categories": [{"name": "Rent", "goal": 1200}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 200}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 100}, {"name": "Entertainment", "goal": 100}, {"name": "Health & Wellness", "goal": 50}, {"name": "Savings", "goal": 800}, {"name": "Professional Development", "goal": 100}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Yamabuki Banking Group"}], "projects": [{"name": "New Restaurant Startup", "budget": 30000, "startDate": "2024-10-01", "endDate": "2025-10-01", "description": "Start and operate a new fusion cuisine restaurant."}], "transactions": [{"amount": -110, "detailsTransactions": [{"name": "Seasonal Winter Greens", "quantity": 12, "unitPrice": 3, "amount": -36, "categoryId": "Groceries", "projectId": null}, {"name": "Organic Free-Range Chicken", "quantity": 5, "unitPrice": 12, "amount": -60, "categoryId": "Groceries", "projectId": null}, {"name": "Nut & Grain Mix", "quantity": 7, "unitPrice": 2, "amount": -14, "categoryId": "Groceries", "projectId": null}], "payee": "Farm Fresh Mart", "notes": "Weekly grocery shopping for the winter season", "date": "2024-12-03", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Groceries"}, {"amount": -50, "detailsTransactions": [{"name": "Business Travel Taxi Rides", "quantity": 5, "unitPrice": 10, "amount": -50, "categoryId": "Transportation", "projectId": "New Restaurant Startup"}], "payee": "Executive Taxi Services", "notes": "Transportation for business meetings", "date": "2024-12-04", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Transportation"}, {"amount": -20, "detailsTransactions": [{"name": "Weekly Mindful Meditation Session", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Health & Wellness", "projectId": null}], "payee": "Zen Retreat Center", "notes": "Weekly restorative meditation class", "date": "2024-12-05", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Health & Wellness"}, {"amount": -40, "detailsTransactions": [{"name": "Monthly High-Speed Internet Service", "quantity": 1, "unitPrice": 40, "amount": -40, "categoryId": "Utilities", "projectId": null}], "payee": "Tokyo Online Connect", "notes": "Monthly internet bill payment", "date": "2024-12-06", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Utilities"}, {"amount": -50, "detailsTransactions": [{"name": "Gourmet Client Dinner Package", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Dining Out", "projectId": "New Restaurant Startup"}], "payee": "Steakhouse Grille", "notes": "Business dinner with a potential client", "date": "2024-12-07", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}, {"amount": -28, "detailsTransactions": [{"name": "Holiday Decor Art Set", "quantity": 5, "unitPrice": 5, "amount": -25, "categoryId": "Miscellaneous", "projectId": null}, {"name": "Festive Stationery Pack", "quantity": 3, "unitPrice": 1, "amount": -3, "categoryId": "Miscellaneous", "projectId": null}], "payee": "Craft Haven", "notes": "Purchase of supplies for holiday decoration", "date": "2024-12-08", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Miscellaneous"}, {"amount": -35, "detailsTransactions": [{"name": "Film Buff Festival Pass", "quantity": 1, "unitPrice": 35, "amount": -35, "categoryId": "Entertainment", "projectId": null}], "payee": "Downtown Cinema", "notes": "Tickets for independent film screenings", "date": "2024-12-09", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Entertainment"}]}, {"categories": [{"name": "Rent", "goal": 1200}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 200}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 100}, {"name": "Entertainment", "goal": 100}, {"name": "Health & Wellness", "goal": 50}, {"name": "Savings", "goal": 800}, {"name": "Professional Development", "goal": 100}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Yamabuki Banking Group"}], "projects": [{"name": "New Restaurant Startup", "budget": 30000, "startDate": "2024-10-01", "endDate": "2025-10-01", "description": "Start and operate a new fusion cuisine restaurant."}], "transactions": [{"amount": -100, "detailsTransactions": [{"name": "Winter Root Vegetables", "quantity": 10, "unitPrice": 4, "amount": -40, "categoryId": "Groceries", "projectId": null}, {"name": "Organic Dairy Selection", "quantity": 5, "unitPrice": 8, "amount": -40, "categoryId": "Groceries", "projectId": null}, {"name": "Citrus Fruits Assortment", "quantity": 5, "unitPrice": 4, "amount": -20, "categoryId": "Groceries", "projectId": null}], "payee": "Health Food Market", "notes": "Weekly groceries including organic and seasonal items", "date": "2024-12-10", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Groceries"}, {"amount": -60, "detailsTransactions": [{"name": "Unlimited Rideshare Monthly Subscription", "quantity": 1, "unitPrice": 60, "amount": -60, "categoryId": "Transportation", "projectId": null}], "payee": "RideShare App", "notes": "Monthly subscription for unlimited rides", "date": "2024-12-11", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Transportation"}, {"amount": -30, "detailsTransactions": [{"name": "Yoga Studio Unlimited Membership", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Health & Wellness", "projectId": null}], "payee": "Tranquil Yoga Studio", "notes": "Membership fee for unlimited weekly classes", "date": "2024-12-12", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Health & Wellness"}, {"amount": -30, "detailsTransactions": [{"name": "Premium Content Stream Subscription", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Entertainment", "projectId": null}], "payee": "StreamVision+", "notes": "Monthly subscription for premium content", "date": "2024-12-13", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Entertainment"}, {"amount": -95, "detailsTransactions": [{"name": "Investor Evening & Dinner Package", "quantity": 1, "unitPrice": 95, "amount": -95, "categoryId": "Dining Out", "projectId": "New Restaurant Startup"}], "payee": "Indulgence Gourmet Restaurant", "notes": "Dinner with business investors", "date": "2024-12-14", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}, {"amount": -15, "detailsTransactions": [{"name": "Pet Food & Essentials", "quantity": 1, "unitPrice": 15, "amount": -15, "categoryId": "Miscellaneous", "projectId": null}], "payee": "Pet Haven Supply Store", "notes": "Purchase of pet food and supplies", "date": "2024-12-15", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Miscellaneous"}, {"amount": -50, "detailsTransactions": [{"name": "Advanced Cooking Seminar Ticket", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Professional Development", "projectId": null}], "payee": "Chef's Academy", "notes": "Participation in a cooking skills seminar", "date": "2024-12-16", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Professional Development"}]}, {"categories": [{"name": "Rent", "goal": 1200}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 200}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 100}, {"name": "Entertainment", "goal": 100}, {"name": "Health & Wellness", "goal": 50}, {"name": "Savings", "goal": 800}, {"name": "Professional Development", "goal": 100}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Yamabuki Banking Group"}], "projects": [{"name": "New Restaurant Startup", "budget": 30000, "startDate": "2024-10-01", "endDate": "2025-10-01", "description": "Start and operate a new fusion cuisine restaurant."}], "transactions": [{"amount": -90, "detailsTransactions": [{"name": "Farm Fresh Winter Greens", "quantity": 12, "unitPrice": 3, "amount": -36, "categoryId": "Groceries", "projectId": null}, {"name": "Artisan Cheese Variety", "quantity": 4, "unitPrice": 8, "amount": -32, "categoryId": "Groceries", "projectId": null}, {"name": "Sourdough Bread Loaves", "quantity": 3, "unitPrice": 7, "amount": -21, "categoryId": "Groceries", "projectId": null}], "payee": "Gourmet Grocery", "notes": "Weekly grocery shopping with emphasis on fresh produce", "date": "2024-12-17", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Groceries"}, {"amount": -70, "detailsTransactions": [{"name": "Annual Metro Transit Pass", "quantity": 1, "unitPrice": 70, "amount": -70, "categoryId": "Transportation", "projectId": null}], "payee": "City Transit Authority", "notes": "Annual public transportation pass renewal", "date": "2024-12-18", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Transportation"}, {"amount": -40, "detailsTransactions": [{"name": "Monthly Water Utility Fee", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Utilities", "projectId": null}, {"name": "Monthly Waste Management Fee", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Utilities", "projectId": null}], "payee": "City Utilities", "notes": "Monthly cost for water and trash services", "date": "2024-12-19", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Utilities"}, {"amount": -35, "detailsTransactions": [{"name": "Ad-Free Streaming Music Subscription", "quantity": 1, "unitPrice": 35, "amount": -35, "categoryId": "Entertainment", "projectId": null}], "payee": "Harmony Music Service", "notes": "Monthly subscription renewal for ad-free listening", "date": "2024-12-20", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Entertainment"}, {"amount": -100, "detailsTransactions": [{"name": "Festive Dinner Gathering - Family Feast", "quantity": 1, "unitPrice": 100, "amount": -100, "categoryId": "Dining Out", "projectId": null}], "payee": "Holiday Hearth Restaurant", "notes": "Celebratory holiday dinner with family", "date": "2024-12-21", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}, {"amount": -50, "detailsTransactions": [{"name": "Winter Dance Lesson Series", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Health & Wellness", "projectId": null}], "payee": "Elegant Movements Dance Studio", "notes": "Winter class registration for weekly lessons", "date": "2024-12-22", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Health & Wellness"}, {"amount": -20, "detailsTransactions": [{"name": "Community Support Donation", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Miscellaneous", "projectId": null}], "payee": "Hope Collective Charity", "notes": "Year-end donation for community support", "date": "2024-12-23", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Miscellaneous"}]}, {"categories": [{"name": "Rent", "goal": 1200}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 200}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 100}, {"name": "Entertainment", "goal": 100}, {"name": "Health & Wellness", "goal": 50}, {"name": "Savings", "goal": 800}, {"name": "Professional Development", "goal": 100}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Yamabuki Banking Group"}], "projects": [{"name": "New Restaurant Startup", "budget": 30000, "startDate": "2024-10-01", "endDate": "2025-10-01", "description": "Start and operate a new fusion cuisine restaurant."}], "transactions": [{"amount": -85, "detailsTransactions": [{"name": "Festive Fruit Assortment", "quantity": 8, "unitPrice": 5, "amount": -40, "categoryId": "Groceries", "projectId": null}, {"name": "Gourmet Winter Pastries", "quantity": 3, "unitPrice": 15, "amount": -45, "categoryId": "Groceries", "projectId": null}], "payee": "Holiday Market", "notes": "Purchase of ingredients and treats for holiday season", "date": "2024-12-24", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Groceries"}, {"amount": -40, "detailsTransactions": [{"name": "Holiday Ride Services", "quantity": 4, "unitPrice": 10, "amount": -40, "categoryId": "Transportation", "projectId": null}], "payee": "Festival Ride-share", "notes": "Transportation for holiday errands and visits", "date": "2024-12-25", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Transportation"}, {"amount": -60, "detailsTransactions": [{"name": "Winter Symphony Concert Tickets", "quantity": 2, "unitPrice": 30, "amount": -60, "categoryId": "Entertainment", "projectId": null}], "payee": "Symphony Hall", "notes": "Holiday concert with family", "date": "2024-12-26", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Entertainment"}, {"amount": -45, "detailsTransactions": [{"name": "Monthly Mobile Service Fee", "quantity": 1, "unitPrice": 45, "amount": -45, "categoryId": "Utilities", "projectId": null}], "payee": "Mobile Network Provider", "notes": "Payment of monthly mobile phone bill", "date": "2024-12-27", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Utilities"}, {"amount": -70, "detailsTransactions": [{"name": "Year-End Dinner Reservation", "quantity": 1, "unitPrice": 70, "amount": -70, "categoryId": "Dining Out", "projectId": null}], "payee": "<PERSON>egant Suppers", "notes": "Pre-booked a special dinner to celebrate the end of the year", "date": "2024-12-28", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}, {"amount": -30, "detailsTransactions": [{"name": "Monthly Gym Membership - January", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Health & Wellness", "projectId": null}], "payee": "Vitality Gym", "notes": "One month gym membership renewal", "date": "2024-12-29", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Health & Wellness"}, {"amount": -50, "detailsTransactions": [{"name": "Monthly Business Progress Consultation", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Professional Development", "projectId": "New Restaurant Startup"}], "payee": "Strategic Consulting Group", "notes": "Consultation meeting to review business progress", "date": "2024-12-30", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Professional Development"}]}, {"categories": [{"name": "Rent", "goal": 1200}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 200}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 100}, {"name": "Entertainment", "goal": 100}, {"name": "Health & Wellness", "goal": 50}, {"name": "Savings", "goal": 800}, {"name": "Professional Development", "goal": 100}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Yamabuki Banking Group"}], "projects": [{"name": "New Restaurant Startup", "budget": 30000, "startDate": "2024-10-01", "endDate": "2025-10-01", "description": "Start and operate a new fusion cuisine restaurant."}], "transactions": [{"amount": -95, "detailsTransactions": [{"name": "Holiday Gourmet Ingredients", "quantity": 10, "unitPrice": 6, "amount": -60, "categoryId": "Groceries", "projectId": null}, {"name": "Sparkling Champagne Bottles", "quantity": 2, "unitPrice": 17.5, "amount": -35, "categoryId": "Groceries", "projectId": null}], "payee": "New Year Supermarket", "notes": "Shopping for New Year's celebration", "date": "2024-12-31", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Groceries"}, {"amount": -80, "detailsTransactions": [{"name": "2025 City Transit Annual Pass", "quantity": 1, "unitPrice": 80, "amount": -80, "categoryId": "Transportation", "projectId": null}], "payee": "Metro City Transport", "notes": "Annual transit pass renewal", "date": "2025-01-01", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Transportation"}, {"amount": -1200, "detailsTransactions": [{"name": "January Rent - Apartment 5A", "quantity": 1, "unitPrice": 1200, "amount": -1200, "categoryId": "Rent", "projectId": null}], "payee": "Cityscape Realty Management", "notes": "Monthly rent payment for January", "date": "2025-01-02", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Rent"}, {"amount": 2500, "detailsTransactions": [], "payee": "Gastronomy Street - December Salary", "notes": "Monthly salary received for December", "date": "2025-01-02", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Savings"}, {"amount": -25, "detailsTransactions": [{"name": "Weekly Yoga Pass Renew - January", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Health & Wellness", "projectId": null}], "payee": "Zen Oasis Yoga Center", "notes": "Monthly fees for yoga classes", "date": "2025-01-03", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Health & Wellness"}, {"amount": -55, "detailsTransactions": [{"name": "New Year Dinner with Friends", "quantity": 1, "unitPrice": 55, "amount": -55, "categoryId": "Dining Out", "projectId": null}], "payee": "Urban Chic Bistro", "notes": "First weekend dinner out with friends of the year", "date": "2025-01-04", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}, {"amount": -35, "detailsTransactions": [{"name": "Executive <PERSON><PERSON>", "quantity": 1, "unitPrice": 12, "amount": -12, "categoryId": "Miscellaneous", "projectId": null}, {"name": "Organizational Planner 2025", "quantity": 1, "unitPrice": 23, "amount": -23, "categoryId": "Miscellaneous", "projectId": null}], "payee": "The Stationery Boutique", "notes": "Purchase of new year agenda and planner", "date": "2025-01-05", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Miscellaneous"}, {"amount": -45, "detailsTransactions": [{"name": "Advanced Business Strategy Course", "quantity": 1, "unitPrice": 45, "amount": -45, "categoryId": "Professional Development", "projectId": "New Restaurant Startup"}], "payee": "SkillEdge Online Learning", "notes": "Subscription to a new business strategy course", "date": "2025-01-06", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Professional Development"}]}, {"categories": [{"name": "Rent", "goal": 1200}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 200}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 100}, {"name": "Entertainment", "goal": 100}, {"name": "Health & Wellness", "goal": 50}, {"name": "Savings", "goal": 800}, {"name": "Professional Development", "goal": 100}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Yamabuki Banking Group"}], "projects": [{"name": "New Restaurant Startup", "budget": 30000, "startDate": "2024-10-01", "endDate": "2025-10-01", "description": "Start and operate a new fusion cuisine restaurant."}], "transactions": [{"amount": -88, "detailsTransactions": [{"name": "Organic Winter Roots", "quantity": 8, "unitPrice": 4, "amount": -32, "categoryId": "Groceries", "projectId": null}, {"name": "Lean Cuts - Premium Selection", "quantity": 4, "unitPrice": 10, "amount": -40, "categoryId": "Groceries", "projectId": null}, {"name": "Hearty Grain Mixes", "quantity": 4, "unitPrice": 4, "amount": -16, "categoryId": "Groceries", "projectId": null}], "payee": "Local Market", "notes": "Weekly grocery shopping with a focus on healthy ingredients", "date": "2025-01-07", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Groceries"}, {"amount": -40, "detailsTransactions": [{"name": "Weekly Meeting Transport Services", "quantity": 4, "unitPrice": 10, "amount": -40, "categoryId": "Transportation", "projectId": "New Restaurant Startup"}], "payee": "City Taxi Service", "notes": "Transport to and from meetings this week", "date": "2025-01-08", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Transportation"}, {"amount": -45, "detailsTransactions": [{"name": "Comprehensive Yoga & Pilates Enrollment", "quantity": 1, "unitPrice": 45, "amount": -45, "categoryId": "Health & Wellness", "projectId": null}], "payee": "Yoga & Wellness Studio", "notes": "Monthly fees for continuing yoga and pilates", "date": "2025-01-09", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Health & Wellness"}, {"amount": -30, "detailsTransactions": [{"name": "Monthly Water Utility Payment", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Utilities", "projectId": null}], "payee": "City Water Services", "notes": "Monthly payment for water bills", "date": "2025-01-10", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Utilities"}, {"amount": -50, "detailsTransactions": [{"name": "Team Dinner - Strategic Planning", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Dining Out", "projectId": "New Restaurant Startup"}], "payee": "Fusion Eatery", "notes": "Business strategy discussion over dinner", "date": "2025-01-11", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}, {"amount": -25, "detailsTransactions": [{"name": "Weekend Streaming Film Rentals", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Entertainment", "projectId": null}], "payee": "StreamFlix Rentals", "notes": "Streaming movie rentals for the weekend", "date": "2025-01-12", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Entertainment"}, {"amount": -20, "detailsTransactions": [{"name": "Weekly Dry Cleaning Services", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Miscellaneous", "projectId": null}], "payee": "Clean & Fresh Dry Cleaners", "notes": "Weekly dry cleaning services", "date": "2025-01-13", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Miscellaneous"}]}, {"categories": [{"name": "Rent", "goal": 1200}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 200}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 100}, {"name": "Entertainment", "goal": 100}, {"name": "Health & Wellness", "goal": 50}, {"name": "Savings", "goal": 800}, {"name": "Professional Development", "goal": 100}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Yamabuki Banking Group"}], "projects": [{"name": "New Restaurant Startup", "budget": 30000, "startDate": "2024-10-01", "endDate": "2025-10-01", "description": "Start and operate a new fusion cuisine restaurant."}], "transactions": [{"amount": -92, "detailsTransactions": [{"name": "Market-Fresh Vegetables", "quantity": 10, "unitPrice": 4, "amount": -40, "categoryId": "Groceries", "projectId": null}, {"name": "Free-Range Organic Chicken", "quantity": 4, "unitPrice": 10, "amount": -40, "categoryId": "Groceries", "projectId": null}, {"name": "Aromatic Herbs & Spices", "quantity": 3, "unitPrice": 4, "amount": -12, "categoryId": "Groceries", "projectId": null}], "payee": "Farmers Market", "notes": "Weekly groceries focused on fresh and organic produce", "date": "2025-01-14", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Groceries"}, {"amount": -35, "detailsTransactions": [{"name": "Weekly City Bus Commute", "quantity": 1, "unitPrice": 35, "amount": -35, "categoryId": "Transportation", "projectId": null}], "payee": "City Bus Service", "notes": "Transportation for city commutes", "date": "2025-01-15", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Transportation"}, {"amount": -50, "detailsTransactions": [{"name": "Canvas & Brushes Set", "quantity": 6, "unitPrice": 8.33, "amount": -50, "categoryId": "Miscellaneous", "projectId": null}], "payee": "Art Supply Haven", "notes": "Purchase of supplies for personal art project", "date": "2025-01-16", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Miscellaneous"}, {"amount": -35, "detailsTransactions": [{"name": "Monthly Electrical Services", "quantity": 1, "unitPrice": 35, "amount": -35, "categoryId": "Utilities", "projectId": null}], "payee": "PowerGrid Electric", "notes": "Monthly electricity bill payment", "date": "2025-01-17", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Utilities"}, {"amount": -75, "detailsTransactions": [{"name": "Partner Collaboration Dinner", "quantity": 1, "unitPrice": 75, "amount": -75, "categoryId": "Dining Out", "projectId": "New Restaurant Startup"}], "payee": "Prime Steakhouse", "notes": "Business dinner with partners to discuss collaboration", "date": "2025-01-18", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}, {"amount": -25, "detailsTransactions": [{"name": "Weekend Cinema Experience", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Entertainment", "projectId": null}], "payee": "Cinema Complex", "notes": "Ticket purchase for a weekend film", "date": "2025-01-19", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Entertainment"}, {"amount": -45, "detailsTransactions": [{"name": "Morning Business Insights Meeting", "quantity": 1, "unitPrice": 45, "amount": -45, "categoryId": "Professional Development", "projectId": "New Restaurant Startup"}], "payee": "Cafe Central", "notes": "Morning meeting with a mentor for business insights", "date": "2025-01-20", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Professional Development"}]}, {"categories": [{"name": "Rent", "goal": 1200}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 200}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 100}, {"name": "Entertainment", "goal": 100}, {"name": "Health & Wellness", "goal": 50}, {"name": "Savings", "goal": 800}, {"name": "Professional Development", "goal": 100}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Yamabuki Banking Group"}], "projects": [{"name": "New Restaurant Startup", "budget": 30000, "startDate": "2024-10-01", "endDate": "2025-10-01", "description": "Start and operate a new fusion cuisine restaurant."}], "transactions": [{"amount": -90, "detailsTransactions": [{"name": "Organic Fruit Basket", "quantity": 9, "unitPrice": 5, "amount": -45, "categoryId": "Groceries", "projectId": null}, {"name": "Whole Grain Pasta", "quantity": 5, "unitPrice": 3, "amount": -15, "categoryId": "Groceries", "projectId": null}, {"name": "Artisan Dairy Selection", "quantity": 4, "unitPrice": 7.5, "amount": -30, "categoryId": "Groceries", "projectId": null}], "payee": "Nature's Harvest Market", "notes": "Weekly grocery shopping focusing on healthy options", "date": "2025-01-21", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Groceries"}, {"amount": -40, "detailsTransactions": [{"name": "Monthly Subway System Pass", "quantity": 1, "unitPrice": 40, "amount": -40, "categoryId": "Transportation", "projectId": null}], "payee": "Urban Transit Services", "notes": "Monthly pass for city subway system", "date": "2025-01-22", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Transportation"}, {"amount": -20, "detailsTransactions": [{"name": "Collection of Second-Hand Books", "quantity": 10, "unitPrice": 2, "amount": -20, "categoryId": "Miscellaneous", "projectId": null}], "payee": "City Library Book Sale", "notes": "Purchase of second-hand books for home library", "date": "2025-01-23", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Miscellaneous"}, {"amount": -30, "detailsTransactions": [{"name": "Monthly Gas Service Payment", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Utilities", "projectId": null}], "payee": "Natural Gas Utility Co.", "notes": "Monthly gas utility bill", "date": "2025-01-24", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Utilities"}, {"amount": -60, "detailsTransactions": [{"name": "Family & Friends Dinner Night", "quantity": 1, "unitPrice": 60, "amount": -60, "categoryId": "Dining Out", "projectId": null}], "payee": "<PERSON><PERSON>", "notes": "Dinner with family and friends", "date": "2025-01-25", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}, {"amount": -30, "detailsTransactions": [{"name": "Monthly Pilates Class Access", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Health & Wellness", "projectId": null}], "payee": "Zen Fitness Studio", "notes": "Continued monthly access to pilates classes", "date": "2025-01-26", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Health & Wellness"}, {"amount": -55, "detailsTransactions": [{"name": "Advanced Leadership Skills Workshop", "quantity": 1, "unitPrice": 55, "amount": -55, "categoryId": "Professional Development", "projectId": "New Restaurant Startup"}], "payee": "Elite Business Institute", "notes": "Workshop participation to enhance leadership skills", "date": "2025-01-27", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Professional Development"}]}, {"categories": [{"name": "Rent", "goal": 1200}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 200}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 100}, {"name": "Entertainment", "goal": 100}, {"name": "Health & Wellness", "goal": 50}, {"name": "Savings", "goal": 800}, {"name": "Professional Development", "goal": 100}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Yamabuki Banking Group"}], "projects": [{"name": "New Restaurant Startup", "budget": 30000, "startDate": "2024-10-01", "endDate": "2025-10-01", "description": "Start and operate a new fusion cuisine restaurant."}], "transactions": [{"amount": -80, "detailsTransactions": [{"name": "Farm-Fresh Vegetables", "quantity": 10, "unitPrice": 4, "amount": -40, "categoryId": "Groceries", "projectId": null}, {"name": "Multigrain Bread Loaves", "quantity": 5, "unitPrice": 4, "amount": -20, "categoryId": "Groceries", "projectId": null}, {"name": "Free-Range Organic Eggs", "quantity": 3, "unitPrice": 6.67, "amount": -20, "categoryId": "Groceries", "projectId": null}], "payee": "Local Grocery Hub", "notes": "Weekly grocery shopping for everyday essentials", "date": "2025-01-28", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Groceries"}, {"amount": -45, "detailsTransactions": [{"name": "Work Meeting Taxi Trips", "quantity": 3, "unitPrice": 15, "amount": -45, "categoryId": "Transportation", "projectId": "New Restaurant Startup"}], "payee": "Gold Line Taxi Service", "notes": "Taxis to work meetings this week", "date": "2025-01-29", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Transportation"}, {"amount": -15, "detailsTransactions": [{"name": "Film Enthusiast Ticket", "quantity": 1, "unitPrice": 15, "amount": -15, "categoryId": "Entertainment", "projectId": null}], "payee": "Cinema Royal", "notes": "Entry to a film screening on leisure day", "date": "2025-01-30", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Entertainment"}, {"amount": -1200, "detailsTransactions": [{"name": "Rental Payment for February", "quantity": 1, "unitPrice": 1200, "amount": -1200, "categoryId": "Rent", "projectId": null}], "payee": "Citywide Realty Services", "notes": "Rent payment for February", "date": "2025-02-01", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Rent"}, {"amount": 2500, "detailsTransactions": [], "payee": "Culinary Stars - <PERSON> <PERSON><PERSON>", "notes": "Monthly salary received", "date": "2025-02-01", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Savings"}, {"amount": -35, "detailsTransactions": [{"name": "Holistic Yoga Package", "quantity": 1, "unitPrice": 35, "amount": -35, "categoryId": "Health & Wellness", "projectId": null}], "payee": "Inner Peace Yoga Center", "notes": "Monthly session package to maintain routine", "date": "2025-02-02", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Health & Wellness"}, {"amount": -65, "detailsTransactions": [{"name": "High-Level Project Discussion Luncheon", "quantity": 1, "unitPrice": 65, "amount": -65, "categoryId": "Dining Out", "projectId": "New Restaurant Startup"}], "payee": "Top Executive Lounge", "notes": "Business lunch for project discussions", "date": "2025-02-03", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}]}, {"categories": [{"name": "Rent", "goal": 1200}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 200}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 100}, {"name": "Entertainment", "goal": 100}, {"name": "Health & Wellness", "goal": 50}, {"name": "Savings", "goal": 800}, {"name": "Professional Development", "goal": 100}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Yamabuki Banking Group"}], "projects": [{"name": "New Restaurant Startup", "budget": 30000, "startDate": "2024-10-01", "endDate": "2025-10-01", "description": "Start and operate a new fusion cuisine restaurant."}], "transactions": [{"amount": -85, "detailsTransactions": [{"name": "Winter Harvest Veggie Collection", "quantity": 12, "unitPrice": 4, "amount": -48, "categoryId": "Groceries", "projectId": null}, {"name": "Grass-Fed Lean Beef Portions", "quantity": 4, "unitPrice": 10, "amount": -40, "categoryId": "Groceries", "projectId": null}], "payee": "Farm Fresh Market", "notes": "Weekly groceries focused on seasonal items", "date": "2025-02-04", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Groceries"}, {"amount": -50, "detailsTransactions": [{"name": "City Commuter Card Recharge", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Transportation", "projectId": null}], "payee": "Metro Secure Services", "notes": "Top-up for city transport card", "date": "2025-02-05", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Transportation"}, {"amount": -30, "detailsTransactions": [{"name": "Live Jazz Night Tickets", "quantity": 2, "unitPrice": 15, "amount": -30, "categoryId": "Entertainment", "projectId": null}], "payee": "Jazz Haven Hall", "notes": "Jazz night with friends", "date": "2025-02-06", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Entertainment"}, {"amount": -25, "detailsTransactions": [{"name": "Coffee Rendezvous with Collaboration Prospect", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Dining Out", "projectId": "New Restaurant Startup"}], "payee": "Corner Café", "notes": "Coffee meet with potential collaborator", "date": "2025-02-07", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}, {"amount": -40, "detailsTransactions": [{"name": "Water Service Utility Payment", "quantity": 1, "unitPrice": 40, "amount": -40, "categoryId": "Utilities", "projectId": null}], "payee": "BlueFlow Water Services", "notes": "Monthly water bill due", "date": "2025-02-08", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Utilities"}, {"amount": -20, "detailsTransactions": [{"name": "Weekend Fitness Access Pass", "quantity": 4, "unitPrice": 5, "amount": -20, "categoryId": "Health & Wellness", "projectId": null}], "payee": "Energize Fitness Club", "notes": "Day passes for weekend workouts", "date": "2025-02-09", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Health & Wellness"}, {"amount": -45, "detailsTransactions": [{"name": "Industry Networking Dinner", "quantity": 1, "unitPrice": 45, "amount": -45, "categoryId": "Dining Out", "projectId": "New Restaurant Startup"}], "payee": "Metropolitan Grill", "notes": "Dinner meeting with industry colleague", "date": "2025-02-10", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}]}, {"categories": [{"name": "Rent", "goal": 1200}, {"name": "Groceries", "goal": 400}, {"name": "Dining Out", "goal": 200}, {"name": "Transportation", "goal": 150}, {"name": "Utilities", "goal": 100}, {"name": "Entertainment", "goal": 100}, {"name": "Health & Wellness", "goal": 50}, {"name": "Savings", "goal": 800}, {"name": "Professional Development", "goal": 100}, {"name": "Miscellaneous", "goal": 50}], "accounts": [{"name": "Yamabuki Banking Group"}], "projects": [{"name": "New Restaurant Startup", "budget": 30000, "startDate": "2024-10-01", "endDate": "2025-10-01", "description": "Start and operate a new fusion cuisine restaurant."}], "transactions": [{"amount": -95, "detailsTransactions": [{"name": "Organic Produce Mix", "quantity": 9, "unitPrice": 5, "amount": -45, "categoryId": "Groceries", "projectId": null}, {"name": "Fresh Atlantic Salmon", "quantity": 5, "unitPrice": 10, "amount": -50, "categoryId": "Groceries", "projectId": null}], "payee": "Whole Foods Market", "notes": "Weekly grocery shopping with a focus on fresh fish", "date": "2025-02-11", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Groceries"}, {"amount": -30, "detailsTransactions": [{"name": "Monthly Subway Card Reload", "quantity": 1, "unitPrice": 30, "amount": -30, "categoryId": "Transportation", "projectId": null}], "payee": "City Subway Authority", "notes": "Monthly subway card renewal", "date": "2025-02-12", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Transportation"}, {"amount": -20, "detailsTransactions": [{"name": "Annual Library Membership Fee", "quantity": 1, "unitPrice": 20, "amount": -20, "categoryId": "Miscellaneous", "projectId": null}], "payee": "Public Library Network", "notes": "Annual library membership renewal", "date": "2025-02-13", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Miscellaneous"}, {"amount": -25, "detailsTransactions": [{"name": "Monthly Electric Service Payment", "quantity": 1, "unitPrice": 25, "amount": -25, "categoryId": "Utilities", "projectId": null}], "payee": "Electric Company", "notes": "Monthly electricity bill payment", "date": "2025-02-14", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Utilities"}, {"amount": -85, "detailsTransactions": [{"name": "Valentine's Day Romantic Dinner", "quantity": 1, "unitPrice": 85, "amount": -85, "categoryId": "Dining Out", "projectId": null}], "payee": "Romantic Bistro", "notes": "Special dinner date for Valentine's Day", "date": "2025-02-14", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Dining Out"}, {"amount": -50, "detailsTransactions": [{"name": "Complete Spa Relaxation Package", "quantity": 1, "unitPrice": 50, "amount": -50, "categoryId": "Health & Wellness", "projectId": null}], "payee": "Zen Retreat Spa", "notes": "Relaxation and wellness package", "date": "2025-02-15", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Health & Wellness"}, {"amount": -40, "detailsTransactions": [{"name": "Online Marketing Masterclass Enrollment", "quantity": 1, "unitPrice": 40, "amount": -40, "categoryId": "Professional Development", "projectId": "New Restaurant Startup"}], "payee": "Online Learning Hub", "notes": "Enrollment in an online marketing course", "date": "2025-02-16", "projectId": "New Restaurant Startup", "accountId": "Yamabuki Banking Group", "categoryId": "Professional Development"}, {"amount": -35, "detailsTransactions": [{"name": "Cinema Snack Combo for Family Night", "quantity": 1, "unitPrice": 35, "amount": -35, "categoryId": "Entertainment", "projectId": null}], "payee": "Cinema Snack Bar", "notes": "Movie night snacks for family outing", "date": "2025-02-17", "projectId": null, "accountId": "Yamabuki Banking Group", "categoryId": "Entertainment"}]}]