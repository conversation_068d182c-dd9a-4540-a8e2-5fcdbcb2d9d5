"use client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";
import { toast } from "sonner";

type RequestType = {
  itemId: string;
  transactionId: string;
  quantity?: number | null;
  unitPrice?: number | null;
  amount: number;
  categoryId?: string | null;
  projectId?: string | null;
};

export const useCreateItemTransaction = () => {
  const queryClient = useQueryClient();
  const userId = useCurrentUserId();

  const mutation = useMutation({
    mutationFn: async (json: RequestType) => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const response = await client.api.itemTransactions.$post(
        { json },
        {
          headers: {
            "X-User-ID": userId,
          },
        },
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || "Failed to create item transaction");
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success("Item transaction created");
      queryClient.invalidateQueries({ queryKey: ["itemTransactions"] });
      queryClient.invalidateQueries({ queryKey: ["transactions"] });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  return mutation;
};