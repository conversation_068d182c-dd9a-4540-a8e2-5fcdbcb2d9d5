import { useQuery } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";

export const useGetWishlistItems = (status?: string, category?: string, priority?: string) => {
  const userId = useCurrentUserId();

  const query = useQuery({
    queryKey: ["wishlist-items", userId, { status, category, priority }],
    queryFn: async () => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const params = new URLSearchParams();
      
      if (status) params.append("status", status);
      if (category) params.append("category", category);
      if (priority) params.append("priority", priority);
      
      const response = await client.api.wishlist.$get(
        { query: Object.fromEntries(params) },
        {
          headers: {
            "X-User-ID": userId,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch wishlist items");
      }

      const { data } = await response.json();
      return data;
    },
  });

  return query;
};