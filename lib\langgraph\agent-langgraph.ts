import { StateGraph, START, END, Annotation } from "@langchain/langgraph";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { BaseMessage, HumanMessage, AIMessage, SystemMessage, ToolMessage } from "@langchain/core/messages";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { RunnableConfig } from "@langchain/core/runnables";
import { DynamicStructuredTool } from "@langchain/core/tools";
import { z } from "zod";
import * as fs from "fs";
import * as path from "path";
import { functions } from "@/lib/AI/functions-ai-chat";

// Define media item interface
export interface MediaItem {
    id: string;
    fileName: string;
    mimeType: string;
    url: string;
    filePath: string;
}

// Define the state for our agent - following LangGraph pattern
const AgentState = Annotation.Root({
    messages: Annotation<BaseMessage[]>({
        reducer: (x, y) => x.concat(y),
        default: () => [],
    }),
    input: Annotation<string>(),
    media: Annotation<MediaItem[]>({
        reducer: (x, y) => x.concat(y),
        default: () => [],
    }),
    output: Annotation<string>(),
    personaId: Annotation<string>(),
});

// Step 1: Define the tools we want to use - following the exact pattern from .input.md
function createFinancialTools(personaId: string) {
    
    // Fetch tools
    const fetchCategoriesTools = new DynamicStructuredTool({
        name: "fetchCategories",
        description: "Retrieves a list of all created financial categories. Returns an array of objects with id, name, goal, and amount properties.",
        schema: z.object({}),
        func: async ({}) => {
            return await functions.fetchCategories({ personaId });
        },
    });

    const fetchAccountsTools = new DynamicStructuredTool({
        name: "fetchAccounts", 
        description: "Retrieves a list of all financial accounts, such as bank accounts or payment services like PayPal. Returns an array with id and name properties.",
        schema: z.object({}),
        func: async ({}) => {
            return await functions.fetchAccounts({ personaId });
        },
    });

    const fetchProjectsTools = new DynamicStructuredTool({
        name: "fetchProjects",
        description: "Retrieves a list of all created projects. Optionally filters projects based on a specific accountId.",
        schema: z.object({
            accountId: z.string().optional().describe("Optional ID of the account to filter projects by"),
        }),
        func: async ({ accountId }) => {
            return await functions.fetchProjects({ accountId, personaId });
        },
    });

    const fetchTransactionsTools = new DynamicStructuredTool({
        name: "fetchTransactions",
        description: "Retrieves multiple transactions within a specified date interval, optionally filtered by account ID. Includes all related detailsTransactions.",
        schema: z.object({
            from: z.string().describe("The start date of the interval in ISO format (e.g., '2024-01-01')"),
            to: z.string().describe("The end date of the interval in ISO format (e.g., '2024-12-31')"),
            accountId: z.string().optional().describe("Optional ID of the account to filter transactions by"),
        }),
        func: async ({ from, to, accountId }) => {
            return await functions.fetchTransactions({ from, to, accountId, personaId });
        },
    });

    const fetchManyItemsTools = new DynamicStructuredTool({
        name: "fetchManyItems",
        description: "Retrieves all detailsTransactions within a specified date interval, independent of their related transactions.",
        schema: z.object({
            from: z.string().describe("The start date of the interval in ISO format (e.g., '2024-01-01')"),
            to: z.string().describe("The end date of the interval in ISO format (e.g., '2024-12-31')"),
        }),
        func: async ({ from, to }) => {
            return await functions.fetchManyItems({ from, to, personaId });
        },
    });

    const fetchOneTransactionTools = new DynamicStructuredTool({
        name: "fetchOneTransaction",
        description: "Retrieves a specific transaction by its unique ID, including all related detailsTransactions.",
        schema: z.object({
            id: z.string().describe("The unique identifier of the transaction to fetch"),
        }),
        func: async ({ id }) => {
            return await functions.fetchOneTransaction({ id, personaId });
        },
    });

    const fetchOneItemTools = new DynamicStructuredTool({
        name: "fetchOneItem", 
        description: "Retrieves a specific detailsTransaction by its unique ID.",
        schema: z.object({
            id: z.string().describe("The unique identifier of the detailsTransaction to fetch"),
        }),
        func: async ({ id }) => {
            return await functions.fetchOneItem({ id, personaId });
        },
    });

    // Create tools
    const createOneTransactionTools = new DynamicStructuredTool({
        name: "createOneTransaction",
        description: "Creates a new transaction with the provided details. Amount values are multiplied by 1,000 for fixed-point arithmetic.",
        schema: z.object({
            date: z.string().describe("The date of the transaction in ISO format (e.g., '2024-11-14')"),
            accountId: z.string().describe("The ID of the account associated with the transaction"),
            amount: z.number().describe("The amount of the transaction. Income are positive values, expenses are negative values. Will be multiplied by 1,000 for storage."),
            payee: z.string().describe("The payee for the transaction"),
            notes: z.string().optional().nullable().describe("Optional notes or description for the transaction"),
            projectId: z.string().optional().nullable().describe("Optional project ID to associate the transaction with"),
            categoryId: z.string().optional().nullable().describe("Optional category ID to classify the transaction"),
        }),
        func: async ({ date, accountId, amount, payee, notes, projectId, categoryId }) => {
            return await functions.createOneTransaction({
                date: new Date(date),
                accountId,
                amount: amount * 1000, // Apply fixed-point arithmetic
                payee,
                notes,
                projectId,
                categoryId,
                personaId,
            });
        },
    });

    const createOneItemTools = new DynamicStructuredTool({
        name: "createOneItem",
        description: "Creates a new detailsTransaction with the provided details. All price-related values are multiplied by 1,000 for fixed-point arithmetic.",
        schema: z.object({
            transactionId: z.string().describe("The ID of the parent transaction"),
            name: z.string().describe("The name or description of the detail transaction"),
            quantity: z.number().describe("The quantity of the item purchased"),
            unitPrice: z.number().describe("The price per unit of the item. Usually positive. Will be multiplied by 1,000 for storage."),
            amount: z.number().describe("The total amount for this detail transaction. Income are positive, expenses are negative."),
            projectId: z.string().optional().nullable().describe("Optional project ID to associate the detail transaction with"),
            categoryId: z.string().optional().nullable().describe("Optional category ID to classify the detail transaction"),
        }),
        func: async ({ transactionId, name, quantity, unitPrice, amount, projectId, categoryId }) => {
            return await functions.createOneItem({
                transactionId,
                name,
                quantity,
                unitPrice: unitPrice * 1000, // Apply fixed-point arithmetic
                amount: amount * 1000, // Apply fixed-point arithmetic
                projectId,
                categoryId,
                personaId,
            });
        },
    });

    const createAccountTools = new DynamicStructuredTool({
        name: "createAccount",
        description: "Creates a new financial account with the provided name.",
        schema: z.object({
            name: z.string().describe("The name of the financial account (e.g., 'Commerzbank', 'PayPal')"),
        }),
        func: async ({ name }) => {
            return await functions.createAccount({ name, personaId });
        },
    });

    const createCategoryTools = new DynamicStructuredTool({
        name: "createCategory",
        description: "Creates a new financial category with the provided name and optional monthly goal. Goal is multiplied by 1,000 for storage.",
        schema: z.object({
            name: z.string().describe("The name of the financial category (e.g., 'Groceries', 'Entertainment')"),
            goal: z.number().optional().nullable().describe("Optional monthly goal for the category. Will be multiplied by 1,000 for storage."),
        }),
        func: async ({ name, goal }) => {
            return await functions.createCategory({
                name,
                goal: goal ? goal * 1000 : goal, // Apply fixed-point arithmetic if goal exists
                personaId,
            });
        },
    });

    const createProjectTools = new DynamicStructuredTool({
        name: "createProject",
        description: "Creates a new project with the provided details. Budget is multiplied by 1,000 for fixed-point arithmetic.",
        schema: z.object({
            name: z.string().describe("The name of the project (e.g., 'Marketing Campaign', 'Website Redesign')"),
            budget: z.number().describe("The financial budget allocated for the project. Will be multiplied by 1,000 for storage."),
            startDate: z.string().describe("The start date of the project in ISO format (e.g., '2024-01-01')"),
            endDate: z.string().describe("The end date of the project in ISO format (e.g., '2024-06-30')"),
            description: z.string().optional().nullable().describe("Optional description or details about the project"),
        }),
        func: async ({ name, budget, startDate, endDate, description }) => {
            return await functions.createProject({
                name,
                budget: budget * 1000, // Apply fixed-point arithmetic
                startDate,
                endDate,
                description,
                personaId,
            });
        },
    });

    const getTodaysDateTools = new DynamicStructuredTool({
        name: "getTodaysDate",
        description: "Gets the current date and returns it as a string representation of today's date.",
        schema: z.object({}),
        func: async ({}) => {
            return await functions.getTodaysDate({ personaId });
        },
    });

    // Return all tools as an array - following the exact pattern from .input.md
    return [
        // Fetch tools
        fetchCategoriesTools,
        fetchAccountsTools,
        fetchProjectsTools,
        fetchTransactionsTools,
        fetchManyItemsTools,
        fetchOneTransactionTools,
        fetchOneItemTools,
        
        // Create tools
        createOneTransactionTools,
        createOneItemTools,
        createAccountTools,
        createCategoryTools,
        createProjectTools,
        
        // Utility tools
        getTodaysDateTools,
    ];
}

export class ChatAgent {
    private systemPrompt = `You are a helpful financial assistant. You can analyze financial documents, receipts, statements, and answer questions about personal finance management.
    
    You have access to comprehensive financial tools that allow you to:
    - Fetch and view accounts, categories, transactions, and projects
    - Create new financial records (transactions, accounts, categories, projects)
    - Update existing financial data
    - Analyze spending patterns and financial data
    
    When users ask about their financial data, use the appropriate tools to fetch the information. When they want to create or modify financial records, use the creation and update tools.
    
    Important: All monetary amounts in the system use fixed-point arithmetic (multiplied by 1,000). The tools handle this conversion automatically, so you can work with normal decimal values.`;

    private mimeFromExt(filePath: string): string {
        const ext = path.extname(filePath).toLowerCase();
        switch (ext) {
            case '.jpg':
            case '.jpeg':
                return 'image/jpeg';
            case '.png':
                return 'image/png';
            case '.gif':
                return 'image/gif';
            case '.webp':
                return 'image/webp';
            case '.pdf':
                return 'application/pdf';
            case '.mp4':
                return 'video/mp4';
            case '.mov':
                return 'video/quicktime';
            case '.webm':
                return 'video/webm';
            default:
                return 'application/octet-stream';
        }
    }

    private async prepareRequestNode(state: typeof AgentState.State) {
        const { input, media } = state;
        
        // Create the content array starting with the text input
        const content: Array<{
            type: string;
            text?: string;
            mime_type?: string;
            source_type?: string;
            data?: string;
        }> = [
            {
                type: "text",
                text: input,
            },
        ];

        // Add media files to the content
        for (const mediaItem of media) {
            try {
                // Read and encode the media file as base64
                const mediaData = fs.readFileSync(mediaItem.filePath);
                const base64Media = mediaData.toString('base64');
                
                content.push({
                    type: "file",
                    mime_type: mediaItem.mimeType,
                    source_type: "base64",
                    data: base64Media,
                });

                // Add a text description for the media
                content.push({
                    type: "text",
                    text: `Attached file: ${mediaItem.fileName} (${mediaItem.mimeType})`,
                });
            } catch (error) {
                console.error(`Error reading media file ${mediaItem.fileName}:`, error);
                // Continue processing other files
            }
        }

        const humanMessage = new HumanMessage({
            content: content,
        });

        return { messages: [new SystemMessage(this.systemPrompt), humanMessage] };
    }

    public async invoke(input: { messages: BaseMessage[], input: string, media?: MediaItem[], personaId: string }) {
        const { personaId } = input;

        // Step 1: Create tools array - following the exact pattern from .input.md
        const tools = createFinancialTools(personaId);

        // Step 2: Wrap tools in ToolNode - following the exact pattern from .input.md
        const toolNode = new ToolNode<typeof AgentState.State>(tools);

        // Step 3: Set up the model - following the exact pattern from .input.md
        const apiKey = process.env.GOOGLE_API_KEY;
        if (!apiKey) {
            throw new Error('GOOGLE_API_KEY environment variable is not set');
        }

        const model = new ChatGoogleGenerativeAI({
            model: "gemini-2.0-flash-exp",
            temperature: 0.1,
            maxRetries: 2,
            apiKey: apiKey,
        });

        // Step 4: Bind tools to model - following the exact pattern from .input.md
        const boundModel = model.bindTools(tools);

        // Step 5: Define the function that determines whether to continue or not - following the exact pattern from .input.md
        const shouldContinue = (state: typeof AgentState.State) => {
            const { messages } = state;
            const lastMessage = messages[messages.length - 1] as AIMessage;
            // If there is no function call, then we finish
            if (!lastMessage.tool_calls || lastMessage.tool_calls.length === 0) {
                return END;
            }
            // Otherwise if there is, we continue
            return "tools";
        };

        // Step 6: Define callModel function - following the exact pattern from .input.md
        const callModel = async (
            state: typeof AgentState.State,
            config?: RunnableConfig,
        ) => {
            let modelMessages = [];
            for (let i = state.messages.length - 1; i >= 0; i--) {
                modelMessages.push(state.messages[i]);
                if (modelMessages.length >= 5) {
                    if (!ToolMessage.isInstance(modelMessages[modelMessages.length - 1])) {
                        break;
                    }
                }
            }
            modelMessages.reverse();

            const response = await boundModel.invoke(modelMessages, config);
            // We return an object, because this will get added to the existing list
            return { messages: [response] };
        };

        // Step 7: Define the workflow - following the exact pattern from .input.md
        const workflow = new StateGraph(AgentState)
            .addNode("prepareRequest", this.prepareRequestNode.bind(this))
            .addNode("agent", callModel)
            .addNode("tools", toolNode)
            .addEdge(START, "prepareRequest")
            .addEdge("prepareRequest", "agent")
            .addConditionalEdges("agent", shouldContinue)
            .addEdge("tools", "agent");

        // Step 8: Compile and run - following the exact pattern from .input.md
        const app = workflow.compile();
        
        const result = await app.invoke({
            messages: input.messages || [],
            input: input.input,
            media: input.media || [],
            output: "",
            personaId: input.personaId,
        });
        
        return result;
    }
}