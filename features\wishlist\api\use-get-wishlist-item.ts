import { useQuery } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";

export const useGetWishlistItem = (id?: string) => {
  const userId = useCurrentUserId();

  const query = useQuery({
    enabled: !!id && !!userId,
    queryKey: ["wishlist-item", userId, { id }],
    queryFn: async () => {
      if (!id) {
        throw new Error("Missing id");
      }
      
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const response = await client.api.wishlist[":id"].$get(
        { param: { id } },
        {
          headers: {
            "X-User-ID": userId,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch wishlist item");
      }

      const { data } = await response.json();
      return data;
    },
  });

  return query;
};