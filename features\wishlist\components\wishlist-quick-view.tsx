import { ExternalLink, ShoppingBag, Calendar, Target } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { formatCurrency } from "@/lib/utils";
import { format } from "date-fns";

type WishlistSource = {
  id: string;
  name: string;
  url?: string;
  price?: number;
  notes?: string;
};

type WishlistPossibility = {
  id: string;
  name: string;
  description?: string;
  notes?: string;
  sources: WishlistSource[];
};

type WishlistItem = {
  id: string;
  name: string;
  category: string;
  estimatedCost: number;
  targetAmount?: number;
  quantity: number;
  targetDate?: Date;
  priority: string;
  status: string;
  notes?: string;
  links?: string;
  imageUrl?: string;
  motivation?: string;
  possibilities: WishlistPossibility[];
};

type Props = {
  item: WishlistItem;
};

export const WishlistQuickView = ({ item }: Props) => {
  const totalSources = item.possibilities.reduce((total, possibility) => total + possibility.sources.length, 0);
  
  const getLowestPrice = () => {
    const allPrices = item.possibilities
      .flatMap(p => p.sources)
      .map(s => s.price)
      .filter((price): price is number => price !== undefined && price !== null);
    
    return allPrices.length > 0 ? Math.min(...allPrices) : null;
  };

  const lowestPrice = getLowestPrice();

  return (
    <div className="bg-gradient-to-br from-indigo-50 to-cyan-50 p-6 border-l-4 border-l-indigo-400">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Info */}
        <div className="lg:col-span-2">
          <div className="flex items-start gap-4 mb-4">
            {item.imageUrl && (
              <img
                src={item.imageUrl}
                alt={item.name}
                className="w-16 h-16 object-cover rounded-lg shadow-sm"
              />
            )}
            <div className="flex-1">
              <h3 className="text-xl font-bold text-gray-900 mb-1">{item.name}</h3>
              <p className="text-indigo-600 font-medium mb-2">{item.category}</p>
              
              {item.motivation && (
                <div className="bg-white/60 rounded-lg p-3 border border-indigo-100 mb-3">
                  <p className="text-sm text-gray-700 italic">"{item.motivation}"</p>
                </div>
              )}
              
              {item.notes && (
                <div className="bg-white/60 rounded-lg p-3 border border-indigo-100">
                  <p className="text-sm text-gray-700">{item.notes}</p>
                </div>
              )}
            </div>
          </div>

          {item.links && (
            <div className="mt-3">
              <a
                href={item.links}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-1 text-indigo-600 hover:text-indigo-800 font-medium text-sm transition-colors"
              >
                Visit General Link <ExternalLink className="h-3 w-3" />
              </a>
            </div>
          )}
        </div>

        {/* Stats */}
        <div className="space-y-3">
          <div className="bg-white/80 rounded-lg p-4 text-center border border-indigo-100">
            <p className="text-2xl font-bold text-indigo-600">{formatCurrency(item.estimatedCost)}</p>
            <p className="text-sm text-gray-600">Estimated Cost</p>
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            <div className="bg-white/80 rounded-lg p-3 text-center border border-indigo-100">
              <p className="text-lg font-semibold text-gray-900">{item.quantity}</p>
              <p className="text-xs text-gray-600">Qty</p>
            </div>
            
            <div className="bg-white/80 rounded-lg p-3 text-center border border-indigo-100">
              <p className="text-lg font-semibold text-gray-900">{item.possibilities.length}</p>
              <p className="text-xs text-gray-600">Options</p>
            </div>
          </div>

          {item.targetAmount && (
            <div className="bg-white/80 rounded-lg p-3 text-center border border-indigo-100">
              <Target className="h-4 w-4 text-green-600 mx-auto mb-1" />
              <p className="text-sm font-semibold text-green-600">{formatCurrency(item.targetAmount)}</p>
              <p className="text-xs text-gray-600">Target</p>
            </div>
          )}
          
          {item.targetDate && (
            <div className="bg-white/80 rounded-lg p-3 text-center border border-indigo-100">
              <Calendar className="h-4 w-4 text-orange-600 mx-auto mb-1" />
              <p className="text-sm font-semibold text-orange-600">{format(new Date(item.targetDate), "MMM dd")}</p>
              <p className="text-xs text-gray-600">Target Date</p>
            </div>
          )}

          {lowestPrice && (
            <div className="bg-white/80 rounded-lg p-3 text-center border border-indigo-100">
              <p className="text-lg font-semibold text-green-600">{formatCurrency(lowestPrice)}</p>
              <p className="text-xs text-gray-600">Best Price</p>
            </div>
          )}
        </div>
      </div>

      {/* Possibilities Summary */}
      {item.possibilities.length > 0 && (
        <div className="mt-6 pt-4 border-t border-indigo-200">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-semibold text-gray-900">Shopping Options</h4>
            <Badge variant="secondary" className="text-xs">
              {totalSources} total sources
            </Badge>
          </div>
          
          <div className="space-y-3">
            {item.possibilities.map((possibility) => (
              <div key={possibility.id} className="bg-white/60 rounded-lg p-4 border border-indigo-100">
                <div className="flex items-center justify-between mb-2">
                  <h5 className="font-semibold text-gray-900">{possibility.name}</h5>
                  <Badge variant="outline" className="text-xs">
                    {possibility.sources.length} sources
                  </Badge>
                </div>
                
                {possibility.description && (
                  <p className="text-sm text-gray-600 mb-3">{possibility.description}</p>
                )}
                
                {possibility.sources.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {possibility.sources.map((source) => (
                      <div key={source.id} className="flex items-center gap-2 bg-gray-50 rounded px-3 py-1">
                        <span className="text-sm font-medium">{source.name}</span>
                        {source.price && (
                          <span className="text-sm font-semibold text-green-600">
                            {formatCurrency(source.price)}
                          </span>
                        )}
                        {source.url && (
                          <a
                            href={source.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-indigo-600 hover:text-indigo-800"
                          >
                            <ExternalLink className="h-3 w-3" />
                          </a>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};