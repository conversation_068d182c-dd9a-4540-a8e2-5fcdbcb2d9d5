"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { useState } from "react";
import { ArrowLeft, Loader2, Package, Store, Tag } from "lucide-react";
import { DataTable } from "@/components/data-table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { useRouter } from "next/navigation";
import { useGetItemTransactions } from "@/features/itemTransactions/api/use-get-item-transactions";
import { itemTransactionColumns } from "./item-transaction-columns";
import { format } from "date-fns";

type Props = {
  params: {
    id: string;
  };
};

const ItemDetailPage = ({ params }: Props) => {
  const router = useRouter();
  const [searchParams, setSearchParams] = useState(new URLSearchParams({
    itemId: params.id
  }));

  // Mock item data - in real app, you'd fetch this from API
  const [item, setItem] = useState({
    id: params.id,
    name: "Loading...",
    description: null as string | null,
    payee: null as string | null,
    defaultCategoryName: null as string | null,
    usageCount: 0,
    createdAt: new Date(),
  });

  const itemTransactionsQuery = useGetItemTransactions({
    itemId: params.id
  });
  const itemTransactions = itemTransactionsQuery.data || [];

  const isLoading = itemTransactionsQuery.isLoading;

  const handleBack = () => {
    router.push("/dashboard/items");
  };

  if (isLoading) {
    return (
      <div className="max-w-screen-2xl mx-auto w-full pb-10">
        <div className="flex items-center gap-2 mb-4">
          <Button variant="outline" size="sm" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Items
          </Button>
        </div>
        
        <div className="grid gap-6">
          <Card className="border-none drop-shadow-sm">
            <CardHeader>
              <Skeleton className="h-8 w-64" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-20 w-full" />
            </CardContent>
          </Card>
          
          <Card className="border-none drop-shadow-sm">
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent>
              <div className="h-[400px] flex items-center justify-center">
                <Loader2 className="size-4 text-slate-300 animate-spin" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Update item details from first transaction if available
  if (itemTransactions.length > 0 && item.name === "Loading...") {
    const firstTransaction = itemTransactions[0];
    setItem({
      id: params.id,
      name: firstTransaction.itemName || "Unknown Item",
      description: firstTransaction.itemDescription,
      payee: null, // This would come from the items API
      defaultCategoryName: firstTransaction.categoryName,
      usageCount: itemTransactions.length,
      createdAt: new Date(), // This would come from the items API
    });
  }

  return (
    <div className="max-w-screen-2xl mx-auto w-full pb-10">
      {/* Header */}
      <div className="flex items-center gap-2 mb-6">
        <Button variant="outline" size="sm" onClick={handleBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Items
        </Button>
      </div>

      <div className="grid gap-6">
        {/* Item Details */}
        <Card className="border-none drop-shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              {item.name}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <div className="text-sm font-medium text-muted-foreground mb-1">
                  Description
                </div>
                <div className="text-sm">
                  {item.description || "No description"}
                </div>
              </div>
              
              {item.payee && (
                <div>
                  <div className="text-sm font-medium text-muted-foreground mb-1">
                    Store/Vendor
                  </div>
                  <Badge variant="secondary" className="text-sm">
                    <Store className="h-3 w-3 mr-1" />
                    {item.payee}
                  </Badge>
                </div>
              )}

              {item.defaultCategoryName && (
                <div>
                  <div className="text-sm font-medium text-muted-foreground mb-1">
                    Default Category
                  </div>
                  <Badge variant="outline" className="text-sm">
                    <Tag className="h-3 w-3 mr-1" />
                    {item.defaultCategoryName}
                  </Badge>
                </div>
              )}

              <div>
                <div className="text-sm font-medium text-muted-foreground mb-1">
                  Usage Count
                </div>
                <Badge variant="default" className="text-sm">
                  {item.usageCount} transaction{item.usageCount !== 1 ? 's' : ''}
                </Badge>
              </div>

              <div>
                <div className="text-sm font-medium text-muted-foreground mb-1">
                  Created
                </div>
                <div className="text-sm">
                  {format(item.createdAt, "dd MMM yyyy")}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Transaction History */}
        <Card className="border-none drop-shadow-sm">
          <CardHeader>
            <CardTitle>Transaction History</CardTitle>
          </CardHeader>
          <CardContent>
            {itemTransactions.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No transactions found for this item</p>
              </div>
            ) : (
              <div>
                <DataTable
                  filterKey="transactionPayee"
                  columns={itemTransactionColumns}
                  data={itemTransactions}
                  disabled={false}
                  onDelete={(rows) => {
                    const ids = rows.map((row) => row.original.id);
                    // TODO: Implement delete functionality
                    console.log("Delete item transactions:", ids);
                  }}
                />
                
                {/* Summary Statistics */}
                <div className="mt-4 p-4 bg-muted/30 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="font-medium">Total Transactions</div>
                      <div className="text-2xl font-bold text-primary">
                        {itemTransactions.length}
                      </div>
                    </div>
                    <div>
                      <div className="font-medium">Total Amount</div>
                      <div className="text-2xl font-bold text-green-600">
                        ${Math.abs(itemTransactions.reduce((sum, t) => sum + t.amount, 0)).toFixed(2)}
                      </div>
                    </div>
                    <div>
                      <div className="font-medium">Average Amount</div>
                      <div className="text-2xl font-bold text-blue-600">
                        ${itemTransactions.length > 0 ? 
                          (Math.abs(itemTransactions.reduce((sum, t) => sum + t.amount, 0)) / itemTransactions.length).toFixed(2) : 
                          '0.00'
                        }
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ItemDetailPage;