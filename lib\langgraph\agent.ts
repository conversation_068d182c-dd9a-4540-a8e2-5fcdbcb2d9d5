import { StateGraph, START, END, Annotation } from "@langchain/langgraph";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { BaseMessage, HumanMessage, AIMessage, SystemMessage, ToolMessage } from "@langchain/core/messages";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { RunnableConfig, Runnable} from "@langchain/core/runnables";
import { DynamicStructuredTool } from "@langchain/core/tools";
import { z } from "zod";
import * as fs from "fs";
import * as path from "path";
import { functions } from "@/lib/AI/functions-ai-chat";
import { createFinancialTools } from "./financial-tools";
import type { BaseLanguageModelInput } from "@langchain/core/language_models/base";

// Define media item interface
export interface MediaItem {
    id: string;
    fileName: string;
    mimeType: string;
    url: string;
    filePath: string;
}

// Define the state for our agent
const AgentState = Annotation.Root({
    messages: Annotation<BaseMessage[]>({
        reducer: (x, y) => x.concat(y),
        default: () => [],
    }),
    input: Annotation<string>(),
    media: Annotation<MediaItem[]>({
        reducer: (x, y) => x.concat(y),
        default: () => [],
    }),
    output: Annotation<string>(),
    userId: Annotation<string>(),
});

// Define the schema for the agent's output
const recipeSchema = z.object({
    title: z.string().min(1).max(200),
    description: z.string(),
    // Add other fields as needed
});


export class ChatAgent {
    userId: string;
    tools: DynamicStructuredTool[] = [];
    systemPrompt: string;


    private currentPersonaId: string = "";

    constructor({tools, userId, systemPrompt}: {tools: DynamicStructuredTool[], userId: string, systemPrompt: string}) {
        this.tools = tools;
        this.userId = userId;
        this.systemPrompt = systemPrompt;
        // Tools and model will be initialized with personaId context
    }

    private mimeFromExt(filePath: string): string {
        const ext = path.extname(filePath).toLowerCase();
        
        switch (ext) {
            // Video formats
            case '.mp4':
                return 'video/mp4';
            case '.mov':
                return 'video/quicktime';
            case '.webm':
                return 'video/webm';
            case '.mpeg':
            case '.mpg':
                return 'video/mpeg';
            case '.wmv':
                return 'video/wmv';
            case '.3gp':
                return 'video/3gpp';
            case '.flv':
                return 'video/x-flv';
            
            // Image formats
            case '.png':
                return 'image/png';
            case '.jpg':
            case '.jpeg':
                return 'image/jpeg';
            case '.webp':
                return 'image/webp';
            
            // Audio formats
            case '.aac':
                return 'audio/x-aac';
            case '.flac':
                return 'audio/flac';
            case '.mp3':
                return 'audio/mp3';
            case '.m4a':
                return 'audio/m4a';
            case '.mpeg':
                return 'audio/mpeg';
            case '.mpga':
                return 'audio/mpga';
            case '.opus':
                return 'audio/opus';
            case '.pcm':
                return 'audio/pcm';
            case '.wav':
                return 'audio/wav';
            
            // Document formats
            case '.pdf':
                return 'application/pdf';
            case '.txt':
                return 'text/plain';
            
            default:
                return 'application/octet-stream';
        }
    }

    private async prepareRequestNode(state: typeof AgentState.State) {
        const { input, media } = state;
        
        // Create the content array starting with the text input
        const content: Array<{
            type: string;
            text?: string;
            mime_type?: string;
            source_type?: string;
            data?: string;
        }> = [
            {
                type: "text",
                text: input,
            },
        ];

        // Add media files to the content
        for (const mediaItem of media) {
            try {
                // Read and encode the media file as base64
                const mediaData = fs.readFileSync(mediaItem.filePath);
                const base64Media = mediaData.toString('base64');
                
                // Get the correct MIME type from file extension
                const correctMimeType = this.mimeFromExt(mediaItem.filePath);
                
                // Determine content type based on MIME type
                const isMediaFile = correctMimeType.startsWith('video/') || 
                                  correctMimeType.startsWith('audio/') || 
                                  correctMimeType.startsWith('image/') ||
                                  correctMimeType === 'application/pdf';
                
                content.push({
                    type: isMediaFile ? "file" : "text",
                    mime_type: correctMimeType,
                    source_type: "base64",
                    data: base64Media,
                });

                // Add a text description for the media
                content.push({
                    type: "text",
                    text: `Attached file: ${mediaItem.fileName} (${correctMimeType})`,
                });
            } catch (error) {
                console.error(`Error reading media file ${mediaItem.fileName}:`, error);
                // Continue processing other files
            }
        }

        const humanMessage = new HumanMessage({
            content: content,
        });

        return { messages: [new SystemMessage(this.systemPrompt), humanMessage] };
    }

    private toolNode = new ToolNode(this.tools);

    // Function to determine whether to continue or end
    private shouldContinue = (state: typeof AgentState.State) => {
        const { messages } = state;
        const lastMessage = messages[messages.length - 1] as AIMessage;
        
        // If there is no function call, then we finish
        if (!lastMessage.tool_calls || lastMessage.tool_calls.length === 0) {
            return END;
        }
        // Otherwise if there is, we continue to tools
        return "action";
    };

    // Modified agent node to handle tool calling
    private async callModel(state: typeof AgentState.State, config?: RunnableConfig) {

        const apiKey = process.env.GOOGLE_API_KEY;
            if (!apiKey) {
                throw new Error('GOOGLE_API_KEY environment variable is not set');
            }


        const model = new ChatGoogleGenerativeAI({
                model: "gemini-2.5-flash",
                temperature: 0.1,
                maxRetries: 2,
                apiKey: apiKey,
            });

        const boundModel = model.bindTools(this.tools);

        const { messages, userId } = state;

        // Limit message history to prevent context overflow
        let modelMessages = [];
        for (let i = messages.length - 1; i >= 0; i--) {
            modelMessages.push(messages[i]);
            if (modelMessages.length >= 10) {
                if (!ToolMessage.isInstance(modelMessages[modelMessages.length - 1])) {
                    break;
                }
            }
        }
        modelMessages.reverse();

        try {
            const response = await boundModel.invoke(modelMessages, config);
            // Return an object because this will get added to the existing list
            return { messages: [response] };
        } catch (error) {
            console.error('Model invocation error:', error);
            const errorMessage = new AIMessage({
                content: "I apologize, but I encountered an error processing your request. Please try again."
            });
            return { messages: [errorMessage] };
        }
    }


    public async invoke(input: { messages: BaseMessage[], input: string, media?: MediaItem[], userId: string }) {
        const workflow = new StateGraph(AgentState)
            .addNode("prepareRequest", this.prepareRequestNode.bind(this))
            .addNode("agent", this.callModel.bind(this))
            .addNode("action", this.toolNode)
            .addEdge(START, "prepareRequest")
            .addEdge("prepareRequest", "agent")
            .addConditionalEdges("agent", this.shouldContinue)
            .addEdge("action", "agent");

        const agent = workflow.compile();
        const result = await agent.invoke({
            messages: input.messages || [],
            input: input.input,
            media: input.media || [],
            output: "",
            userId: input.userId,
        });
        return result;
    }
}
