"use client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";
import { toast } from "sonner";

type RequestType = {
  name: string;
  description?: string;
  defaultCategoryId?: string;
  payee?: string;
};

type ResponseType = {
  data: {
    id: string;
    name: string;
    description?: string | null;
    payee?: string | null;
    defaultCategoryId?: string | null;
    barcode?: string | null;
    userId: string;
    createdAt: Date;
    updatedAt: Date;
  };
  created: boolean;
};

export const useFindOrCreateItem = () => {
  const queryClient = useQueryClient();
  const userId = useCurrentUserId();

  const mutation = useMutation({
    mutationFn: async (json: RequestType): Promise<ResponseType> => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const response = await client.api.items["find-or-create"].$post(
        { json },
        {
          headers: {
            "X-User-ID": userId,
          },
        },
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || "Failed to find or create item");
      }

      return response.json();
    },
    onSuccess: (data) => {
      if (data.created) {
        toast.success(`Item "${data.data.name}" created`);
      }
      queryClient.invalidateQueries({ queryKey: ["items"] });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  return mutation;
};