"use client";

import { useState } from "react";
import { Loader2, Plus, Download, Filter, Package } from "lucide-react";
import { DataTable } from "@/components/data-table";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON>H<PERSON>er, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useGetItemTransactions } from "@/features/itemTransactions/api/use-get-item-transactions";
import { useGetAccounts } from "@/features/accounts/api/use-get-accounts";
import { useGetItems } from "@/features/items/api/use-get-items";
import { useGetCategories } from "@/features/categories/api/use-get-categories";
import { columns } from "./columns";
import { DateFilter } from "@/components/date-filter";
import { AccountFilter } from "@/components/account-filter";
import { CategoryFilter } from "@/components/category-filter";
import { formatCurrency } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const ItemTransactionsPage = () => {
  const [accountFilter, setAccountFilter] = useState<string>("");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [itemFilter, setItemFilter] = useState<string>("all");
  const [dateRange, setDateRange] = useState<{
    from: string;
    to: string;
  }>({
    from: "",
    to: "",
  });

  const itemTransactionsQuery = useGetItemTransactions({
    from: dateRange.from,
    to: dateRange.to,
    accountId: accountFilter,
    categoryId: categoryFilter === "all" ? "" : categoryFilter,
    itemId: itemFilter === "all" ? "" : itemFilter,
  });

  const accountsQuery = useGetAccounts();
  const itemsQuery = useGetItems();
  const categoriesQuery = useGetCategories();

  const itemTransactions = itemTransactionsQuery.data || [];
  const accounts = accountsQuery.data || [];
  const items = itemsQuery.data || [];
  const categories = categoriesQuery.data || [];

  const isLoading = itemTransactionsQuery.isLoading;

  // Calculate summary statistics
  const totalAmount = itemTransactions.reduce((sum, transaction) => sum + transaction.amount, 0);
  const totalQuantity = itemTransactions.reduce((sum, transaction) => sum + (transaction.quantity || 1), 0);
  const uniqueItems = new Set(itemTransactions.map(t => t.itemId)).size;

  const handleExport = () => {
    // Simple CSV export
    const headers = ["Date", "Item", "Payee", "Category", "Quantity", "Unit Price", "Total Amount"];
    const csvData = [
      headers.join(","),
      ...itemTransactions.map(transaction => [
        transaction.date,
        `"${transaction.itemName}"`,
        `"${transaction.transactionPayee}"`,
        `"${transaction.categoryName || 'Uncategorized'}"`,
        transaction.quantity || 1,
        transaction.unitPrice,
        transaction.amount,
      ].join(","))
    ].join("\\n");

    const blob = new Blob([csvData], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `item-transactions-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return (
      <div className="max-w-screen-2xl mx-auto w-full pb-10 -mt-24">
        <Card className="border-none drop-shadow-sm">
          <CardHeader className="gap-y-2 lg:flex-row lg:items-center lg:justify-between">
            <Skeleton className="h-8 w-48" />
          </CardHeader>
          <CardContent>
            <div className="h-[500px] w-full flex items-center justify-center">
              <Loader2 className="size-6 text-slate-300 animate-spin" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-screen-2xl mx-auto w-full pb-10 -mt-24">
      <Card className="border-none drop-shadow-sm">
        <CardHeader className="gap-y-2 lg:flex-row lg:items-center lg:justify-between">
          <CardTitle className="text-xl line-clamp-1 flex items-center gap-2">
            <Package className="h-5 w-5" />
            Item Transactions Overview
          </CardTitle>
          <div className="flex gap-2">
            <Button
              onClick={handleExport}
              size="sm"
              variant="outline"
              className="w-full lg:w-auto"
            >
              <Download className="size-4 mr-2" />
              Export CSV
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{itemTransactions.length}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(totalAmount)}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Unique Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{uniqueItems}</div>
              </CardContent>
            </Card>
          </div>

          {/* Filters */}
          <div className="flex flex-col lg:flex-row gap-2 mb-4">
            <DateFilter
              from={dateRange.from}
              to={dateRange.to}
              onUpdate={(values) => setDateRange(values)}
            />
            <AccountFilter
              accountId={accountFilter}
              onChange={setAccountFilter}
            />
            <CategoryFilter
              categoryId={categoryFilter}
              onChange={setCategoryFilter}
            />
            <Select value={itemFilter} onValueChange={setItemFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Items" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Items</SelectItem>
                {items
                  .filter((item) => item.id && item.id.trim() !== "")
                  .map((item) => (
                    <SelectItem key={item.id} value={item.id}>
                      {item.name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>

          <DataTable
            columns={columns}
            data={itemTransactions}
            filterKey="itemName"
            onDelete={() => {}} // TODO: Implement bulk delete if needed
            disabled={false}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default ItemTransactionsPage;