import { useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";
import { InferRequestType, InferResponseType } from "hono";

type ResponseType = InferResponseType<typeof client.api.wishlist[":id"]["possibilities"]["$post"]>;
type RequestType = InferRequestType<typeof client.api.wishlist[":id"]["possibilities"]["$post"]>["json"];

export const useCreateWishlistPossibility = () => {
  const userId = useCurrentUserId();
  const queryClient = useQueryClient();

  const mutation = useMutation<ResponseType, Error, { wishlistItemId: string; possibility: RequestType; files?: FileList }>({
    mutationFn: async ({ wishlistItemId, possibility, files }) => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      // If files are provided, use the multipart form endpoint
      if (files && files.length > 0) {
        const formData = new FormData();
        formData.append("data", JSON.stringify(possibility));
        
        // Append files with specific keys
        for (let i = 0; i < files.length; i++) {
          formData.append(`file-${i}`, files[i]);
        }

        const response = await fetch(`/api/wishlist/${wishlistItemId}/possibilities/with-files`, {
          method: "POST",
          body: formData,
          headers: {
            "X-User-ID": userId,
          },
        });

        if (!response.ok) {
          throw new Error("Failed to create possibility with files");
        }

        return response.json();
      } else {
        // Use the regular JSON endpoint
        const response = await client.api.wishlist[":id"]["possibilities"].$post(
          { param: { id: wishlistItemId }, json: possibility },
          {
            headers: {
              "X-User-ID": userId,
            },
          }
        );

        if (!response.ok) {
          throw new Error("Failed to create possibility");
        }

        return response.json();
      }
    },
    onSuccess: (data, { wishlistItemId }) => {
      queryClient.invalidateQueries({
        queryKey: ["wishlist-items", userId],
      });
      queryClient.invalidateQueries({
        queryKey: ["wishlist-item", userId, { id: wishlistItemId }],
      });
    },
  });

  return mutation;
};