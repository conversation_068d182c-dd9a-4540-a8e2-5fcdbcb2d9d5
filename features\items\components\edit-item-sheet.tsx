"use client";

import { z } from "zod";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON>etDescription,
  <PERSON>et<PERSON>ead<PERSON>,
  SheetTitle,
} from "@/components/ui/sheet";
import { insertItemSchema } from "@/db/schema";
import { useGetItem } from "../api/use-get-item";
import { useEditItem } from "../api/use-edit-item";
import { ItemForm } from "./item-form";
import { Loader2 } from "lucide-react";

const formSchema = insertItemSchema.omit({
  id: true,
  userId: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  tagIds: z.array(z.string()).optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface EditItemSheetProps {
  isOpen: boolean;
  onClose: () => void;
  id?: string;
}

export const EditItemSheet = ({
  isOpen,
  onClose,
  id,
}: EditItemSheetProps) => {
  const itemQuery = useGetItem(id);
  const editMutation = useEditItem(id);

  const isPending = editMutation.isPending;
  const isLoading = itemQuery.isLoading;

  const onSubmit = (values: FormValues) => {
    editMutation.mutate(values, {
      onSuccess: () => {
        onClose();
      },
    });
  };

  const defaultValues = itemQuery.data ? {
    name: itemQuery.data.name,
    description: itemQuery.data.description || "",
    payee: itemQuery.data.payee || "",
    defaultCategoryId: itemQuery.data.defaultCategoryId || "",
    barcode: itemQuery.data.barcode || "",
    tags: itemQuery.data.tags || [],
  } : undefined;

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="overflow-y-auto">
        <SheetHeader>
          <SheetTitle>Edit Item</SheetTitle>
          <SheetDescription>
            Make changes to this item. Click save when you're done.
          </SheetDescription>
        </SheetHeader>
        
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : (
          <ItemForm
            id={id}
            defaultValues={defaultValues}
            onSubmit={onSubmit}
            onCancel={onClose}
            disabled={isPending}
          />
        )}
      </SheetContent>
    </Sheet>
  );
};