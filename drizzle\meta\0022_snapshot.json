{"id": "2954e8c9-1652-4209-8c2a-4f5704d6a098", "prevId": "c1413eca-98a2-4980-9d9b-2be2e65fe818", "version": "5", "dialect": "pg", "tables": {"account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "plaid_id": {"name": "plaid_id", "type": "text", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "categories": {"name": "categories", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "plaid_id": {"name": "plaid_id", "type": "text", "primaryKey": false, "notNull": false}, "goal": {"name": "goal", "type": "integer", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "conversations": {"name": "conversations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "detailsTransaction": {"name": "detailsTransaction", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "quantity": {"name": "quantity", "type": "real", "primaryKey": false, "notNull": false}, "unit_price": {"name": "unit_price", "type": "integer", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": false}, "transaction_id": {"name": "transaction_id", "type": "text", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"detailsTransaction_project_id_project_id_fk": {"name": "detailsTransaction_project_id_project_id_fk", "tableFrom": "detailsTransaction", "tableTo": "project", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "detailsTransaction_transaction_id_transactions_id_fk": {"name": "detailsTransaction_transaction_id_transactions_id_fk", "tableFrom": "detailsTransaction", "tableTo": "transactions", "columnsFrom": ["transaction_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "detailsTransaction_category_id_categories_id_fk": {"name": "detailsTransaction_category_id_categories_id_fk", "tableFrom": "detailsTransaction", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "item_transactions": {"name": "item_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "item_id": {"name": "item_id", "type": "text", "primaryKey": false, "notNull": true}, "transaction_id": {"name": "transaction_id", "type": "text", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "real", "primaryKey": false, "notNull": false}, "unit_price": {"name": "unit_price", "type": "integer", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "text", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"item_transactions_item_id_items_id_fk": {"name": "item_transactions_item_id_items_id_fk", "tableFrom": "item_transactions", "tableTo": "items", "columnsFrom": ["item_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "item_transactions_transaction_id_transactions_id_fk": {"name": "item_transactions_transaction_id_transactions_id_fk", "tableFrom": "item_transactions", "tableTo": "transactions", "columnsFrom": ["transaction_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "item_transactions_category_id_categories_id_fk": {"name": "item_transactions_category_id_categories_id_fk", "tableFrom": "item_transactions", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "item_transactions_project_id_project_id_fk": {"name": "item_transactions_project_id_project_id_fk", "tableFrom": "item_transactions", "tableTo": "project", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "items": {"name": "items", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "default_category_id": {"name": "default_category_id", "type": "text", "primaryKey": false, "notNull": false}, "barcode": {"name": "barcode", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"items_default_category_id_categories_id_fk": {"name": "items_default_category_id_categories_id_fk", "tableFrom": "items", "tableTo": "categories", "columnsFrom": ["default_category_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "media": {"name": "media", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "message_id": {"name": "message_id", "type": "text", "primaryKey": false, "notNull": true}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true}, "mime_type": {"name": "mime_type", "type": "text", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"media_message_id_messages_id_fk": {"name": "media_message_id_messages_id_fk", "tableFrom": "media", "tableTo": "messages", "columnsFrom": ["message_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "media_files": {"name": "media_files", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true}, "original_file_name": {"name": "original_file_name", "type": "text", "primaryKey": false, "notNull": true}, "mime_type": {"name": "mime_type", "type": "text", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": true}, "s3_key": {"name": "s3_key", "type": "text", "primaryKey": false, "notNull": true}, "s3_url": {"name": "s3_url", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true, "default": "'general'"}, "entity_type": {"name": "entity_type", "type": "text", "primaryKey": false, "notNull": false}, "entity_id": {"name": "entity_id", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"media_files_user_id_user_id_fk": {"name": "media_files_user_id_user_id_fk", "tableFrom": "media_files", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"media_files_s3_key_unique": {"name": "media_files_s3_key_unique", "nullsNotDistinct": false, "columns": ["s3_key"]}}}, "messages": {"name": "messages", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "conversation_id": {"name": "conversation_id", "type": "text", "primaryKey": false, "notNull": true}, "sender": {"name": "sender", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"messages_conversation_id_conversations_id_fk": {"name": "messages_conversation_id_conversations_id_fk", "tableFrom": "messages", "tableTo": "conversations", "columnsFrom": ["conversation_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "project": {"name": "project", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "budget": {"name": "budget", "type": "integer", "primaryKey": false, "notNull": true}, "startDate": {"name": "startDate", "type": "timestamp", "primaryKey": false, "notNull": true}, "endDate": {"name": "endDate", "type": "timestamp", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"name": "session_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}}, "transactions": {"name": "transactions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "payee": {"name": "payee", "type": "text", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "timestamp", "primaryKey": false, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": false}, "category_id": {"name": "category_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"transactions_account_id_accounts_id_fk": {"name": "transactions_account_id_accounts_id_fk", "tableFrom": "transactions", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "transactions_project_id_project_id_fk": {"name": "transactions_project_id_project_id_fk", "tableFrom": "transactions", "tableTo": "project", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "transactions_category_id_categories_id_fk": {"name": "transactions_category_id_categories_id_fk", "tableFrom": "transactions", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}}, "verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "wishlist_items": {"name": "wishlist_items", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "estimated_cost": {"name": "estimated_cost", "type": "integer", "primaryKey": false, "notNull": true}, "target_amount": {"name": "target_amount", "type": "integer", "primaryKey": false, "notNull": false}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "target_date": {"name": "target_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "text", "primaryKey": false, "notNull": false, "default": "'medium'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'planned'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "links": {"name": "links", "type": "text", "primaryKey": false, "notNull": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "motivation": {"name": "motivation", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "wishlist_possibilities": {"name": "wishlist_possibilities", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "wishlist_item_id": {"name": "wishlist_item_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"wishlist_possibilities_wishlist_item_id_wishlist_items_id_fk": {"name": "wishlist_possibilities_wishlist_item_id_wishlist_items_id_fk", "tableFrom": "wishlist_possibilities", "tableTo": "wishlist_items", "columnsFrom": ["wishlist_item_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "wishlist_sources": {"name": "wishlist_sources", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "possibility_id": {"name": "possibility_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"wishlist_sources_possibility_id_wishlist_possibilities_id_fk": {"name": "wishlist_sources_possibility_id_wishlist_possibilities_id_fk", "tableFrom": "wishlist_sources", "tableTo": "wishlist_possibilities", "columnsFrom": ["possibility_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}