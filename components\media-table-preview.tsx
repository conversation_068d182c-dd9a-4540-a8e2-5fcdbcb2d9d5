"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  Eye, 
  Image as ImageIcon, 
  FileText, 
  File,
  Camera
} from "lucide-react";
import { useGetMedia } from "@/features/media/api/use-get-media";
import { MediaGallery } from "@/components/media-gallery";

interface MediaTablePreviewProps {
  entityType: string;
  entityId: string;
  category?: string;
  compact?: boolean;
}

export function MediaTablePreview({
  entityType,
  entityId,
  category,
  compact = true,
}: MediaTablePreviewProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  const { data: mediaData, isLoading } = useGetMedia({
    entityType,
    entityId,
    category,
    limit: compact ? 3 : undefined,
  });

  const files = mediaData?.data || [];

  if (isLoading) {
    return (
      <div className="flex items-center gap-1 text-xs text-gray-500">
        <div className="w-4 h-4 rounded bg-gray-200 animate-pulse" />
        Loading...
      </div>
    );
  }

  if (files.length === 0) {
    return (
      <div className="flex items-center gap-1 text-xs text-gray-400">
        <Camera className="w-3 h-3" />
        No media
      </div>
    );
  }

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) {
      return <ImageIcon className="w-3 h-3 text-blue-500" />;
    }
    if (mimeType === 'application/pdf') {
      return <FileText className="w-3 h-3 text-red-500" />;
    }
    return <File className="w-3 h-3 text-gray-500" />;
  };

  if (compact) {
    return (
      <div className="flex items-center gap-1">
        {/* Thumbnail preview */}
        <div className="flex -space-x-1">
          {files.slice(0, 2).map((file) => (
            <div
              key={file.id}
              className="w-6 h-6 rounded border-2 border-white bg-gray-100 flex items-center justify-center overflow-hidden"
            >
              {file.mimeType.startsWith('image/') ? (
                <img
                  src={file.url}
                  alt={file.originalFileName}
                  className="w-full h-full object-cover"
                />
              ) : (
                getFileIcon(file.mimeType)
              )}
            </div>
          ))}
          {files.length > 2 && (
            <div className="w-6 h-6 rounded border-2 border-white bg-gray-200 flex items-center justify-center">
              <span className="text-xs font-medium">+{files.length - 2}</span>
            </div>
          )}
        </div>
        
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>
            <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
              <Eye className="w-3 h-3 mr-1" />
              {files.length}
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>Media Files</DialogTitle>
              <DialogDescription>
                {files.length} file{files.length !== 1 ? 's' : ''} attached to this {entityType}
              </DialogDescription>
            </DialogHeader>
            <MediaGallery
              entityType={entityType}
              entityId={entityId}
              category={category}
              compact={false}
              showViewToggle={true}
            />
          </DialogContent>
        </Dialog>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <Badge variant="outline" className="text-xs">
          {files.length} file{files.length !== 1 ? 's' : ''}
        </Badge>
      </div>
      <MediaGallery
        entityType={entityType}
        entityId={entityId}
        category={category}
        compact={false}
        showViewToggle={false}
      />
    </div>
  );
}