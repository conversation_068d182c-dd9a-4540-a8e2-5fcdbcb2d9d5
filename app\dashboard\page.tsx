"use client";

import { createId } from "@paralleldrive/cuid2";
import { DataGrid } from "@/components/data-grid";
import { DataCharts } from "@/components/data-charts";
import { useState, useContext, useEffect } from "react";
import { ThemeContext } from "@/components/context-provider";
import { useGenFollowUpQ } from "@/features/chat/api/use-follow-up";
import { useUpdateChat } from "@/features/chat/hook/use-update-message";
import { useGetWeeklyAnalyse } from "@/features/chat/hook/use-get-week-summary";
import { Button } from "@/components/ui/button";
import { RefreshCcw } from "lucide-react";
import { authClient } from "@/lib/auth-client"; // import the auth client

export default function Home() {
  const genFollowUpQ = useGenFollowUpQ();
  const { followHistory, setFollowQ, setWRshort, setWRlong } = useUpdateChat();
  const { mutate } = useGetWeeklyAnalyse();
  const {
    data: session,
    isPending, //loading state
    error, //error object
    refetch, //refetch the session
  } = authClient.useSession();

  // Use real user data instead of persona
  const userInfo = session?.user
    ? {
        id: session.user.id,
        name: session.user.name || "User",
        email: session.user.email || "",
      }
    : null;

  const handleFetchWeeklyAnalyse = () => {
    if (!userInfo) return;

    mutate(
      { personaDes: JSON.stringify(userInfo) },
      {
        onSuccess: (data) => {
          setWRshort(data.reducedText);
          setWRlong(data.res);
        },
      },
    );
  };

  /*  let WeekResumeShort = weekQuery.data?.reducedText 
    let WeekResumeLong = weekQuery.data?.res */

  const handleClick = () => {
    if (!userInfo) return;

    // Call the mutation function with the required payload
    genFollowUpQ.mutate(
      { personaDes: JSON.stringify(userInfo), followHistory },
      {
        onSuccess: (data) => {
          // Handle the successful response
          setFollowQ(data);
        },
        onError: (error) => {
          // Handle any errors
          console.error("Error occurred:", error);
        },
      },
    );
  };

  const [renderKey, setRenderKey] = useState(createId());
  const rerender = () => {
    setRenderKey(createId());
  };

  useEffect(() => {
    if (userInfo) {
      handleFetchWeeklyAnalyse();
      handleClick();
    }
  }, [userInfo?.id]);

  return (
    <div
      key={renderKey}
      className="max-w-screen-2xl flex-1 w-full mx-auto pb-10"
    >
      <DataGrid />
      <Button className="p-3 mb-3" onClick={rerender}>
        <RefreshCcw size={16} />
      </Button>
      <DataCharts />
    </div>
  );
}
