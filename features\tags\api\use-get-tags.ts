import { useQuery } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";

export const useGetTags = (search?: string) => {
  const userId = useCurrentUserId();

  const query = useQuery({
    queryKey: ["tags", { search }],
    queryFn: async () => {
      const response = await client.api.tags.$get(
        { 
          query: search ? { search } : undefined 
        },
        { 
          headers: { "X-User-ID": userId! } 
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch tags");
      }

      const { data } = await response.json();
      return data;
    },
    enabled: !!userId,
  });

  return query;
};