"use client";

import React, { useState, useRef, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Camera, Upload, X, FileImage, Loader2, CheckCircle, AlertCircle, File, Image as ImageIcon, FileText } from "lucide-react";
import { toast } from "sonner";
import { useUploadMedia } from "@/features/media/api/use-upload-media";
import { useGetMedia } from "@/features/media/api/use-get-media";
import { useDeleteMedia } from "@/features/media/api/use-delete-media";

interface UploadedFile {
  id: string;
  file: File;
  preview: string;
  status: 'pending' | 'uploading' | 'completed' | 'error';
}

interface MediaUploadProps {
  onFilesUploaded?: (files: any[]) => void;
  onError?: (error: string) => void;
  maxFiles?: number;
  acceptedTypes?: string[];
  category?: "receipts" | "documents" | "images" | "general";
  entityType?: string;
  entityId?: string;
  title?: string;
  description?: string;
  showExisting?: boolean;
  allowCamera?: boolean;
}

export function MediaUpload({ 
  onFilesUploaded, 
  onError,
  maxFiles = 5,
  acceptedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'application/pdf', 'text/plain'],
  category = "general",
  entityType,
  entityId,
  title = "File Upload",
  description = "Upload files to store in your media library",
  showExisting = false,
  allowCamera = true
}: MediaUploadProps) {
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const uploadMutation = useUploadMedia();
  const deleteMutation = useDeleteMedia();
  const { data: existingFiles } = useGetMedia({ 
    category, 
    entityType, 
    entityId,
    enabled: showExisting 
  });

  const generateId = () => Math.random().toString(36).substring(2);

  const validateFile = (file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return `File type ${file.type} not supported.`;
    }
    if (file.size > 50 * 1024 * 1024) { // 50MB limit
      return "File size must be less than 50MB";
    }
    return null;
  };

  const createFilePreview = (file: File): Promise<string> => {
    return new Promise((resolve) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.readAsDataURL(file);
      } else {
        // For non-image files, return a generic icon
        resolve('');
      }
    });
  };

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) {
      return <ImageIcon className="h-8 w-8 text-blue-500" />;
    }
    if (mimeType === 'application/pdf') {
      return <FileText className="h-8 w-8 text-red-500" />;
    }
    return <File className="h-8 w-8 text-gray-500" />;
  };

  const addFiles = async (newFiles: FileList) => {
    const validFiles: File[] = [];
    
    for (let i = 0; i < newFiles.length; i++) {
      const file = newFiles[i];
      const error = validateFile(file);
      
      if (error) {
        toast.error(`${file.name}: ${error}`);
        continue;
      }
      
      if (files.length + validFiles.length >= maxFiles) {
        toast.error(`Maximum ${maxFiles} files allowed`);
        break;
      }
      
      validFiles.push(file);
    }

    if (validFiles.length === 0) return;

    const uploadedFiles: UploadedFile[] = [];
    for (const file of validFiles) {
      const preview = await createFilePreview(file);
      uploadedFiles.push({
        id: generateId(),
        file,
        preview,
        status: 'pending'
      });
    }

    setFiles(prev => [...prev, ...uploadedFiles]);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files;
    if (selectedFiles) {
      addFiles(selectedFiles);
    }
    event.target.value = '';
  };

  const removeFile = (id: string) => {
    setFiles(prev => prev.filter(file => file.id !== id));
  };

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { facingMode: 'environment' }
      });
      streamRef.current = stream;
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
      setIsCameraOpen(true);
    } catch (error) {
      toast.error("Unable to access camera. Please check permissions.");
    }
  };

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setIsCameraOpen(false);
  };

  const capturePhoto = () => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) return;

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    context.drawImage(video, 0, 0);

    canvas.toBlob(async (blob) => {
      if (!blob) return;

      const file = new File([blob], `photo-${Date.now()}.jpeg`, { type: 'image/jpeg' });
      const preview = await createFilePreview(file);
      
      const uploadedFile: UploadedFile = {
        id: generateId(),
        file,
        preview,
        status: 'pending'
      };

      setFiles(prev => [...prev, uploadedFile]);
      stopCamera();
    }, 'image/jpeg', 0.8);
  };

  const uploadFiles = async () => {
    if (files.length === 0) {
      toast.error("Please select at least one file");
      return;
    }

    setFiles(prev => prev.map(file => ({ ...file, status: 'uploading' as const })));

    const filesToUpload = files.map(f => f.file);
    
    try {
      const result = await uploadMutation.mutateAsync({
        files: filesToUpload,
        options: {
          category,
          entityType,
          entityId,
        }
      });

      setFiles(prev => prev.map(file => ({ ...file, status: 'completed' as const })));
      onFilesUploaded?.(result.data);
      
    } catch (error) {
      setFiles(prev => prev.map(file => ({ ...file, status: 'error' as const })));
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      onError?.(errorMessage);
    }
  };

  const handleDeleteExisting = async (fileId: string) => {
    try {
      await deleteMutation.mutateAsync(fileId);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const clearAll = () => {
    setFiles([]);
    stopCamera();
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileImage className="h-5 w-5" />
          {title}
        </CardTitle>
        <CardDescription>
          {description}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Upload Actions */}
        <div className="flex flex-col sm:flex-row gap-2">
          <Button
            onClick={() => fileInputRef.current?.click()}
            variant="outline"
            type="button"
            className="flex-1"
            disabled={uploadMutation.isPending || files.length >= maxFiles}
          >
            <Upload className="h-4 w-4 mr-2" />
            Choose Files
          </Button>
          
          {allowCamera && (
            <Dialog open={isCameraOpen} onOpenChange={setIsCameraOpen}>
              <DialogTrigger asChild>
                <Button
                  onClick={startCamera}
                  variant="outline"
                  className="flex-1"
                  disabled={uploadMutation.isPending || files.length >= maxFiles}
                >
                  <Camera className="h-4 w-4 mr-2" />
                  Take Photo
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Take Photo</DialogTitle>
                  <DialogDescription>
                    Position your subject clearly in the frame
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <video
                    ref={videoRef}
                    autoPlay
                    playsInline
                    className="w-full rounded-lg"
                  />
                  <canvas ref={canvasRef} className="hidden" />
                  <div className="flex gap-2">
                    <Button onClick={capturePhoto} className="flex-1">
                      Capture
                    </Button>
                    <Button onClick={stopCamera} variant="outline" className="flex-1">
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </div>

        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={handleFileSelect}
          className="hidden"
        />

        {/* File Preview */}
        {files.length > 0 && (
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">
                {files.length} file{files.length > 1 ? 's' : ''} selected
              </span>
              <Button onClick={clearAll} variant="ghost" size="sm">
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
              {files.map(({ id, file, preview, status }) => (
                <div key={id} className="relative group">
                  <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 border flex items-center justify-center">
                    {file.type.startsWith('image/') && preview ? (
                      <img
                        src={preview}
                        alt={file.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      getFileIcon(file.type)
                    )}
                  </div>
                  
                  {/* Status overlay */}
                  <div className="absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                    {status === 'pending' && (
                      <Badge variant="secondary">Ready</Badge>
                    )}
                    {status === 'uploading' && (
                      <Badge variant="secondary">
                        <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                        Uploading
                      </Badge>
                    )}
                    {status === 'completed' && (
                      <Badge variant="default">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Done
                      </Badge>
                    )}
                    {status === 'error' && (
                      <Badge variant="destructive">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Error
                      </Badge>
                    )}
                  </div>

                  {/* Remove button */}
                  <Button
                    onClick={() => removeFile(id)}
                    size="sm"
                    variant="destructive"
                    className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    disabled={uploadMutation.isPending}
                  >
                    <X className="h-3 w-3" />
                  </Button>

                  {/* File name */}
                  <div className="absolute bottom-0 left-0 right-0 bg-black/70 text-white text-xs p-1 rounded-b-lg truncate">
                    {file.name}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Upload Button */}
        {files.length > 0 && (
          <Button
            onClick={uploadFiles}
            disabled={uploadMutation.isPending || files.every(f => f.status === 'completed')}
            className="w-full"
          >
            {uploadMutation.isPending ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Uploading Files...
              </>
            ) : (
              `Upload ${files.length} File${files.length > 1 ? 's' : ''}`
            )}
          </Button>
        )}

        {/* Progress indicator */}
        {uploadMutation.isPending && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Uploading files...</span>
              <span>{files.filter(f => f.status === 'completed').length}/{files.length}</span>
            </div>
            <Progress 
              value={(files.filter(f => f.status === 'completed').length / files.length) * 100} 
            />
          </div>
        )}

        {/* Existing Files */}
        {showExisting && existingFiles?.data && existingFiles.data.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Existing Files</h4>
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
              {existingFiles.data.map((file) => (
                <div key={file.id} className="relative group">
                  <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 border flex items-center justify-center">
                    {file.mimeType.startsWith('image/') ? (
                      <img
                        src={file.url}
                        alt={file.originalFileName}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      getFileIcon(file.mimeType)
                    )}
                  </div>

                  {/* Delete button */}
                  <Button
                    onClick={() => handleDeleteExisting(file.id)}
                    size="sm"
                    variant="destructive"
                    className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    disabled={deleteMutation.isPending}
                  >
                    <X className="h-3 w-3" />
                  </Button>

                  {/* File info */}
                  <div className="absolute bottom-0 left-0 right-0 bg-black/70 text-white text-xs p-1 rounded-b-lg">
                    <div className="truncate">{file.originalFileName}</div>
                    <div className="text-gray-300">{formatFileSize(file.fileSize)}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}