export const testData = [
  [
    {
      accountId: "commerzbank",
      amount: -150000,
      payee: "Berlin Apartments GmbH",
      date: "2024-04-08T10:00:00Z",
      notes: "Monthly rent payment",
      categoryId: "housing",
      detailsTransactions: [],
    },
    {
      accountId: "paypal",
      amount: -35000,
      payee: "Café Kultur",
      date: "2024-04-09T16:30:00Z",
      notes: "Catching up with friends",
      categoryId: "dining",
      detailsTransactions: [
        {
          amount: 20000,
          name: "<PERSON><PERSON>",
          categoryId: "drink",
          quantity: 4,
          unitPrice: 5000,
        },
        {
          amount: 15000,
          name: "Cheesecake Slice",
          categoryId: "food",
          quantity: 3,
          unitPrice: 5000,
        },
      ],
    },
    {
      accountId: "revolut",
      amount: -70000,
      payee: "Zalando",
      date: "2024-04-11T13:00:00Z",
      notes: "Spring wardrobe refresh",
      categoryId: "clothing",
      detailsTransactions: [
        {
          amount: 25000,
          name: "T-shirt",
          categoryId: "clothing",
          quantity: 2,
          unitPrice: 12500,
        },
        {
          amount: 45000,
          name: "<PERSON><PERSON>",
          categoryId: "clothing",
          quantity: 1,
          unitPrice: 45000,
        },
      ],
    },
    {
      accountId: "n26",
      amount: 300000,
      payee: "N26 Savings Account",
      date: "2024-04-12T09:00:00Z",
      notes: "Monthly savings transfer",
      categoryId: "savings",
      detailsTransactions: [],
    },
    {
      accountId: "paypal",
      amount: -45000,
      payee: "FitStar Gym Berlin",
      date: "2024-04-13T11:00:00Z",
      notes: "Monthly gym membership fee",
      categoryId: "health",
      detailsTransactions: [],
    },
    {
      accountId: "commerzbank",
      amount: -90000,
      payee: "Amazon.de",
      date: "2024-04-14T19:00:00Z",
      notes: "Smart Home Accessories",
      categoryId: "electronics",
      detailsTransactions: [
        {
          amount: 30000,
          name: "Smart Bulb",
          categoryId: "electronics",
          quantity: 2,
          unitPrice: 15000,
        },
        {
          amount: 60000,
          name: "Smart Plug",
          categoryId: "electronics",
          quantity: 3,
          unitPrice: 20000,
        },
      ],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: 3200000,
      payee: "Tech Solutions GmbH",
      date: "2024-04-01T09:00:00Z",
      notes: "Monthly salary deposit",
      categoryId: "income",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -56000,
      payee: "Edeka Berlin",
      date: "2024-04-02T18:00:00Z",
      notes: "Bi-weekly groceries",
      categoryId: "groceries",
      detailsTransactions: [
        {
          amount: 9000,
          name: "Bread",
          categoryId: "food",
          quantity: 3,
          unitPrice: 3000,
        },
        {
          amount: 12000,
          name: "Milk",
          categoryId: "dairy",
          quantity: 6,
          unitPrice: 2000,
        },
        {
          amount: 35000,
          name: "Fresh Salmon",
          categoryId: "seafood",
          quantity: 2,
          unitPrice: 17500,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -130000,
      payee: "Bar 55 Berlin",
      date: "2024-04-04T20:00:00Z",
      notes: "Drinks and snacks",
      categoryId: "dining",
      detailsTransactions: [
        {
          amount: 45000,
          name: "Cocktail",
          categoryId: "drink",
          quantity: 6,
          unitPrice: 7500,
        },
        {
          amount: 85000,
          name: "Assorted Tapas",
          categoryId: "food",
          quantity: 5,
          unitPrice: 17000,
        },
      ],
    },
    {
      accountId: "n26",
      amount: 450000,
      payee: "Freelancer.com",
      date: "2024-04-05T12:00:00Z",
      notes: "Payment for web development project",
      categoryId: "income",
      detailsTransactions: [],
    },
    {
      accountId: "commerzbank",
      amount: -30000,
      payee: "Spotify",
      date: "2024-04-06T15:00:00Z",
      notes: "Annual premium subscription",
      categoryId: "entertainment",
      detailsTransactions: [],
    },
    {
      accountId: "das_bank",
      amount: -120000,
      payee: "Saturn Berlin",
      date: "2024-04-07T14:30:00Z",
      notes: "New smartphone model",
      categoryId: "electronics",
      detailsTransactions: [
        {
          amount: 120000,
          name: "OnePlus Nord",
          categoryId: "electronics",
          quantity: 1,
          unitPrice: 120000,
        },
      ],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: 250000,
      payee: "Freelance Client",
      date: "2024-04-15T10:00:00Z",
      notes: "Final payment for mobile app project",
      categoryId: "freelance",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -60000,
      payee: "Berlin Water Works",
      date: "2024-04-16T08:00:00Z",
      notes: "Monthly water bill",
      categoryId: "utilities",
      detailsTransactions: [],
    },
    {
      accountId: "paypal",
      amount: -88000,
      payee: "Cineplex Berlin",
      date: "2024-04-17T20:00:00Z",
      notes: "Movie and dinner",
      categoryId: "entertainment",
      detailsTransactions: [
        {
          amount: 28000,
          name: "Movie Tickets",
          categoryId: "entertainment",
          quantity: 2,
          unitPrice: 14000,
        },
        {
          amount: 60000,
          name: "Dinner for Two",
          categoryId: "dining",
          quantity: 2,
          unitPrice: 30000,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -45000,
      payee: "BVG",
      date: "2024-04-18T07:00:00Z",
      notes: "Monthly AB transport ticket",
      categoryId: "transportation",
      detailsTransactions: [],
    },
    {
      accountId: "commerzbank",
      amount: -150000,
      payee: "Airbnb",
      date: "2024-04-19T18:30:00Z",
      notes: "Accommodation in Hamburg",
      categoryId: "travel",
      detailsTransactions: [],
    },
    {
      accountId: "das_bank",
      amount: -220000,
      payee: "Apple Store Berlin",
      date: "2024-04-20T16:00:00Z",
      notes: "Purchase of new MacBook Air",
      categoryId: "electronics",
      detailsTransactions: [
        {
          amount: 220000,
          name: "MacBook Air 13-inch",
          categoryId: "electronics",
          quantity: 1,
          unitPrice: 220000,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -50000,
      payee: "Techniker Krankenkasse",
      date: "2024-04-21T12:00:00Z",
      notes: "Monthly health insurance premium",
      categoryId: "insurance",
      detailsTransactions: [],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: 3400000,
      payee: "Tech Innovations AG",
      date: "2024-04-22T09:00:00Z",
      notes: "Monthly salary deposit",
      categoryId: "income",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -80000,
      payee: "Rewe Berlin",
      date: "2024-04-23T18:30:00Z",
      notes: "Grocery restock for the week",
      categoryId: "groceries",
      detailsTransactions: [
        {
          amount: 20000,
          name: "Organic Vegetables",
          categoryId: "food",
          quantity: 5,
          unitPrice: 4000,
        },
        {
          amount: 30000,
          name: "Fresh Fish",
          categoryId: "seafood",
          quantity: 2,
          unitPrice: 15000,
        },
        {
          amount: 30000,
          name: "Craft Beer",
          categoryId: "drink",
          quantity: 6,
          unitPrice: 5000,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -90000,
      payee: "Klunkerkranich Berlin",
      date: "2024-04-24T20:00:00Z",
      notes: "Birthday celebration at rooftop bar",
      categoryId: "entertainment",
      detailsTransactions: [
        {
          amount: 60000,
          name: "Cocktails",
          categoryId: "drink",
          quantity: 6,
          unitPrice: 10000,
        },
        {
          amount: 30000,
          name: "Snacks",
          categoryId: "food",
          quantity: 3,
          unitPrice: 10000,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -65000,
      payee: "Decathlon Berlin",
      date: "2024-04-25T15:00:00Z",
      notes: "New running shoes and fitness gear",
      categoryId: "health",
      detailsTransactions: [
        {
          amount: 50000,
          name: "Running Shoes",
          categoryId: "health",
          quantity: 1,
          unitPrice: 50000,
        },
        {
          amount: 15000,
          name: "Gym T-shirt",
          categoryId: "health",
          quantity: 3,
          unitPrice: 5000,
        },
      ],
    },
    {
      accountId: "commerzbank",
      amount: -20000,
      payee: "Five Elephant Berlin",
      date: "2024-04-26T11:30:00Z",
      notes: "Weekly coffee treat",
      categoryId: "dining",
      detailsTransactions: [
        {
          amount: 20000,
          name: "Flat Whites",
          categoryId: "drink",
          quantity: 4,
          unitPrice: 5000,
        },
      ],
    },
    {
      accountId: "das_bank",
      amount: -120000,
      payee: "DB Bahn",
      date: "2024-04-27T08:00:00Z",
      notes: "Train tickets for a trip to Leipzig",
      categoryId: "travel",
      detailsTransactions: [],
    },
    {
      accountId: "paypal",
      amount: -45000,
      payee: "Fressnapf Berlin",
      date: "2024-04-28T14:00:00Z",
      notes: "Cat food and toys",
      categoryId: "pets",
      detailsTransactions: [
        {
          amount: 30000,
          name: "Premium Cat Food",
          categoryId: "pets",
          quantity: 10,
          unitPrice: 3000,
        },
        {
          amount: 15000,
          name: "Cat Toys",
          categoryId: "pets",
          quantity: 3,
          unitPrice: 5000,
        },
      ],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: -24000,
      payee: "Dussmann das KulturKaufhaus",
      date: "2024-04-29T13:00:00Z",
      notes: "Technical books for continuous learning",
      categoryId: "education",
      detailsTransactions: [
        {
          amount: 24000,
          name: "Software Development Books",
          categoryId: "education",
          quantity: 2,
          unitPrice: 12000,
        },
      ],
    },
    {
      accountId: "revolut",
      amount: -32000,
      payee: "Barber Shop Berlin",
      date: "2024-04-30T17:30:00Z",
      notes: "Haircut and beard trim",
      categoryId: "personal care",
      detailsTransactions: [
        {
          amount: 32000,
          name: "Haircut and Beard Trim",
          categoryId: "personal care",
          quantity: 1,
          unitPrice: 32000,
        },
      ],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: 3300000,
      payee: "Innovative Solutions GmbH",
      date: "2024-05-01T09:00:00Z",
      notes: "Monthly salary deposit",
      categoryId: "income",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -97000,
      payee: "Bauhaus Berlin",
      date: "2024-05-02T14:00:00Z",
      notes: "Materials for DIY home projects",
      categoryId: "household items",
      detailsTransactions: [
        {
          amount: 30000,
          name: "Paint",
          categoryId: "household items",
          quantity: 3,
          unitPrice: 10000,
        },
        {
          amount: 67000,
          name: "Power Drill",
          categoryId: "household items",
          quantity: 1,
          unitPrice: 67000,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -45000,
      payee: "Mercedes-Benz Arena Berlin",
      date: "2024-05-03T20:00:00Z",
      notes: "Tickets to see a favorite band live",
      categoryId: "entertainment",
      detailsTransactions: [
        {
          amount: 45000,
          name: "Concert Tickets",
          categoryId: "entertainment",
          quantity: 2,
          unitPrice: 22500,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -34000,
      payee: "Berlin Coffee Roasters",
      date: "2024-05-04T11:00:00Z",
      notes: "Purchase of premium coffee beans",
      categoryId: "groceries",
      detailsTransactions: [
        {
          amount: 34000,
          name: "Ethiopian Yirgacheffe Beans",
          categoryId: "groceries",
          quantity: 2,
          unitPrice: 17000,
        },
      ],
    },
    {
      accountId: "commerzbank",
      amount: -60000,
      payee: "Tierarztpraxis Berlin",
      date: "2024-05-05T16:00:00Z",
      notes: "Regular check-up for the cat",
      categoryId: "pets",
      detailsTransactions: [
        {
          amount: 60000,
          name: "Veterinary Services",
          categoryId: "pets",
          quantity: 1,
          unitPrice: 60000,
        },
      ],
    },
    {
      accountId: "das_bank",
      amount: -130000,
      payee: "Apple Store Berlin",
      date: "2024-05-06T12:30:00Z",
      notes: "Accessories for the new iPhone",
      categoryId: "electronics",
      detailsTransactions: [
        {
          amount: 80000,
          name: "Leather Phone Case",
          categoryId: "electronics",
          quantity: 1,
          unitPrice: 80000,
        },
        {
          amount: 50000,
          name: "Wireless Charger",
          categoryId: "electronics",
          quantity: 1,
          unitPrice: 50000,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -40000,
      payee: "Doctors Without Borders",
      date: "2024-05-07T10:00:00Z",
      notes: "Monthly donation to support global health initiatives",
      categoryId: "charity",
      detailsTransactions: [],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: 250000,
      payee: "Remote Tech Solutions",
      date: "2024-05-08T10:00:00Z",
      notes: "Payment for web development work",
      categoryId: "freelance",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -42000,
      payee: "Kreuzberg Market",
      date: "2024-05-09T11:00:00Z",
      notes: "Organic fruits and vegetables",
      categoryId: "groceries",
      detailsTransactions: [
        {
          amount: 18000,
          name: "Organic Tomatoes",
          categoryId: "groceries",
          quantity: 3,
          unitPrice: 6000,
        },
        {
          amount: 24000,
          name: "Fresh Berries Mix",
          categoryId: "groceries",
          quantity: 2,
          unitPrice: 12000,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -300000,
      payee: "N26 Savings Account",
      date: "2024-05-10T09:00:00Z",
      notes: "Regular savings deposit",
      categoryId: "savings",
      detailsTransactions: [],
    },
    {
      accountId: "paypal",
      amount: -100000,
      payee: "MediaMarkt",
      date: "2024-05-11T17:00:00Z",
      notes: "New router and accessories",
      categoryId: "electronics",
      detailsTransactions: [
        {
          amount: 80000,
          name: "Wi-Fi Router",
          categoryId: "electronics",
          quantity: 1,
          unitPrice: 80000,
        },
        {
          amount: 20000,
          name: "Ethernet Cables",
          categoryId: "electronics",
          quantity: 4,
          unitPrice: 5000,
        },
      ],
    },
    {
      accountId: "commerzbank",
      amount: -69000,
      payee: "Prater Garten",
      date: "2024-05-12T19:30:00Z",
      notes: "Dinner and drinks",
      categoryId: "dining",
      detailsTransactions: [
        {
          amount: 45000,
          name: "Main Courses",
          categoryId: "dining",
          quantity: 3,
          unitPrice: 15000,
        },
        {
          amount: 24000,
          name: "Craft Beers",
          categoryId: "drink",
          quantity: 4,
          unitPrice: 6000,
        },
      ],
    },
    {
      accountId: "das_bank",
      amount: -80000,
      payee: "Ikea Berlin",
      date: "2024-05-13T15:00:00Z",
      notes: "New furnishings and decorations",
      categoryId: "household items",
      detailsTransactions: [
        {
          amount: 40000,
          name: "Table Lamp",
          categoryId: "household items",
          quantity: 2,
          unitPrice: 20000,
        },
        {
          amount: 40000,
          name: "Decorative Cushions",
          categoryId: "household items",
          quantity: 4,
          unitPrice: 10000,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -25000,
      payee: "Urban Sports Club",
      date: "2024-05-14T08:00:00Z",
      notes: "Monthly gym membership",
      categoryId: "health",
      detailsTransactions: [],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: -43000,
      payee: "Schönheitssalon Berlin",
      date: "2024-05-15T14:00:00Z",
      notes: "Hair styling and treatment",
      categoryId: "personal care",
      detailsTransactions: [
        {
          amount: 43000,
          name: "Hair Styling and Treatment",
          categoryId: "personal care",
          quantity: 1,
          unitPrice: 43000,
        },
      ],
    },
    {
      accountId: "revolut",
      amount: -32000,
      payee: "Italian Deli Berlin",
      date: "2024-05-16T13:00:00Z",
      notes: "Italian cheeses and cured meats",
      categoryId: "groceries",
      detailsTransactions: [
        {
          amount: 12000,
          name: "Parmigiano Reggiano",
          categoryId: "groceries",
          quantity: 1,
          unitPrice: 12000,
        },
        {
          amount: 20000,
          name: "Prosciutto di Parma",
          categoryId: "groceries",
          quantity: 1,
          unitPrice: 20000,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -150000,
      payee: "Hotel Adlon Berlin",
      date: "2024-05-17T19:00:00Z",
      notes: "Weekend staycation in Berlin",
      categoryId: "travel",
      detailsTransactions: [],
    },
    {
      accountId: "paypal",
      amount: -45000,
      payee: "Fit Berlin Academy",
      date: "2024-05-18T09:30:00Z",
      notes: "Participation in a fitness workshop",
      categoryId: "health",
      detailsTransactions: [],
    },
    {
      accountId: "commerzbank",
      amount: -90000,
      payee: "Hornbach Garden Center",
      date: "2024-05-19T11:00:00Z",
      notes: "Plants and garden tools for spring",
      categoryId: "household items",
      detailsTransactions: [
        {
          amount: 45000,
          name: "Garden Tools Set",
          categoryId: "household items",
          quantity: 1,
          unitPrice: 45000,
        },
        {
          amount: 45000,
          name: "Assorted Outdoor Plants",
          categoryId: "household items",
          quantity: 6,
          unitPrice: 7500,
        },
      ],
    },
    {
      accountId: "das_bank",
      amount: -27000,
      payee: "Berlin Art Store",
      date: "2024-05-20T16:00:00Z",
      notes: "Materials for creative projects",
      categoryId: "education",
      detailsTransactions: [
        {
          amount: 27000,
          name: "Watercolor Set and Brushes",
          categoryId: "education",
          quantity: 1,
          unitPrice: 27000,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -23000,
      payee: "Steam",
      date: "2024-05-21T20:00:00Z",
      notes: "Purchase of new video games",
      categoryId: "entertainment",
      detailsTransactions: [
        {
          amount: 23000,
          name: "Video Games",
          categoryId: "entertainment",
          quantity: 2,
          unitPrice: 11500,
        },
      ],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: -22000,
      payee: "Vodafone Berlin",
      date: "2024-05-22T09:00:00Z",
      notes: "Monthly broadband subscription",
      categoryId: "utilities",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -58000,
      payee: "Berlin Brew Fest",
      date: "2024-05-23T15:00:00Z",
      notes: "Entry tickets and beers",
      categoryId: "entertainment",
      detailsTransactions: [
        {
          amount: 58000,
          name: "Festival Entry and Craft Beer Tasting",
          categoryId: "entertainment",
          quantity: 2,
          unitPrice: 29000,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -76000,
      payee: "Autohaus Berlin",
      date: "2024-05-24T12:00:00Z",
      notes: "Unexpected car maintenance",
      categoryId: "transportation",
      detailsTransactions: [
        {
          amount: 76000,
          name: "Oil Change and Brake Inspection",
          categoryId: "transportation",
          quantity: 1,
          unitPrice: 76000,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -35000,
      payee: "Das Lokal Berlin",
      date: "2024-05-25T19:00:00Z",
      notes: "Team dinner event",
      categoryId: "dining",
      detailsTransactions: [
        {
          amount: 35000,
          name: "Meals and Drinks",
          categoryId: "dining",
          quantity: 3,
          unitPrice: 11667,
        },
      ],
    },
    {
      accountId: "commerzbank",
      amount: -89000,
      payee: "IKEA Berlin",
      date: "2024-05-26T10:30:00Z",
      notes: "Ergonomic office chair for home workspace",
      categoryId: "household items",
      detailsTransactions: [
        {
          amount: 89000,
          name: "Ergonomic Office Chair",
          categoryId: "household items",
          quantity: 1,
          unitPrice: 89000,
        },
      ],
    },
    {
      accountId: "das_bank",
      amount: -50000,
      payee: "Mauerpark Flea Market",
      date: "2024-05-27T11:45:00Z",
      notes: "Vintage clothes and handmade jewelry",
      categoryId: "clothing",
      detailsTransactions: [
        {
          amount: 50000,
          name: "Assorted Vintage Clothes and Accessories",
          categoryId: "clothing",
          quantity: 5,
          unitPrice: 10000,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -15000,
      payee: "Bonanza Coffee Berlin",
      date: "2024-05-28T08:00:00Z",
      notes: "Weekly specialty coffee treat",
      categoryId: "dining",
      detailsTransactions: [
        {
          amount: 15000,
          name: "Specialty Coffee",
          categoryId: "dining",
          quantity: 3,
          unitPrice: 5000,
        },
      ],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: 300000,
      payee: "N26 Savings Account",
      date: "2024-05-29T09:00:00Z",
      notes: "End-of-month savings transfer",
      categoryId: "savings",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -125000,
      payee: "Berlin Property Management",
      date: "2024-05-30T10:00:00Z",
      notes: "Monthly rent for apartment",
      categoryId: "housing",
      detailsTransactions: [],
    },
    {
      accountId: "paypal",
      amount: -40000,
      payee: "Telekom Deutschland",
      date: "2024-05-31T15:00:00Z",
      notes: "Mobile phone and data plan",
      categoryId: "utilities",
      detailsTransactions: [],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: 3300000,
      payee: "Tech Innovations AG",
      date: "2024-06-01T09:00:00Z",
      notes: "Monthly salary deposit",
      categoryId: "income",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -185000,
      payee: "Lufthansa",
      date: "2024-06-02T11:00:00Z",
      notes: "Round-trip tickets for Bangkok",
      categoryId: "travel",
      detailsTransactions: [],
    },
    {
      accountId: "n26",
      amount: -120000,
      payee: "Allianz Global Assistance",
      date: "2024-06-03T13:00:00Z",
      notes: "Comprehensive travel insurance for Thailand trip",
      categoryId: "insurance",
      detailsTransactions: [],
    },
    {
      accountId: "paypal",
      amount: -95000,
      payee: "Decathlon Berlin",
      date: "2024-06-04T16:30:00Z",
      notes: "New travel backpack and accessories",
      categoryId: "clothing",
      detailsTransactions: [
        {
          amount: 60000,
          name: "Travel Backpack",
          categoryId: "clothing",
          quantity: 1,
          unitPrice: 60000,
        },
        {
          amount: 35000,
          name: "Travel Accessories",
          categoryId: "clothing",
          quantity: 3,
          unitPrice: 11667,
        },
      ],
    },
    {
      accountId: "commerzbank",
      amount: -70000,
      payee: "Berlin Health Clinic",
      date: "2024-06-05T10:00:00Z",
      notes: "Travel vaccinations for Southeast Asia",
      categoryId: "health",
      detailsTransactions: [],
    },
    {
      accountId: "das_bank",
      amount: -45000,
      payee: "Revolut",
      date: "2024-06-06T09:00:00Z",
      notes: "Exchange EUR to THB for travel spending",
      categoryId: "travel",
      detailsTransactions: [],
    },
    {
      accountId: "paypal",
      amount: -50000,
      payee: "Airbnb",
      date: "2024-06-07T14:00:00Z",
      notes: "Initial deposit for Bangkok accommodation",
      categoryId: "travel",
      detailsTransactions: [],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: -300000,
      payee: "N26 Savings Account",
      date: "2024-06-08T09:00:00Z",
      notes: "Regular monthly savings",
      categoryId: "savings",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -60000,
      payee: "Local Berlin Restaurant",
      date: "2024-06-09T19:30:00Z",
      notes: "Dinner with friends",
      categoryId: "dining",
      detailsTransactions: [
        {
          amount: 60000,
          name: "Dinner for two",
          categoryId: "dining",
          quantity: 2,
          unitPrice: 30000,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -85000,
      payee: "Berlin Spa & Wellness",
      date: "2024-06-10T15:00:00Z",
      notes: "Spa day before travel",
      categoryId: "health",
      detailsTransactions: [],
    },
    {
      accountId: "paypal",
      amount: -45000,
      payee: "Berlin Pet Sitters",
      date: "2024-06-11T08:00:00Z",
      notes: "Pet sitting services during travel",
      categoryId: "pets",
      detailsTransactions: [],
    },
    {
      accountId: "commerzbank",
      amount: -150000,
      payee: "RentalCars.com",
      date: "2024-06-12T12:00:00Z",
      notes: "Rental car booking for Bangkok trip",
      categoryId: "travel",
      detailsTransactions: [],
    },
    {
      accountId: "das_bank",
      amount: -74000,
      payee: "Berlin Travel Shop",
      date: "2024-06-13T14:00:00Z",
      notes: "Purchase of sunblock, insect repellent, and travel guides",
      categoryId: "travel",
      detailsTransactions: [
        {
          amount: 30000,
          name: "Sunblock",
          categoryId: "health",
          quantity: 2,
          unitPrice: 15000,
        },
        {
          amount: 20000,
          name: "Insect Repellent",
          categoryId: "health",
          quantity: 2,
          unitPrice: 10000,
        },
        {
          amount: 24000,
          name: "Travel Guides",
          categoryId: "education",
          quantity: 2,
          unitPrice: 12000,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -25000,
      payee: "Berlin Energy",
      date: "2024-06-14T10:00:00Z",
      notes: "Monthly utilities",
      categoryId: "utilities",
      detailsTransactions: [],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: -110000,
      payee: "Expedia",
      date: "2024-06-15T09:00:00Z",
      notes: "Final payment for hotel in Bangkok",
      categoryId: "travel",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -65000,
      payee: "ALDI Nord Berlin",
      date: "2024-06-16T17:00:00Z",
      notes: "Stocking up on home essentials before travel",
      categoryId: "groceries",
      detailsTransactions: [
        {
          amount: 20000,
          name: "Fresh Produce",
          categoryId: "groceries",
          quantity: 4,
          unitPrice: 5000,
        },
        {
          amount: 45000,
          name: "Bulk Snacks",
          categoryId: "groceries",
          quantity: 5,
          unitPrice: 9000,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -120000,
      payee: "Museum Island Berlin",
      date: "2024-06-17T12:30:00Z",
      notes: "Tickets for exhibitions and events",
      categoryId: "entertainment",
      detailsTransactions: [
        {
          amount: 120000,
          name: "Museum Passes",
          categoryId: "entertainment",
          quantity: 4,
          unitPrice: 30000,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -18000,
      payee: "Vodafone",
      date: "2024-06-18T08:00:00Z",
      notes: "International roaming package",
      categoryId: "utilities",
      detailsTransactions: [],
    },
    {
      accountId: "commerzbank",
      amount: -50000,
      payee: "KaDeWe Berlin",
      date: "2024-06-19T15:00:00Z",
      notes: "Gifts to bring to hosts in Thailand",
      categoryId: "gifts",
      detailsTransactions: [
        {
          amount: 50000,
          name: "Assorted German Chocolates",
          categoryId: "gifts",
          quantity: 10,
          unitPrice: 5000,
        },
      ],
    },
    {
      accountId: "das_bank",
      amount: -45000,
      payee: "Berlin Medical Center",
      date: "2024-06-20T10:30:00Z",
      notes: "General health checkup before international travel",
      categoryId: "health",
      detailsTransactions: [],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: -80000,
      payee: "Globetrotter Ausrüstung",
      date: "2024-06-21T14:00:00Z",
      notes: "New travel luggage for extended trips",
      categoryId: "travel",
      detailsTransactions: [
        {
          amount: 80000,
          name: "Durable Travel Suitcase",
          categoryId: "travel",
          quantity: 1,
          unitPrice: 80000,
        },
      ],
    },
    {
      accountId: "revolut",
      amount: -24000,
      payee: "Burgermeister Berlin",
      date: "2024-06-22T18:30:00Z",
      notes: "Quick dinner out",
      categoryId: "dining",
      detailsTransactions: [
        {
          amount: 24000,
          name: "Burgers and Fries",
          categoryId: "dining",
          quantity: 2,
          unitPrice: 12000,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -32000,
      payee: "Rossmann Berlin",
      date: "2024-06-23T12:00:00Z",
      notes: "Travel toiletries and first-aid supplies",
      categoryId: "health",
      detailsTransactions: [
        {
          amount: 32000,
          name: "Travel Health Kit",
          categoryId: "health",
          quantity: 1,
          unitPrice: 32000,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -50000,
      payee: "Uber",
      date: "2024-06-24T06:00:00Z",
      notes: "Uber ride to and from Berlin Airport",
      categoryId: "transportation",
      detailsTransactions: [
        {
          amount: 50000,
          name: "Round Trip Airport Transfer",
          categoryId: "transportation",
          quantity: 2,
          unitPrice: 25000,
        },
      ],
    },
    {
      accountId: "commerzbank",
      amount: -150000,
      payee: "Booking.com",
      date: "2024-06-25T10:00:00Z",
      notes: "Advance payment for the first week's stay",
      categoryId: "travel",
      detailsTransactions: [],
    },
    {
      accountId: "das_bank",
      amount: -25000,
      payee: "Thalia Berlin",
      date: "2024-06-26T16:30:00Z",
      notes: "Novels and travel guides for the trip",
      categoryId: "education",
      detailsTransactions: [
        {
          amount: 25000,
          name: "Travel and Leisure Reading",
          categoryId: "education",
          quantity: 3,
          unitPrice: 8333,
        },
      ],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: 450000,
      payee: "Freelance Client",
      date: "2024-06-27T10:00:00Z",
      notes: "Payment for freelance software development project",
      categoryId: "freelance",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -80000,
      payee: "Globetrotter Ausrüstung",
      date: "2024-06-21T14:00:00Z",
      notes: "New travel luggage for extended trips",
      categoryId: "travel",
      detailsTransactions: [
        {
          amount: 80000,
          name: "Durable Travel Suitcase",
          categoryId: "travel",
          quantity: 1,
          unitPrice: 80000,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -32000,
      payee: "Rossmann Berlin",
      date: "2024-06-23T12:00:00Z",
      notes: "Travel toiletries and first-aid supplies",
      categoryId: "health",
      detailsTransactions: [
        {
          amount: 32000,
          name: "Travel Health Kit",
          categoryId: "health",
          quantity: 1,
          unitPrice: 32000,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -50000,
      payee: "Uber",
      date: "2024-06-24T06:00:00Z",
      notes: "Uber ride to and from Berlin Airport",
      categoryId: "transportation",
      detailsTransactions: [
        {
          amount: 50000,
          name: "Round Trip Airport Transfer",
          categoryId: "transportation",
          quantity: 2,
          unitPrice: 25000,
        },
      ],
    },
    {
      accountId: "commerzbank",
      amount: -150000,
      payee: "Airbnb",
      date: "2024-06-28T10:00:00Z",
      notes: "Final payment for Bangkok accommodation",
      categoryId: "travel",
      detailsTransactions: [],
    },
    {
      accountId: "das_bank",
      amount: -25000,
      payee: "Thalia Berlin",
      date: "2024-06-26T16:30:00Z",
      notes: "Novels and travel guides for the trip",
      categoryId: "education",
      detailsTransactions: [
        {
          amount: 25000,
          name: "Travel and Leisure Reading",
          categoryId: "education",
          quantity: 3,
          unitPrice: 8333,
        },
      ],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: -120000,
      payee: "Bangkok Historical Sites",
      date: "2024-07-01T10:00:00Z",
      notes: "Tickets to Grand Palace and Wat Arun",
      categoryId: "travel",
      detailsTransactions: [
        {
          amount: 70000,
          name: "Grand Palace Entry",
          categoryId: "travel",
          quantity: 2,
          unitPrice: 35000,
        },
        {
          amount: 50000,
          name: "Wat Arun Tickets",
          categoryId: "travel",
          quantity: 2,
          unitPrice: 25000,
        },
      ],
    },
    {
      accountId: "revolut",
      amount: -55000,
      payee: "Bangkok Street Food Vendors",
      date: "2024-07-02T19:00:00Z",
      notes: "Exploring local cuisine with a guided tour",
      categoryId: "dining",
      detailsTransactions: [
        {
          amount: 55000,
          name: "Street Food Sampling",
          categoryId: "dining",
          quantity: 2,
          unitPrice: 27500,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -65000,
      payee: "Luxury Spa Bangkok",
      date: "2024-07-03T15:00:00Z",
      notes: "Relaxation day at a well-known spa",
      categoryId: "health",
      detailsTransactions: [
        {
          amount: 65000,
          name: "Full Body Massage and Spa Treatments",
          categoryId: "health",
          quantity: 1,
          unitPrice: 65000,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -30000,
      payee: "Bangkok Mobile Provider",
      date: "2024-07-04T12:00:00Z",
      notes: "Purchasing local SIM cards for easier communication",
      categoryId: "utilities",
      detailsTransactions: [
        {
          amount: 30000,
          name: "2 Local SIM Cards",
          categoryId: "utilities",
          quantity: 2,
          unitPrice: 15000,
        },
      ],
    },
    {
      accountId: "commerzbank",
      amount: -85000,
      payee: "Chatuchak Weekend Market",
      date: "2024-07-05T10:30:00Z",
      notes: "Shopping for souvenirs and local handicrafts",
      categoryId: "gifts",
      detailsTransactions: [
        {
          amount: 85000,
          name: "Assorted Souvenirs and Handicrafts",
          categoryId: "gifts",
          quantity: 10,
          unitPrice: 8500,
        },
      ],
    },
    {
      accountId: "das_bank",
      amount: -40000,
      payee: "Bangkok Transit Authority",
      date: "2024-07-06T08:00:00Z",
      notes: "Weekly passes for BTS Skytrain and MRT",
      categoryId: "transportation",
      detailsTransactions: [
        {
          amount: 40000,
          name: "Public Transit Weekly Passes",
          categoryId: "transportation",
          quantity: 2,
          unitPrice: 20000,
        },
      ],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: -90000,
      payee: "Thai Culinary School",
      date: "2024-07-07T10:00:00Z",
      notes: "Participation in a traditional Thai cooking class",
      categoryId: "education",
      detailsTransactions: [
        {
          amount: 90000,
          name: "Thai Cooking Class for Two",
          categoryId: "education",
          quantity: 2,
          unitPrice: 45000,
        },
      ],
    },
    {
      accountId: "revolut",
      amount: -70000,
      payee: "Koh Kret Tour",
      date: "2024-07-08T08:30:00Z",
      notes: "Day trip to Koh Kret island with a guided tour",
      categoryId: "travel",
      detailsTransactions: [
        {
          amount: 70000,
          name: "Day Tour Ticket",
          categoryId: "travel",
          quantity: 2,
          unitPrice: 35000,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -50000,
      payee: "Asiatique Night Market",
      date: "2024-07-09T19:00:00Z",
      notes: "Dining and snacks at Asiatique Night Market",
      categoryId: "dining",
      detailsTransactions: [
        {
          amount: 50000,
          name: "Assorted Street Food",
          categoryId: "dining",
          quantity: 5,
          unitPrice: 10000,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -40000,
      payee: "Chiang Mai Elephant Sanctuary",
      date: "2024-07-10T06:00:00Z",
      notes: "Visit to elephant sanctuary including transportation",
      categoryId: "travel",
      detailsTransactions: [
        {
          amount: 40000,
          name: "Sanctuary Entry and Transport",
          categoryId: "travel",
          quantity: 2,
          unitPrice: 20000,
        },
      ],
    },
    {
      accountId: "commerzbank",
      amount: -60000,
      payee: "Wat Pho Massage School",
      date: "2024-07-11T14:00:00Z",
      notes: "Traditional Thai massage sessions",
      categoryId: "health",
      detailsTransactions: [
        {
          amount: 60000,
          name: "Thai Massage",
          categoryId: "health",
          quantity: 2,
          unitPrice: 30000,
        },
      ],
    },
    {
      accountId: "das_bank",
      amount: -30000,
      payee: "MBK Center",
      date: "2024-07-12T16:00:00Z",
      notes: "Shopping for souvenirs at MBK shopping center",
      categoryId: "gifts",
      detailsTransactions: [
        {
          amount: 30000,
          name: "Local Handicrafts and Souvenirs",
          categoryId: "gifts",
          quantity: 6,
          unitPrice: 5000,
        },
      ],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: -45000,
      payee: "Damnoen Saduak Floating Market",
      date: "2024-07-13T07:30:00Z",
      notes: "Boat tour and local shopping at the floating market",
      categoryId: "travel",
      detailsTransactions: [
        {
          amount: 45000,
          name: "Floating Market Boat Tour",
          categoryId: "travel",
          quantity: 2,
          unitPrice: 22500,
        },
      ],
    },
    {
      accountId: "revolut",
      amount: -30000,
      payee: "Gaggan Restaurant",
      date: "2024-07-14T19:00:00Z",
      notes: "Farewell dinner at a renowned restaurant",
      categoryId: "dining",
      detailsTransactions: [
        {
          amount: 30000,
          name: "Innovative Thai Tasting Menu",
          categoryId: "dining",
          quantity: 1,
          unitPrice: 30000,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -35000,
      payee: "Siam Paragon",
      date: "2024-07-15T15:00:00Z",
      notes: "Last minute gifts and souvenirs",
      categoryId: "gifts",
      detailsTransactions: [
        {
          amount: 35000,
          name: "Assorted Thai Crafts",
          categoryId: "gifts",
          quantity: 5,
          unitPrice: 7000,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -25000,
      payee: "Grab Taxi",
      date: "2024-07-16T05:00:00Z",
      notes: "Transport to Bangkok International Airport",
      categoryId: "transportation",
      detailsTransactions: [
        {
          amount: 25000,
          name: "Airport Drop-off Service",
          categoryId: "transportation",
          quantity: 1,
          unitPrice: 25000,
        },
      ],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: 3400000,
      payee: "Tech Innovations AG",
      date: "2024-07-17T09:00:00Z",
      notes: "Monthly salary deposit after returning from vacation",
      categoryId: "income",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -45000,
      payee: "REWE Berlin",
      date: "2024-07-18T18:00:00Z",
      notes: "Restocking fresh groceries after the trip",
      categoryId: "groceries",
      detailsTransactions: [
        {
          amount: 45000,
          name: "Fresh Produce and Essentials",
          categoryId: "groceries",
          quantity: 10,
          unitPrice: 4500,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -60000,
      payee: "Clean & Go Berlin",
      date: "2024-07-19T10:30:00Z",
      notes: "Laundry and dry cleaning of clothes from the trip",
      categoryId: "personal care",
      detailsTransactions: [],
    },
    {
      accountId: "paypal",
      amount: -32000,
      payee: "BVG",
      date: "2024-07-20T08:00:00Z",
      notes: "Monthly pass for public transportation",
      categoryId: "transportation",
      detailsTransactions: [],
    },
    {
      accountId: "commerzbank",
      amount: -70000,
      payee: "Zen Spa Berlin",
      date: "2024-07-21T14:00:00Z",
      notes: "Relaxation massage to ease back into daily life",
      categoryId: "health",
      detailsTransactions: [],
    },
    {
      accountId: "das_bank",
      amount: -25000,
      payee: "Foto Kiosk Berlin",
      date: "2024-07-22T16:00:00Z",
      notes: "Printing photos from the Bangkok trip",
      categoryId: "entertainment",
      detailsTransactions: [],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: -150000,
      payee: "Coursera",
      date: "2024-07-23T09:00:00Z",
      notes: "Enrollment in a professional development course on data science",
      categoryId: "education",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -38000,
      payee: "Amazon.de",
      date: "2024-07-24T18:00:00Z",
      notes: "Purchase of books on software development and productivity",
      categoryId: "education",
      detailsTransactions: [
        {
          amount: 38000,
          name: "Books on Software Development",
          categoryId: "education",
          quantity: 2,
          unitPrice: 19000,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -49000,
      payee: "Berlin Coffee Club",
      date: "2024-07-25T10:30:00Z",
      notes: "Monthly subscription for specialty coffee beans delivered home",
      categoryId: "groceries",
      detailsTransactions: [],
    },
    {
      accountId: "paypal",
      amount: -27000,
      payee: "Netflix",
      date: "2024-07-26T08:00:00Z",
      notes: "Monthly Netflix subscription fee",
      categoryId: "entertainment",
      detailsTransactions: [],
    },
    {
      accountId: "commerzbank",
      amount: -80000,
      payee: "Philips Hue Online Store",
      date: "2024-07-27T14:00:00Z",
      notes: "Purchase of smart lighting solutions for home automation",
      categoryId: "electronics",
      detailsTransactions: [
        {
          amount: 80000,
          name: "Smart Light Bulbs Kit",
          categoryId: "electronics",
          quantity: 4,
          unitPrice: 20000,
        },
      ],
    },
    {
      accountId: "das_bank",
      amount: -55000,
      payee: "ArtStore.de",
      date: "2024-07-28T16:00:00Z",
      notes: "Art supplies for personal projects",
      categoryId: "household items",
      detailsTransactions: [
        {
          amount: 55000,
          name: "Watercolor Paints and Brushes",
          categoryId: "household items",
          quantity: 3,
          unitPrice: 18333,
        },
      ],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: 3200000,
      payee: "Innovative Solutions GmbH",
      date: "2024-07-29T09:00:00Z",
      notes: "Monthly salary payment",
      categoryId: "income",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -85000,
      payee: "Airbnb",
      date: "2024-07-30T12:00:00Z",
      notes: "Booking a weekend getaway to the Baltic Sea",
      categoryId: "travel",
      detailsTransactions: [],
    },
    {
      accountId: "n26",
      amount: -46000,
      payee: "Zalando",
      date: "2024-07-31T16:30:00Z",
      notes: "Birthday gift purchase for a friend",
      categoryId: "gifts",
      detailsTransactions: [],
    },
    {
      accountId: "paypal",
      amount: -35000,
      payee: "Eventim",
      date: "2024-08-01T20:00:00Z",
      notes: "Tickets for an upcoming concert",
      categoryId: "entertainment",
      detailsTransactions: [],
    },
    {
      accountId: "commerzbank",
      amount: -78000,
      payee: "Apple Online Store",
      date: "2024-08-02T11:00:00Z",
      notes: "Purchase of the latest iPhone model",
      categoryId: "electronics",
      detailsTransactions: [],
    },
    {
      accountId: "das_bank",
      amount: -24000,
      payee: "Bauhaus",
      date: "2024-08-03T14:30:00Z",
      notes: "Supplies for home gardening projects",
      categoryId: "household items",
      detailsTransactions: [
        {
          amount: 24000,
          name: "Plants and Gardening Tools",
          categoryId: "household items",
          quantity: 4,
          unitPrice: 6000,
        },
      ],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: -120000,
      payee: "Berlin Tech Conference",
      date: "2024-08-04T09:00:00Z",
      notes: "Registration fee for a weekend workshop on emerging technologies",
      categoryId: "education",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -52000,
      payee: "Kochhaus Berlin",
      date: "2024-08-05T12:30:00Z",
      notes: "Purchase of a high-end blender for home use",
      categoryId: "household items",
      detailsTransactions: [],
    },
    {
      accountId: "n26",
      amount: -29000,
      payee: "FitX Berlin",
      date: "2024-08-06T17:00:00Z",
      notes: "Monthly membership renewal at local gym",
      categoryId: "health",
      detailsTransactions: [],
    },
    {
      accountId: "paypal",
      amount: -34000,
      payee: "Fressnapf",
      date: "2024-08-07T15:00:00Z",
      notes: "Monthly supply of pet food and toys",
      categoryId: "pets",
      detailsTransactions: [
        {
          amount: 34000,
          name: "Premium Cat Food and Toys",
          categoryId: "pets",
          quantity: 5,
          unitPrice: 6800,
        },
      ],
    },
    {
      accountId: "commerzbank",
      amount: -65000,
      payee: "Restaurant Amrit Berlin",
      date: "2024-08-08T20:00:00Z",
      notes: "Dinner with friends at an Indian restaurant",
      categoryId: "dining",
      detailsTransactions: [
        {
          amount: 65000,
          name: "Indian Cuisine Dinner",
          categoryId: "dining",
          quantity: 3,
          unitPrice: 21667,
        },
      ],
    },
    {
      accountId: "das_bank",
      amount: -43000,
      payee: "Cinestar Berlin",
      date: "2024-08-09T18:00:00Z",
      notes: "Tickets and snacks for a movie night",
      categoryId: "entertainment",
      detailsTransactions: [
        {
          amount: 43000,
          name: "Movie Tickets and Concessions",
          categoryId: "entertainment",
          quantity: 2,
          unitPrice: 21500,
        },
      ],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: 500000,
      payee: "Upwork",
      date: "2024-08-10T09:00:00Z",
      notes: "Payment received for freelance software project",
      categoryId: "freelance",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -150000,
      payee: "E*TRADE",
      date: "2024-08-11T12:00:00Z",
      notes: "Monthly investment in technology stocks",
      categoryId: "investments",
      detailsTransactions: [],
    },
    {
      accountId: "n26",
      amount: -80000,
      payee: "Auto Service Berlin",
      date: "2024-08-12T14:30:00Z",
      notes: "Routine car service and oil change",
      categoryId: "transportation",
      detailsTransactions: [],
    },
    {
      accountId: "paypal",
      amount: -60000,
      payee: "Obi Baumarkt",
      date: "2024-08-13T10:30:00Z",
      notes: "Materials for home DIY projects",
      categoryId: "household items",
      detailsTransactions: [
        {
          amount: 60000,
          name: "Paint, Brushes, and Repair Tools",
          categoryId: "household items",
          quantity: 4,
          unitPrice: 15000,
        },
      ],
    },
    {
      accountId: "commerzbank",
      amount: -22000,
      payee: "Five Elephant",
      date: "2024-08-14T16:00:00Z",
      notes: "Weekly specialty coffee hangout",
      categoryId: "dining",
      detailsTransactions: [
        {
          amount: 22000,
          name: "Coffee and Pastries",
          categoryId: "dining",
          quantity: 2,
          unitPrice: 11000,
        },
      ],
    },
    {
      accountId: "das_bank",
      amount: -45000,
      payee: "Deutsches Theater Berlin",
      date: "2024-08-15T19:00:00Z",
      notes: "Tickets to a new stage play",
      categoryId: "entertainment",
      detailsTransactions: [
        {
          amount: 45000,
          name: "Stage Play Tickets",
          categoryId: "entertainment",
          quantity: 2,
          unitPrice: 22500,
        },
      ],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: -90000,
      payee: "Berlin Plumbing Services",
      date: "2024-08-16T08:00:00Z",
      notes: "Emergency plumbing repair for a leaking pipe",
      categoryId: "household items",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -55000,
      payee: "Decathlon",
      date: "2024-08-17T12:30:00Z",
      notes: "Purchase of a new fitness tracker watch",
      categoryId: "electronics",
      detailsTransactions: [
        {
          amount: 55000,
          name: "Fitness Tracker Watch",
          categoryId: "electronics",
          quantity: 1,
          unitPrice: 55000,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -70000,
      payee: "Morgenstern Restaurant",
      date: "2024-08-18T11:00:00Z",
      notes: "Brunch with family at a popular local restaurant",
      categoryId: "dining",
      detailsTransactions: [
        {
          amount: 70000,
          name: "Brunch for Four",
          categoryId: "dining",
          quantity: 4,
          unitPrice: 17500,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -30000,
      payee: "Adobe Creative Cloud",
      date: "2024-08-19T10:00:00Z",
      notes: "Monthly subscription renewal for graphic design software",
      categoryId: "utilities",
      detailsTransactions: [],
    },
    {
      accountId: "commerzbank",
      amount: -40000,
      payee: "Asian Market Berlin",
      date: "2024-08-20T15:30:00Z",
      notes: "Purchasing specialty Asian ingredients for cooking",
      categoryId: "groceries",
      detailsTransactions: [
        {
          amount: 40000,
          name: "Assorted Asian Spices and Sauces",
          categoryId: "groceries",
          quantity: 10,
          unitPrice: 4000,
        },
      ],
    },
    {
      accountId: "das_bank",
      amount: -25000,
      payee: "Cineplex Berlin",
      date: "2024-08-21T19:00:00Z",
      notes: "Evening movie outing",
      categoryId: "entertainment",
      detailsTransactions: [
        {
          amount: 25000,
          name: "Movie Tickets for Two",
          categoryId: "entertainment",
          quantity: 2,
          unitPrice: 12500,
        },
      ],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: 500000,
      payee: "Freelance Client",
      date: "2024-08-22T09:00:00Z",
      notes: "Income from freelance software development project",
      categoryId: "freelance",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -120000,
      payee: "MediaMarkt",
      date: "2024-08-23T12:30:00Z",
      notes: "Purchase of a new high-end audio system for home entertainment",
      categoryId: "electronics",
      detailsTransactions: [
        {
          amount: 120000,
          name: "High-End Audio System",
          categoryId: "electronics",
          quantity: 1,
          unitPrice: 120000,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -65000,
      payee: "Berlin Modern Art Museum",
      date: "2024-08-24T15:00:00Z",
      notes: "Tickets for an exclusive modern art exhibition",
      categoryId: "entertainment",
      detailsTransactions: [
        {
          amount: 65000,
          name: "Art Exhibition Tickets",
          categoryId: "entertainment",
          quantity: 2,
          unitPrice: 32500,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -28000,
      payee: "Urban Yoga Berlin",
      date: "2024-08-25T10:00:00Z",
      notes: "Monthly pass for yoga classes",
      categoryId: "health",
      detailsTransactions: [],
    },
    {
      accountId: "commerzbank",
      amount: -85000,
      payee: "Le Cordon Bleu Berlin",
      date: "2024-08-26T18:00:00Z",
      notes:
        "Participation in a gourmet cooking class focusing on French cuisine",
      categoryId: "education",
      detailsTransactions: [],
    },
    {
      accountId: "das_bank",
      amount: -45000,
      payee: "Vinothek Berlin",
      date: "2024-08-27T19:30:00Z",
      notes: "Wine tasting event with a selection of international wines",
      categoryId: "dining",
      detailsTransactions: [
        {
          amount: 45000,
          name: "Wine Tasting Experience",
          categoryId: "dining",
          quantity: 2,
          unitPrice: 22500,
        },
      ],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: -40000,
      payee: "Resume Experts Berlin",
      date: "2024-08-28T09:00:00Z",
      notes:
        "Professional services for resume and LinkedIn profile optimization",
      categoryId: "professional services",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -52000,
      payee: "Berlin Tea House",
      date: "2024-08-29T14:30:00Z",
      notes:
        "Variety of loose leaf teas for personal enjoyment and health benefits",
      categoryId: "groceries",
      detailsTransactions: [
        {
          amount: 52000,
          name: "Assorted Loose Leaf Teas",
          categoryId: "groceries",
          quantity: 4,
          unitPrice: 13000,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -89000,
      payee: "IKEA Berlin",
      date: "2024-08-30T16:00:00Z",
      notes:
        "New ergonomic office chair and desk for a more comfortable work-from-home setup",
      categoryId: "household items",
      detailsTransactions: [
        {
          amount: 89000,
          name: "Ergonomic Chair and Desk",
          categoryId: "household items",
          quantity: 2,
          unitPrice: 44500,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -31000,
      payee: "Berlin Botanical Garden",
      date: "2024-08-31T11:00:00Z",
      notes:
        "Admission for two to explore the botanical gardens and special exhibitions",
      categoryId: "entertainment",
      detailsTransactions: [
        {
          amount: 31000,
          name: "Botanical Garden Admission Tickets",
          categoryId: "entertainment",
          quantity: 2,
          unitPrice: 15500,
        },
      ],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: 350000,
      payee: "Freelance Client",
      date: "2024-09-01T09:00:00Z",
      notes: "Payment received for freelance graphic design work",
      categoryId: "freelance",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -60000,
      payee: "City Fitness Berlin",
      date: "2024-09-02T12:00:00Z",
      notes:
        "Renewal of monthly gym membership for fitness classes and gym access",
      categoryId: "health",
      detailsTransactions: [],
    },
    {
      accountId: "n26",
      amount: -75000,
      payee: "Berlin School of Photography",
      date: "2024-09-03T15:00:00Z",
      notes:
        "Enrollment in an advanced photography course to improve professional skills",
      categoryId: "education",
      detailsTransactions: [],
    },
    {
      accountId: "paypal",
      amount: -43000,
      payee: "Berlin Water Works",
      date: "2024-09-04T10:30:00Z",
      notes: "Monthly payment for water and sewage services",
      categoryId: "utilities",
      detailsTransactions: [],
    },
    {
      accountId: "commerzbank",
      amount: -22000,
      payee: "Berlin Art Week",
      date: "2024-09-05T16:00:00Z",
      notes:
        "Tickets for two to attend a local art fair featuring contemporary artists",
      categoryId: "entertainment",
      detailsTransactions: [
        {
          amount: 22000,
          name: "Art Fair Admission for Two",
          categoryId: "entertainment",
          quantity: 2,
          unitPrice: 11000,
        },
      ],
    },
    {
      accountId: "das_bank",
      amount: -50000,
      payee: "Allianz Auto Insurance",
      date: "2024-09-06T08:00:00Z",
      notes: "Quarterly car insurance payment",
      categoryId: "insurance",
      detailsTransactions: [],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: 480000,
      payee: "Freelance Client",
      date: "2024-09-07T09:00:00Z",
      notes: "Payment received for freelance IT consultancy work",
      categoryId: "freelance",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -30000,
      payee: "H&M Online Store",
      date: "2024-09-08T12:30:00Z",
      notes: "Purchase of autumn jackets",
      categoryId: "clothing",
      detailsTransactions: [
        {
          amount: 30000,
          name: "Two Autumn Jackets",
          categoryId: "clothing",
          quantity: 2,
          unitPrice: 15000,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -40000,
      payee: "Spreewald Nature Reserve",
      date: "2024-09-09T16:00:00Z",
      notes: "Weekend retreat booking for relaxation and nature exploration",
      categoryId: "travel",
      detailsTransactions: [],
    },
    {
      accountId: "paypal",
      amount: -25000,
      payee: "Bio Company",
      date: "2024-09-10T10:30:00Z",
      notes: "Monthly supply of herbal supplements for health",
      categoryId: "health",
      detailsTransactions: [],
    },
    {
      accountId: "commerzbank",
      amount: -68000,
      payee: "Ikea",
      date: "2024-09-11T15:00:00Z",
      notes: "Purchasing new decor items for home renovation",
      categoryId: "household items",
      detailsTransactions: [
        {
          amount: 68000,
          name: "Curtains, Cushions, and Vases",
          categoryId: "household items",
          quantity: 5,
          unitPrice: 13600,
        },
      ],
    },
    {
      accountId: "das_bank",
      amount: -55000,
      payee: "Coffee Circle",
      date: "2024-09-12T19:00:00Z",
      notes: "High-end coffee machine for home barista experiences",
      categoryId: "electronics",
      detailsTransactions: [
        {
          amount: 55000,
          name: "Espresso Machine",
          categoryId: "electronics",
          quantity: 1,
          unitPrice: 55000,
        },
      ],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: 350000,
      payee: "Upwork",
      date: "2024-09-13T09:00:00Z",
      notes: "Payment for a completed graphic design project",
      categoryId: "freelance",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -22000,
      payee: "Eventim",
      date: "2024-09-14T18:30:00Z",
      notes: "Tickets for a live music event featuring local bands",
      categoryId: "entertainment",
      detailsTransactions: [
        {
          amount: 22000,
          name: "Concert Tickets",
          categoryId: "entertainment",
          quantity: 2,
          unitPrice: 11000,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -78000,
      payee: "Amazon",
      date: "2024-09-15T12:00:00Z",
      notes: "Purchase of external hard drive and laptop stand",
      categoryId: "electronics",
      detailsTransactions: [
        {
          amount: 78000,
          name: "External Hard Drive and Laptop Stand",
          categoryId: "electronics",
          quantity: 2,
          unitPrice: 39000,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -34000,
      payee: "The Barn",
      date: "2024-09-16T19:00:00Z",
      notes:
        "Dinner at a farm-to-table restaurant specializing in organic dishes",
      categoryId: "dining",
      detailsTransactions: [
        {
          amount: 34000,
          name: "Organic Dinner for Two",
          categoryId: "dining",
          quantity: 2,
          unitPrice: 17000,
        },
      ],
    },
    {
      accountId: "commerzbank",
      amount: -50000,
      payee: "Berlin Water Utility",
      date: "2024-09-17T10:00:00Z",
      notes: "Quarterly water service payment",
      categoryId: "utilities",
      detailsTransactions: [],
    },
    {
      accountId: "das_bank",
      amount: -27000,
      payee: "Berlin Book Club",
      date: "2024-09-18T14:30:00Z",
      notes: "Monthly subscription for curated book selections",
      categoryId: "entertainment",
      detailsTransactions: [],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: 480000,
      payee: "Remote Design Studio",
      date: "2024-09-19T09:00:00Z",
      notes: "Payment for a design project completed remotely",
      categoryId: "freelance",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -63000,
      payee: "Green Market Berlin",
      date: "2024-09-20T12:30:00Z",
      notes: "Weekly shopping for organic produce and eco-friendly products",
      categoryId: "groceries",
      detailsTransactions: [
        {
          amount: 63000,
          name: "Organic Vegetables and Natural Goods",
          categoryId: "groceries",
          quantity: 10,
          unitPrice: 6300,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -45000,
      payee: "Berlin Cyclists Club",
      date: "2024-09-21T16:00:00Z",
      notes: "Annual membership fee for cycling club and weekend rides",
      categoryId: "health",
      detailsTransactions: [],
    },
    {
      accountId: "paypal",
      amount: -37000,
      payee: "Volksbühne Berlin",
      date: "2024-09-22T20:00:00Z",
      notes: "Tickets to a contemporary theatre production",
      categoryId: "entertainment",
      detailsTransactions: [
        {
          amount: 37000,
          name: "Theatre Tickets for Two",
          categoryId: "entertainment",
          quantity: 2,
          unitPrice: 18500,
        },
      ],
    },
    {
      accountId: "commerzbank",
      amount: -88000,
      payee: "SmartHome Tech",
      date: "2024-09-23T11:00:00Z",
      notes: "Installation of a new smart home security system",
      categoryId: "electronics",
      detailsTransactions: [
        {
          amount: 88000,
          name: "Smart Home Security Kit",
          categoryId: "electronics",
          quantity: 1,
          unitPrice: 88000,
        },
      ],
    },
    {
      accountId: "das_bank",
      amount: -55000,
      payee: "Fit Berlin",
      date: "2024-09-24T09:00:00Z",
      notes: "Five personal training sessions for fitness improvement",
      categoryId: "health",
      detailsTransactions: [],
    },
  ],
  [
    {
      accountId: "commerzbank",
      amount: 460000,
      payee: "Freelance Client",
      date: "2024-09-25T09:00:00Z",
      notes: "Income from a completed web development project",
      categoryId: "freelance",
      detailsTransactions: [],
    },
    {
      accountId: "revolut",
      amount: -30000,
      payee: "Zalando",
      date: "2024-09-26T12:30:00Z",
      notes: "Purchasing new autumn clothing and accessories",
      categoryId: "clothing",
      detailsTransactions: [
        {
          amount: 30000,
          name: "Autumn Sweaters and Scarves",
          categoryId: "clothing",
          quantity: 3,
          unitPrice: 10000,
        },
      ],
    },
    {
      accountId: "n26",
      amount: -85000,
      payee: "Apple Store Online",
      date: "2024-09-27T15:00:00Z",
      notes: "Purchasing the latest iPad for personal and professional use",
      categoryId: "electronics",
      detailsTransactions: [
        {
          amount: 85000,
          name: "Latest iPad",
          categoryId: "electronics",
          quantity: 1,
          unitPrice: 85000,
        },
      ],
    },
    {
      accountId: "paypal",
      amount: -62000,
      payee: "Catering Berlin Gourmet",
      date: "2024-09-28T18:00:00Z",
      notes: "Catering for a dinner party at home with friends",
      categoryId: "dining",
      detailsTransactions: [
        {
          amount: 62000,
          name: "Gourmet Dinner Service",
          categoryId: "dining",
          quantity: 1,
          unitPrice: 62000,
        },
      ],
    },
    {
      accountId: "commerzbank",
      amount: -45000,
      payee: "Party City",
      date: "2024-09-29T10:30:00Z",
      notes: "Buying decorations for Halloween",
      categoryId: "entertainment",
      detailsTransactions: [
        {
          amount: 45000,
          name: "Halloween Party Decorations",
          categoryId: "entertainment",
          quantity: 15,
          unitPrice: 3000,
        },
      ],
    },
    {
      accountId: "das_bank",
      amount: -35000,
      payee: "Digital Photography Review",
      date: "2024-09-30T14:00:00Z",
      notes: "Annual subscription to enhance photography skills and knowledge",
      categoryId: "education",
      detailsTransactions: [],
    },
  ],
];
