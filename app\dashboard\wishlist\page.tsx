"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent } from "@/components/ui/card";
import { useState } from "react";
import { Plus, Loader2, Heart } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useGetWishlistItems } from "@/features/wishlist/api/use-get-wishlist-items";
import { useNewWishlistItem } from "@/features/wishlist/hooks/use-new-wishlist-item";
import { WishlistTable } from "@/features/wishlist/components/wishlist-table";
import { NewWishlistItemSheet } from "@/features/wishlist/components/new-wishlist-item-sheet";
import { EditWishlistItemSheet } from "@/features/wishlist/components/edit-wishlist-item-sheet";
import { createId } from "@paralleldrive/cuid2";
import { RefreshCcw } from "lucide-react";

const WishlistPage = () => {
  const { onOpen } = useNewWishlistItem();
  const wishlistQuery = useGetWishlistItems();
  const [renderKey, setRenderKey] = useState(createId());
  
  const rerender = () => {
    setRenderKey(createId());
  };

  if (wishlistQuery.isLoading) {
    return (
      <div className="max-w-screen-2xl mx-auto w-full pb-10">
        <Card className="border-none drop-shadow-sm">
          <CardHeader>
            <Skeleton className="h-8 w-48" />
          </CardHeader>
          <CardContent>
            <div className="h-[500px] flex items-center justify-center">
              <Loader2 className="size-4 text-slate-300 animate-spin" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <>
      <NewWishlistItemSheet />
      <EditWishlistItemSheet />
      <div key={renderKey} className="flex-1 w-full mb-10">
        <Card className="border-none drop-shadow-sm">
          <CardHeader className="gap-y-2 lg:flex-row lg:items-center lg:justify-between">
            <CardTitle className="line-clamp-1 flex items-center gap-2">
              <Heart className="size-5 text-pink-500" />
              Wishlist
              <Button variant={"outline"} onClick={rerender} className="p-3">
                <RefreshCcw size={16} />
              </Button>
            </CardTitle>
            <Button onClick={onOpen} size="sm">
              <Plus className="size-4 mr-2" />
              Add New Wish
            </Button>
          </CardHeader>
          <CardContent>
            <WishlistTable data={wishlistQuery.data || []} />
          </CardContent>
        </Card>
      </div>
    </>
  );
};

export default WishlistPage;