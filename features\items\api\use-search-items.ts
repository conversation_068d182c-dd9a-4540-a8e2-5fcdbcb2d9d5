"use client";
import { useQuery } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";

export const useSearchItems = (name: string, exact: boolean = false, enabled: boolean = true) => {
  const userId = useCurrentUserId();

  const query = useQuery({
    enabled: !!userId && !!name && enabled,
    queryKey: ["items", "search", { userId, name, exact }],
    queryFn: async () => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const response = await client.api.items.search.$get(
        {
          query: {
            name: name,
            exact: exact.toString(),
          },
        },
        {
          headers: {
            "X-User-ID": userId,
          },
        },
      );

      if (!response.ok) {
        throw new Error("Failed to search items");
      }

      const { data } = await response.json();
      return data;
    },
  });

  return query;
};