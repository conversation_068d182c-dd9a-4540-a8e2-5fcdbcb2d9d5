import fs from 'fs/promises';
import path from 'path';
import { createId } from "@paralleldrive/cuid2";

interface LocalUploadOptions {
  userId: string;
  category?: 'receipts' | 'documents' | 'images' | 'general';
  filename?: string;
  contentType?: string;
}

interface UploadResult {
  key: string;
  url: string;
  size: number;
  contentType: string;
}

class LocalStorage {
  private baseDir: string;
  private publicUrl: string;

  constructor() {
    this.baseDir = path.join(process.cwd(), 'public', 'uploads');
    this.publicUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  }

  /**
   * Generate a unique file key with user isolation and categorization
   */
  private generateFileKey(options: LocalUploadOptions, originalFilename: string): string {
    const { userId, category = 'general', filename } = options;
    const fileId = createId();
    const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const extension = originalFilename.split('.').pop() || '';
    const finalFilename = filename || `${fileId}.${extension}`;
    
    return `media/${userId}/${category}/${timestamp}/${finalFilename}`;
  }

  /**
   * Ensure directory exists
   */
  private async ensureDir(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  /**
   * Upload a file to local storage
   */
  async uploadFile(
    file: Buffer | Uint8Array | File,
    originalFilename: string,
    options: LocalUploadOptions
  ): Promise<UploadResult> {
    try {
      const key = this.generateFileKey(options, originalFilename);
      const filePath = path.join(this.baseDir, key);
      
      let buffer: Buffer;
      let size: number;
      let contentType: string;

      // Handle different file types
      if (file instanceof File) {
        buffer = Buffer.from(await file.arrayBuffer());
        size = file.size;
        contentType = file.type || options.contentType || 'application/octet-stream';
      } else {
        buffer = Buffer.from(file);
        size = buffer.length;
        contentType = options.contentType || this.getMimeTypeFromFilename(originalFilename);
      }

      // Ensure directory exists
      await this.ensureDir(path.dirname(filePath));

      // Write file to local storage
      await fs.writeFile(filePath, buffer);
      
      // Generate public URL
      const url = this.getPublicUrl(key);

      return {
        key,
        url,
        size,
        contentType,
      };
    } catch (error) {
      console.error('Local storage upload error:', error);
      throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete a file from local storage
   */
  async deleteFile(key: string): Promise<void> {
    try {
      const filePath = path.join(this.baseDir, key);
      await fs.unlink(filePath);
    } catch (error) {
      console.error('Local storage delete error:', error);
      throw new Error(`Failed to delete file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if a file exists
   */
  async fileExists(key: string): Promise<boolean> {
    try {
      const filePath = path.join(this.baseDir, key);
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get file info
   */
  async getFileInfo(key: string): Promise<{ size: number; contentType: string } | null> {
    try {
      const filePath = path.join(this.baseDir, key);
      const stats = await fs.stat(filePath);
      const contentType = this.getMimeTypeFromFilename(key);
      
      return {
        size: stats.size,
        contentType,
      };
    } catch {
      return null;
    }
  }

  /**
   * Generate public URL for a file
   */
  getPublicUrl(key: string): string {
    return `${this.publicUrl}/uploads/${key}`;
  }

  /**
   * Get MIME type from filename extension
   */
  private getMimeTypeFromFilename(filename: string): string {
    const ext = path.extname(filename).toLowerCase();
    const mimeTypes: Record<string, string> = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.pdf': 'application/pdf',
      '.txt': 'text/plain',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    };
    return mimeTypes[ext] || 'application/octet-stream';
  }
}

// Singleton instance
let localStorage: LocalStorage | null = null;

/**
 * Get or create LocalStorage instance
 */
export function getLocalStorage(): LocalStorage {
  if (!localStorage) {
    localStorage = new LocalStorage();
  }
  return localStorage;
}

export type { LocalUploadOptions, UploadResult };
