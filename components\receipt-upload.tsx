"use client";

import React, { useState, useRef, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Camera, Upload, X, FileImage, Loader2, CheckCircle, AlertCircle } from "lucide-react";
import { toast } from "sonner";

interface UploadedFile {
  id: string;
  file: File;
  preview: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
}

interface ReceiptUploadProps {
  onReceiptsProcessed?: (data: any) => void;
  onError?: (error: string) => void;
  maxFiles?: number;
  acceptedTypes?: string[];
}

export function ReceiptUpload({ 
  onReceiptsProcessed, 
  onError,
  maxFiles = 5,
  acceptedTypes = ['image/jpeg', 'image/png', 'image/webp', 'application/pdf']
}: ReceiptUploadProps) {
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const generateId = () => Math.random().toString(36).substring(2);

  const validateFile = (file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return `File type ${file.type} not supported. Please use JPEG, PNG, WebP, or PDF files.`;
    }
    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      return "File size must be less than 10MB";
    }
    return null;
  };

  const createFilePreview = (file: File): Promise<string> => {
    return new Promise((resolve) => {
      if (file.type === 'application/pdf') {
        resolve('/pdf-icon.svg'); // You can create a PDF icon or use a default
      } else {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.readAsDataURL(file);
      }
    });
  };

  const addFiles = async (newFiles: FileList) => {
    const validFiles: File[] = [];
    
    for (let i = 0; i < newFiles.length; i++) {
      const file = newFiles[i];
      const error = validateFile(file);
      
      if (error) {
        toast.error(`${file.name}: ${error}`);
        continue;
      }
      
      if (files.length + validFiles.length >= maxFiles) {
        toast.error(`Maximum ${maxFiles} files allowed`);
        break;
      }
      
      validFiles.push(file);
    }

    if (validFiles.length === 0) return;

    const uploadedFiles: UploadedFile[] = [];
    for (const file of validFiles) {
      const preview = await createFilePreview(file);
      uploadedFiles.push({
        id: generateId(),
        file,
        preview,
        status: 'pending'
      });
    }

    setFiles(prev => [...prev, ...uploadedFiles]);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files;
    if (selectedFiles) {
      addFiles(selectedFiles);
    }
    // Reset input value to allow selecting the same file again
    event.target.value = '';
  };

  const removeFile = (id: string) => {
    setFiles(prev => prev.filter(file => file.id !== id));
  };

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { facingMode: 'environment' } // Use rear camera on mobile
      });
      streamRef.current = stream;
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
      setIsCameraOpen(true);
    } catch (error) {
      toast.error("Unable to access camera. Please check permissions.");
    }
  };

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setIsCameraOpen(false);
  };

  const capturePhoto = () => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) return;

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    context.drawImage(video, 0, 0);

    canvas.toBlob(async (blob) => {
      if (!blob) return;

      const file = new File([blob], `receipt-${Date.now()}.jpeg`, { type: 'image/jpeg' });
      const preview = await createFilePreview(file);
      
      const uploadedFile: UploadedFile = {
        id: generateId(),
        file,
        preview,
        status: 'pending'
      };

      setFiles(prev => [...prev, uploadedFile]);
      stopCamera();
    }, 'image/jpeg', 0.8);
  };

  const processReceipts = async () => {
    if (files.length === 0) {
      toast.error("Please upload at least one receipt");
      return;
    }

    setIsProcessing(true);
    
    // Update all files to processing status
    setFiles(prev => prev.map(file => ({ ...file, status: 'processing' as const })));

    try {
      const formData = new FormData();
      files.forEach(({ file }, index) => {
        formData.append(`receipt-${index}`, file);
      });

      const response = await fetch('/api/receipts/process', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      // Update files to completed status
      setFiles(prev => prev.map(file => ({ ...file, status: 'completed' as const })));
      
      toast.success(`Processed ${files.length} receipt(s) successfully`);

      onReceiptsProcessed?.(result);

    } catch (error) {
      console.error('Receipt processing error:', error);
      
      // Update files to error status
      setFiles(prev => prev.map(file => ({ ...file, status: 'error' as const })));
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      toast.error(`Processing Failed: ${errorMessage}`);
      
      onError?.(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  const clearAll = () => {
    setFiles([]);
    stopCamera();
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileImage className="h-5 w-5" />
          Receipt Upload
        </CardTitle>
        <CardDescription>
          Upload receipt images or PDFs to automatically create transactions
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Upload Actions */}
        <div className="flex flex-col sm:flex-row gap-2">
          <Button
            onClick={() => fileInputRef.current?.click()}
            variant="outline"
            className="flex-1"
            disabled={isProcessing || files.length >= maxFiles}
          >
            <Upload className="h-4 w-4 mr-2" />
            Choose Files
          </Button>
          
          <Dialog open={isCameraOpen} onOpenChange={setIsCameraOpen}>
            <DialogTrigger asChild>
              <Button
                onClick={startCamera}
                variant="outline"
                className="flex-1"
                disabled={isProcessing || files.length >= maxFiles}
              >
                <Camera className="h-4 w-4 mr-2" />
                Take Photo
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Take Receipt Photo</DialogTitle>
                <DialogDescription>
                  Position the receipt clearly in the frame
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <video
                  ref={videoRef}
                  autoPlay
                  playsInline
                  className="w-full rounded-lg"
                />
                <canvas ref={canvasRef} className="hidden" />
                <div className="flex gap-2">
                  <Button onClick={capturePhoto} className="flex-1">
                    Capture
                  </Button>
                  <Button onClick={stopCamera} variant="outline" className="flex-1">
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={handleFileSelect}
          className="hidden"
        />

        {/* File Preview */}
        {files.length > 0 && (
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">
                {files.length} file{files.length > 1 ? 's' : ''} selected
              </span>
              <Button onClick={clearAll} variant="ghost" size="sm">
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
              {files.map(({ id, file, preview, status }) => (
                <div key={id} className="relative group">
                  <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 border">
                    {file.type === 'application/pdf' ? (
                      <div className="w-full h-full flex items-center justify-center">
                        <FileImage className="h-8 w-8 text-gray-500" />
                      </div>
                    ) : (
                      <img
                        src={preview}
                        alt={file.name}
                        className="w-full h-full object-cover"
                      />
                    )}
                  </div>
                  
                  {/* Status overlay */}
                  <div className="absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                    {status === 'pending' && (
                      <Badge variant="secondary">Ready</Badge>
                    )}
                    {status === 'processing' && (
                      <Badge variant="secondary">
                        <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                        Processing
                      </Badge>
                    )}
                    {status === 'completed' && (
                      <Badge variant="default">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Done
                      </Badge>
                    )}
                    {status === 'error' && (
                      <Badge variant="destructive">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Error
                      </Badge>
                    )}
                  </div>

                  {/* Remove button */}
                  <Button
                    onClick={() => removeFile(id)}
                    size="sm"
                    variant="destructive"
                    className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    disabled={isProcessing}
                  >
                    <X className="h-3 w-3" />
                  </Button>

                  {/* File name */}
                  <div className="absolute bottom-0 left-0 right-0 bg-black/70 text-white text-xs p-1 rounded-b-lg truncate">
                    {file.name}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Process Button */}
        {files.length > 0 && (
          <Button
            onClick={processReceipts}
            disabled={isProcessing || files.every(f => f.status === 'completed')}
            className="w-full"
          >
            {isProcessing ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Processing Receipts...
              </>
            ) : (
              `Process ${files.length} Receipt${files.length > 1 ? 's' : ''}`
            )}
          </Button>
        )}

        {/* Progress indicator */}
        {isProcessing && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Processing receipts...</span>
              <span>{files.filter(f => f.status === 'completed').length}/{files.length}</span>
            </div>
            <Progress 
              value={(files.filter(f => f.status === 'completed').length / files.length) * 100} 
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}