import { useQuery } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { useSearchParams } from "next/navigation";
import { useCurrentUserId } from "@/lib/utils";

export const useGetCategories = () => {
  const params = useSearchParams();
  const from = params.get("from") || "";
  const to = params.get("to") || "";
  const accountId = params.get("accountId") || "";
  const userId = useCurrentUserId();

  const query = useQuery({
    queryKey: [
      "categories",
      {
        from,
        to,
        accountId,
        userId,
      },
    ],
    queryFn: async () => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const response = await client.api.categories.$get(
        {
          query: {
            from,
            to,
            accountId,
          },
        },
        {
          headers: {
            "X-User-ID": userId,
          },
        },
      );

      if (!response.ok) {
        throw new Error("Failed to fetch categories");
      }

      const { data } = await response.json();
      return data;
    },
  });

  return query;
};
