import { Hono } from "hono";
import { handle } from "hono/vercel";
import { cors } from "hono/cors"; // Add cors import
import { auth } from "@/lib/auth";
import accounts from "./accounts";
import transactions from "./transactions";
import categories from "./categories";
import summary from "./summary";
import getip from "./getip";
import projects from "./projects";
import conversation from "./conversation";
import detailsTransactions from "./details-transactions";
import itemTransactions from "./item-transactions";
import items from "./items";
import authent from "./auth";
import wishlist from "./wishlist";
import receipts from "./receipts";
import media from "./media";
import tags from "./tags";

export const runtime = "nodejs";

const app = new Hono().basePath("/api");

const routes = app
  .route("/accounts", accounts)
  .route("/categories", categories)
  .route("/transactions", transactions)
  .route("/summary", summary)
  .route("/getip", getip)
  .route("/conversation", conversation)
  .route("/detailsTransactions", detailsTransactions)
  .route("/itemTransactions", itemTransactions)
  .route("/items", items)
  .route("/projects", projects)
  .route("/wishlist", wishlist)
  .route("/receipts", receipts)
  .route("/media", media)
  .route("/tags", tags)
  .route("/auth", authent);

export const GET = handle(app);
export const POST = handle(app);
export const PATCH = handle(app);
export const DELETE = handle(app);

export type AppType = typeof routes;
