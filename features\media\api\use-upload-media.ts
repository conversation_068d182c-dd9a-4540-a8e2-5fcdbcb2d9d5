import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useCurrentUserId } from "@/lib/utils";
import { client } from "@/lib/hono";
import { toast } from "sonner";

interface UploadMediaOptions {
  category?: "receipts" | "documents" | "images" | "general";
  entityType?: string;
  entityId?: string;
  metadata?: Record<string, any>;
}

interface MediaFile {
  id: string;
  fileName: string;
  originalFileName: string;
  mimeType: string;
  fileSize: number;
  url: string;
  category: string;
  entityType: string | null;
  entityId: string | null;
  createdAt: string;
}

export const useUploadMedia = () => {
  const userId = useCurrentUserId();
  const queryClient = useQueryClient();

  return useMutation<
    { data: MediaFile[]; message: string },
    Error,
    { files: FileList | File[]; options?: UploadMediaOptions }
  >({
    mutationFn: async ({ files, options = {} }) => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const formData = new FormData();
      
      // Add files to form data
      Array.from(files).forEach((file, index) => {
        formData.append(`file-${index}`, file);
      });

      // Add options to form data
      if (options.category) {
        formData.append("category", options.category);
      }
      if (options.entityType) {
        formData.append("entityType", options.entityType);
      }
      if (options.entityId) {
        formData.append("entityId", options.entityId);
      }
      if (options.metadata) {
        formData.append("metadata", JSON.stringify(options.metadata));
      }

      const response = await client.api.media.upload.$post(
        { form: formData },
        { headers: { "X-User-ID": userId } }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Upload failed");
      }

      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["media"] });
      toast.success(data.message || "Files uploaded successfully");
    },
    onError: (error) => {
      toast.error(`Upload failed: ${error.message}`);
    },
  });
};