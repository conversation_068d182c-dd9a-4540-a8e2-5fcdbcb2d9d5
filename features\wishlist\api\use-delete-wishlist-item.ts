import { InferResponseType } from "hono";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";

type ResponseType = InferResponseType<typeof client.api.wishlist[":id"]["$delete"]>;

export const useDeleteWishlistItem = (id?: string) => {
  const userId = useCurrentUserId();
  const queryClient = useQueryClient();
  
  const mutation = useMutation<ResponseType, Error>({
    mutationFn: async () => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const response = await client.api.wishlist[":id"].$delete(
        { param: { id } },
        {
          headers: {
            "X-User-ID": userId,
          },
        },
      );
      const data = await response.json();

      return data;
    },
    onSuccess: () => {
      toast.success("Wishlist item deleted");
      queryClient.invalidateQueries({ queryKey: ["wishlist-items", userId] });
    },
    onError: () => {
      toast.error("Failed to delete wishlist item");
    },
  });

  return mutation;
};