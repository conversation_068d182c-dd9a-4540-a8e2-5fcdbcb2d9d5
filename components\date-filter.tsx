"use client";

import { DateRange } from "react-day-picker";
import { useState } from "react";
import { subDays, format } from "date-fns";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "./ui/button";
import { Calendar } from "./ui/calendar";
import { formatDateRange } from "@/lib/utils";
import { ChevronDown } from "lucide-react";

type Props = {
  from: string;
  to: string;
  onUpdate: (values: { from: string; to: string }) => void;
};

export const DateFilter = ({ from, to, onUpdate }: Props) => {
  const defaultTo = new Date();
  const defaultFrom = subDays(defaultTo, 30);

  const paramState = {
    from: from ? new Date(from) : defaultFrom,
    to: to ? new Date(to) : defaultTo,
  };

  const [date, setDate] = useState<DateRange | undefined>(paramState);

  const handleApply = (dateRange: DateRange | undefined) => {
    const fromDate = format(dateRange?.from || defaultFrom, "yyyy-MM-dd");
    const toDate = format(dateRange?.to || defaultTo, "yyyy-MM-dd");
    
    onUpdate({
      from: fromDate,
      to: toDate,
    });
  };

  const onReset = () => {
    setDate(undefined);
    onUpdate({
      from: "",
      to: "",
    });
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          disabled={false}
          size="sm"
          variant="outline"
          className="lg:w-auto w-full h-9 font-normal"
        >
          <span>{formatDateRange(paramState)}</span>
          <ChevronDown className="ml-2 size-4 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="lg:w-auto w-full p-0">
        <Calendar
          disabled={false}
          initialFocus
          mode="range"
          defaultMonth={date?.from}
          selected={date}
          onSelect={setDate}
          numberOfMonths={2}
        />

        <div className="p-4 w-full flex items-center gap-x-2">
          <div>
            <Button
              onClick={onReset}
              disabled={!date?.from || !date?.to}
              className="w-full"
            >
              Reset
            </Button>
          </div>
          <div>
            <Button
              onClick={() => handleApply(date)}
              disabled={!date?.from || !date?.to}
              className="w-full"
            >
              Apply
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};
