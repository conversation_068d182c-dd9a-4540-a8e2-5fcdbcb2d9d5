import { DataFilter } from "./data-filter";
import { AccountFilter } from "./account-filter";
import { DialogProfilForm } from "./dialog-profil-form";
import Link from "next/link";
import { Suspense } from "react";

export const Filter = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 items-center justify-center gap-x-2 gap-y-2 lg:gap-y-0 lg:gap-x-2">
      <AccountFilter /> {/* Wrap AccountFilter in Suspense */}
      <DataFilter />
    </div>
  );
};
