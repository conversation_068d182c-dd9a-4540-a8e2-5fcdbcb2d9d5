{"id": "bbfadf74-e849-4131-aa64-eca6b9c33393", "prevId": "d48d04ab-1b1e-49c3-9eed-45c53719504e", "version": "5", "dialect": "pg", "tables": {"accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "plaid_id": {"name": "plaid_id", "type": "text", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "categories": {"name": "categories", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "plaid_id": {"name": "plaid_id", "type": "text", "primaryKey": false, "notNull": false}, "goal": {"name": "goal", "type": "integer", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "detailsTransaction": {"name": "detailsTransaction", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": false}, "unit_price": {"name": "unit_price", "type": "integer", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": false}, "transaction_id": {"name": "transaction_id", "type": "text", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"detailsTransaction_project_id_project_id_fk": {"name": "detailsTransaction_project_id_project_id_fk", "tableFrom": "detailsTransaction", "tableTo": "project", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "detailsTransaction_transaction_id_transactions_id_fk": {"name": "detailsTransaction_transaction_id_transactions_id_fk", "tableFrom": "detailsTransaction", "tableTo": "transactions", "columnsFrom": ["transaction_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "detailsTransaction_category_id_categories_id_fk": {"name": "detailsTransaction_category_id_categories_id_fk", "tableFrom": "detailsTransaction", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "project": {"name": "project", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "budget": {"name": "budget", "type": "integer", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "timestamp", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "transactions": {"name": "transactions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "payee": {"name": "payee", "type": "text", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "timestamp", "primaryKey": false, "notNull": false}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": false}, "category_id": {"name": "category_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"transactions_account_id_accounts_id_fk": {"name": "transactions_account_id_accounts_id_fk", "tableFrom": "transactions", "tableTo": "accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "transactions_project_id_project_id_fk": {"name": "transactions_project_id_project_id_fk", "tableFrom": "transactions", "tableTo": "project", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "transactions_category_id_categories_id_fk": {"name": "transactions_category_id_categories_id_fk", "tableFrom": "transactions", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}