"use client";

import { z } from "zod";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { insertItemSchema } from "@/db/schema";
import { useCreateItem } from "../api/use-create-item";
import { ItemForm } from "./item-form";

const formSchema = insertItemSchema.omit({
  id: true,
  userId: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  tagIds: z.array(z.string()).optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface NewItemSheetProps {
  isOpen: boolean;
  onClose: () => void;
}

export const NewItemSheet = ({
  isOpen,
  onClose,
}: NewItemSheetProps) => {
  const createMutation = useCreateItem();

  const isPending = createMutation.isPending;

  const onSubmit = (values: FormValues) => {
    createMutation.mutate(values, {
      onSuccess: () => {
        onClose();
      },
    });
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="overflow-y-auto">
        <SheetHeader>
          <SheetTitle>Create Item</SheetTitle>
          <SheetDescription>
            Add a new item to your inventory. You can add details and tags to organize it better.
          </SheetDescription>
        </SheetHeader>
        
        <ItemForm
          onSubmit={onSubmit}
          onCancel={onClose}
          disabled={isPending}
        />
      </SheetContent>
    </Sheet>
  );
};