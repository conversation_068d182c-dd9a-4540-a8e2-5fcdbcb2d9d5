import { useQuery } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";

export const useGetAccounts = () => {
  const userId = useCurrentUserId();

  const query = useQuery({
    queryKey: ["accounts", userId],
    queryFn: async () => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const response = await client.api.accounts.$get(
        {},
        {
          headers: {
            "X-User-ID": userId,
          },
        },
      );

      if (!response.ok) {
        throw new Error("Failed to fetch accounts");
      }

      const { data } = await response.json();

      return data;
    },
  });

  return query;
};
