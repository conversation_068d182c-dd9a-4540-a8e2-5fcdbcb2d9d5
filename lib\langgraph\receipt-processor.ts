import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { HumanMessage, SystemMessage } from "@langchain/core/messages";
import { z } from "zod";
import * as fs from "fs";
import * as path from "path";

// Tag suggestion schema for individual items
export const ItemTagSuggestionSchema = z.object({
  itemName: z.string().describe("Name of the item"),
  existingTagIds: z.array(z.string()).describe("IDs of existing tags that are suitable for this item"),
  suggestedNewTags: z.array(z.string()).describe("Names of new tags to create if no existing ones are suitable"),
});

// Tag processing result schema
export const TagProcessingSchema = z.object({
  itemTagSuggestions: z.array(ItemTagSuggestionSchema).describe("Tag suggestions for each item"),
  confidence: z.number().describe("Confidence level of tag suggestions (0-1)"),
  processingNotes: z.string().optional().describe("Notes about tag processing"),
});

// Receipt item schema that matches detailsTransactions table
export const ReceiptItemSchema = z.object({
  name: z.string().optional().describe("Item name/description"),
  quantity: z.number().optional().describe("Quantity of the item"),
  unitPrice: z.number().optional().describe("Price per unit (will be converted to miliunits)"),
  amount: z.number().optional().describe("Total amount for this item (will be converted to miliunits)"),
  categoryId: z.string().describe("Category from item. If no good match exists, sugest one should be a name not an ID"),
  isIncome: z.boolean().default(false).describe("True if this is income (like refunds), false if expense"),
  suggestedTagIds: z.array(z.string()).optional().describe("Suggested tag IDs for this item"),
  suggestedNewTags: z.array(z.string()).optional().describe("Suggested new tag names for this item"),
});

// Receipt transaction schema that matches transactions table
export const ReceiptTransactionSchema = z.object({
  amount: z.number().describe("Total transaction amount (will be converted to miliunits)"),
  payee: z.string().describe("Merchant/store name"),
  notes: z.string().optional().describe("Additional notes about the transaction"),
  date: z.string().describe("Transaction date in YYYY-MM-DD format"),
  accountId: z.string().optional().describe("Account ID - will need to be set by user"),
  categoryId: z.string().optional().describe("Main category ID for the transaction"),
  isIncome: z.boolean().default(false).describe("True if this is income (like salary, refunds), false if expense"),
  detailsTransactions: z.array(ReceiptItemSchema).describe("Individual items from the receipt"),
});

// Complete receipt processing response
export const ReceiptProcessingSchema = z.object({
  transactions: z.array(ReceiptTransactionSchema).describe("Extracted transactions from receipt(s)"),
  suggestedCategories: z.array(z.string()).describe("Suggested category names that might need to be created"),
  confidence: z.number().describe("Confidence level of the extraction (0-1)"),
  processingNotes: z.string().optional().describe("Notes about the processing or any issues"),
  tagProcessingResult: TagProcessingSchema.optional().describe("Tag processing results"),
});

export type ItemTagSuggestion = z.infer<typeof ItemTagSuggestionSchema>;
export type TagProcessingResult = z.infer<typeof TagProcessingSchema>;
export type ReceiptItem = z.infer<typeof ReceiptItemSchema>;
export type ReceiptTransaction = z.infer<typeof ReceiptTransactionSchema>;
export type ReceiptProcessingResult = z.infer<typeof ReceiptProcessingSchema>;

export interface MediaItem {
  id: string;
  fileName: string;
  mimeType: string;
  url: string;
  filePath: string;
}

export interface UserAccount {
  id: string;
  name: string;
}

export interface UserCategory {
  id: string;
  name: string;
}

export interface UserTag {
  id: string;
  name: string;
  color?: string;
}

export interface UserData {
  accounts: UserAccount[];
  categories: UserCategory[];
  tags: UserTag[];
}

//TODO update prompt to always match food & groceries category to revolut

export class ReceiptProcessor {
  private model: ChatGoogleGenerativeAI;
  private modelWithSchema: any;
  private tagModel: any;

  constructor() {
    const apiKey = process.env.GOOGLE_API_KEY;
    if (!apiKey) {
      throw new Error('GOOGLE_API_KEY environment variable is not set');
    }

    this.model = new ChatGoogleGenerativeAI({
      model: "gemini-2.5-flash-lite",
      temperature: 0.1,
      maxRetries: 2,
      apiKey: apiKey,
    });

    // Create model with structured output for receipt processing
    this.modelWithSchema = this.model.withStructuredOutput(ReceiptProcessingSchema);
    
    // Create specialized model for tag processing
    this.tagModel = this.model.withStructuredOutput(TagProcessingSchema);
  }

  private createSystemPrompt(userData: UserData): string {
    const accountsList = userData.accounts.map(acc => `- ${acc.name} (ID: ${acc.id})`).join('\n');
    const categoriesList = userData.categories.map(cat => `- ${cat.name} (ID: ${cat.id})`).join('\n');
    const tagsList = userData.tags.map(tag => `- ${tag.name} (ID: ${tag.id})`).join('\n');

    return `You are an expert receipt parser that extracts structured financial transaction data from receipt images.

AVAILABLE USER ACCOUNTS:
${accountsList}

AVAILABLE USER CATEGORIES:
${categoriesList}

AVAILABLE USER TAGS:
${tagsList}

CRITICAL RULES:
1. Extract ALL individual items from receipts with their exact names, quantities, unit prices, and amounts
2. Return amounts as decimal numbers (e.g., 12.50, not 1250) - the system will convert to miliunits
3. For each item and main transaction, try to match to existing categories from the list above using categoryId
4. SPECIAL RULE: Always assign food/grocery transactions to "Revolut" account if it exists
5. Be extremely careful with number parsing - verify amounts add up correctly
6. Use the merchant name as the "payee" field
7. Extract date in YYYY-MM-DD format or use today's date if not visible
8. Try to suggest the most appropriate accountId from the available accounts
9. Include tax, tip, and fees as separate line items if present
10. If multiple receipts are detected, create separate transaction objects
11. DETERMINE TRANSACTION TYPE: Set isIncome flag for both transactions and items
    - Most receipts are EXPENSES (isIncome: false) - purchases, bills, etc.
    - INCOME receipts (isIncome: true) are rare: refunds, returns, cashback, salary slips
    - For regular purchases (groceries, shopping), always use isIncome: false
    - For returns/refunds, use isIncome: true

CATEGORY MATCHING GUIDELINES:
- Match items to existing categories when possible using the exact categoryId
- For food items: look for categories like "Groceries", "Food", "Dining", etc.
- For household items: look for "Household", "Supplies", etc.
- For personal care: look for "Personal Care", "Health", etc.
- If no good match exists, suggest new category names in suggestedCategories and CategoryId 
ACCOUNT SELECTION:
- For food/grocery transactions, prioritize "Revolut" account if available
- For other transactions, suggest the most appropriate account based on the transaction type
- If unsure, leave accountId undefined

IMPORTANT: The response MUST match the exact database schema structure. All monetary amounts will be automatically converted to integer miliunits by the system.

Focus on accuracy - it's better to be conservative than to guess incorrectly.

NOTE: Tag processing will be handled by a separate specialized model, so focus on accurate transaction and category extraction.`;
  }

  public async processReceipts(mediaItems: MediaItem[], userData: UserData): Promise<ReceiptProcessingResult> {
    try {
      // Create content for the model
      const content: Array<{
        type: string;
        text?: string;
        mime_type?: string;
        source_type?: string;
        data?: string;
      }> = [
        {
          type: "text",
          text: `Please extract all transaction details from these ${mediaItems.length} receipt image(s). Return structured data following the ReceiptProcessingSchema format.`,
        }
      ];

      // Add all media files to the content
      for (const mediaItem of mediaItems) {
        // Check if it's an image or PDF
        if (!mediaItem.mimeType.startsWith('image/') && mediaItem.mimeType !== 'application/pdf') {
          console.warn(`Skipping non-image/PDF file: ${mediaItem.fileName}`);
          continue;
        }

        // Read and encode the media file as base64
        const mediaData = fs.readFileSync(mediaItem.filePath);
        const base64Media = mediaData.toString('base64');
        
        content.push({
          type: "file",
          mime_type: mediaItem.mimeType,
          source_type: "base64",
          data: base64Media,
        });

        // Add a text description for the media
        content.push({
          type: "text",
          text: `Receipt image: ${mediaItem.fileName}`,
        });
      }

      const messages = [
        new SystemMessage(this.createSystemPrompt(userData)),
        new HumanMessage({ content: content })
      ];

      // Use structured output to get properly formatted response
      const result = await this.modelWithSchema.invoke(messages);
      
      // Process tags for the extracted items
      const tagProcessingResult = await this.processItemTags(result.transactions, userData);
      
      // Merge tag suggestions into the result
      const finalResult = this.mergeTagSuggestions(result, tagProcessingResult);
      
      return finalResult as ReceiptProcessingResult;

    } catch (error) {
      console.error('Error processing receipts:', JSON.stringify(error, null, 2));
      console.error(error);
      
      // Return a fallback result
      return {
        transactions: [{
          amount: 0,
          payee: "Unknown Merchant",
          notes: `Receipt processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          date: new Date().toISOString().split('T')[0],
          accountId: undefined,
          categoryId: undefined,
          projectId: undefined,
          detailsTransactions: [{
            name: "Receipt item (processing failed)",
            quantity: undefined,
            unitPrice: undefined,
            amount: 0,
            categoryId: undefined,
            projectId: undefined,
          }]
        }],
        suggestedCategories: [],
        confidence: 0,
        processingNotes: "Processing failed - manual entry required"
      };
    }
  }

  private createTagProcessingPrompt(userData: UserData): string {
    const tagsList = userData.tags.map(tag => `- ${tag.name} (ID: ${tag.id})`).join('\n');
    
    return `You are a specialized AI that assigns appropriate tags to receipt items based on available user tags.

AVAILABLE USER TAGS:
${tagsList}

Your task:
1. For each item provided, analyze what it is and suggest appropriate tags
2. Prioritize using existing user tags when they match the item
3. If no existing tags are suitable, suggest new tag names that would be appropriate
4. Consider the item's category, type, brand, or other relevant characteristics
5. Suggest 1-3 tags per item maximum

GUIDELINES:
- Food items: Use tags like "food", "snacks", "groceries", "organic", "frozen", etc.
- Personal care: Use tags like "hygiene", "skincare", "health", "beauty", etc.
- Household items: Use tags like "cleaning", "supplies", "kitchen", "bathroom", etc.
- Clothing: Use tags like "clothing", "shoes", "accessories", "winter", "summer", etc.
- Electronics: Use tags like "electronics", "tech", "gadgets", etc.
- Be specific but not overly narrow (e.g., "beverages" rather than "cola")
- Prefer existing user tags over creating new ones when possible

Return suggestions for each item with confidence level.`;
  }

  private async processItemTags(
    transactions: ReceiptTransaction[], 
    userData: UserData
  ): Promise<TagProcessingResult> {
    try {
      // Collect all items from all transactions
      const allItems: { name: string; transaction: number; itemIndex: number }[] = [];
      
      transactions.forEach((transaction, transactionIndex) => {
        transaction.detailsTransactions.forEach((item, itemIndex) => {
          if (item.name && item.name.trim() && item.name !== 'undefined') {
            allItems.push({
              name: item.name.trim(),
              transaction: transactionIndex,
              itemIndex: itemIndex
            });
          }
        });
      });

      // If no items with names, return empty result
      if (allItems.length === 0) {
        return {
          itemTagSuggestions: [],
          confidence: 1.0,
          processingNotes: "No named items found for tag processing"
        };
      }

      // Create prompt for tag processing
      const itemList = allItems.map((item, index) => 
        `${index + 1}. ${item.name}`
      ).join('\n');

      const content = `Please suggest appropriate tags for the following receipt items:

${itemList}

For each item, choose from the available user tags or suggest new ones if none are suitable.`;

      const messages = [
        new SystemMessage(this.createTagProcessingPrompt(userData)),
        new HumanMessage({ content: content })
      ];

      // Process with tag-specific model
      const tagResult = await this.tagModel.invoke(messages);
      
      return tagResult as TagProcessingResult;

    } catch (error) {
      console.error('Error processing tags:', error);
      return {
        itemTagSuggestions: [],
        confidence: 0,
        processingNotes: `Tag processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private mergeTagSuggestions(
    receiptResult: ReceiptProcessingResult,
    tagResult: TagProcessingResult
  ): ReceiptProcessingResult {
    // Create a map of item names to their tag suggestions
    const tagSuggestionsMap = new Map<string, { existingTagIds: string[], suggestedNewTags: string[] }>();
    
    tagResult.itemTagSuggestions.forEach(suggestion => {
      tagSuggestionsMap.set(suggestion.itemName.toLowerCase().trim(), {
        existingTagIds: suggestion.existingTagIds,
        suggestedNewTags: suggestion.suggestedNewTags
      });
    });

    // Update transactions with tag suggestions
    const updatedTransactions = receiptResult.transactions.map(transaction => ({
      ...transaction,
      detailsTransactions: transaction.detailsTransactions.map(item => {
        if (!item.name || !item.name.trim() || item.name === 'undefined') {
          return item;
        }

        const itemKey = item.name.toLowerCase().trim();
        const tagSuggestion = tagSuggestionsMap.get(itemKey);
        
        if (tagSuggestion) {
          return {
            ...item,
            suggestedTagIds: tagSuggestion.existingTagIds,
            suggestedNewTags: tagSuggestion.suggestedNewTags
          };
        }

        return item;
      })
    }));

    return {
      ...receiptResult,
      transactions: updatedTransactions,
      tagProcessingResult: tagResult
    };
  }

  public async processSingleReceipt(mediaItem: MediaItem, userData: UserData): Promise<ReceiptProcessingResult> {
    return this.processReceipts([mediaItem], userData);
  }
}