"use client";

import { useState } from "react";
import {
  Conversation,
  ConversationContent,
  ConversationScrollButton,
} from "@/components/conversation";
import { Message, MessageContent } from "@/components/message";
import {
  PromptInput,
  PromptInputTextarea,
  PromptInputSubmit,
  PromptInputToolbar,
  PromptInputTools,
  PromptInputButton,
  PromptInputModelSelect,
  PromptInputModelSelectContent,
  PromptInputModelSelectItem,
  PromptInputModelSelectTrigger,
  PromptInputModelSelectValue,
} from "@/components/prompt-input";
import { Response } from "@/components/response";
import { Loader } from "@/components/loader";
import { Actions, Action } from "@/components/actions";
import {
  Reasoning,
  ReasoningContent,
  ReasoningTrigger,
} from "@/components/reasoning";
import {
  Sources,
  SourcesContent,
  SourcesTrigger,
  Source,
} from "@/components/sources";
import { <PERSON><PERSON>lock, CodeBlockCopy<PERSON><PERSON>on } from "@/components/code-block";
import {
  Refresh<PERSON><PERSON>wIcon,
  CopyIcon,
  ThumbsUpIcon,
  ThumbsDownIcon,
  MicIcon,
  PaperclipIcon,
  ImageIcon,
  CodeIcon,
} from "lucide-react";

// Define types for our enhanced messages
interface MessagePart {
  type: "text" | "reasoning" | "source-url" | "code";
  text?: string;
  url?: string;
  language?: string;
  code?: string;
}

interface EnhancedMessage {
  id: string;
  role: "user" | "assistant";
  parts: MessagePart[];
}

// Available models with more options
const models = [
  { id: "gpt-4o", name: "GPT-4o" },
  { id: "claude-3-5-sonnet", name: "Claude 3.5 Sonnet" },
  { id: "gemini-1.5-pro", name: "Gemini 1.5 Pro" },
  { id: "deepseek-r1", name: "DeepSeek R1" },
];

// Sample code snippets for demo
const sampleCodeBlocks = [
  {
    language: "typescript",
    code: `interface User {
  id: string;
  name: string;
  email: string;
}

const createUser = (userData: Partial<User>): User => {
  return {
    id: crypto.randomUUID(),
    ...userData
  } as User;
};`,
  },
  {
    language: "python",
    code: `def calculate_portfolio_return(investments):
    total_value = sum(inv['value'] for inv in investments)
    total_cost = sum(inv['cost'] for inv in investments)
    return (total_value - total_cost) / total_cost * 100

portfolio = [
    {'symbol': 'AAPL', 'cost': 1000, 'value': 1200},
    {'symbol': 'GOOGL', 'cost': 800, 'value': 950}
]

return_percentage = calculate_portfolio_return(portfolio)
print(f"Portfolio return: {return_percentage:.2f}%")`,
  },
];

const AdvancedChat = () => {
  const [input, setInput] = useState("");
  const [messages, setMessages] = useState<EnhancedMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedModel, setSelectedModel] = useState(models[0].id);

  const handleRetry = () => {
    if (messages.length > 0) {
      const lastUserMessage = [...messages]
        .reverse()
        .find((m) => m.role === "user");
      if (lastUserMessage && !isLoading) {
        setIsLoading(true);
        // Remove the last AI message and regenerate
        setMessages((prev) =>
          prev.filter((m) => m.id !== messages[messages.length - 1].id)
        );

        setTimeout(() => {
          const parts: MessagePart[] = [
            {
              type: "text",
              text: `Regenerated response using ${models.find((m) => m.id === selectedModel)?.name} for: "${lastUserMessage.parts[0].text}". This is an enhanced response with improved capabilities.`,
            },
            {
              type: "reasoning",
              text: `Regenerating response based on user request. Using improved reasoning for better response quality.`,
            },
          ];
          if (Math.random() > 0.7) {
            const codeBlock =
              sampleCodeBlocks[
                Math.floor(Math.random() * sampleCodeBlocks.length)
              ];
            parts.push({
              type: "code",
              language: codeBlock.language,
              code: codeBlock.code,
            });
          }
          const aiMessage: EnhancedMessage = {
            id: Date.now().toString(),
            role: "assistant",
            parts,
          };
          setMessages((prev) => [...prev, aiMessage]);
          setIsLoading(false);
        }, 1500);
      }
    }
  };

  const handleCopy = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim() && !isLoading) {
      const userMessage: EnhancedMessage = {
        id: Date.now().toString(),
        role: "user",
        parts: [{ type: "text", text: input }],
      };

      setMessages((prev) => [...prev, userMessage]);
      setInput("");
      setIsLoading(true);

      // Simulate AI response with enhanced features
      setTimeout(() => {
        const hasCode =
          input.toLowerCase().includes("code") ||
          input.toLowerCase().includes("function") ||
          input.toLowerCase().includes("script");
        const hasReasoningModel = selectedModel === "deepseek-r1";

        const parts: MessagePart[] = [
          {
            type: "text",
            text: `Using model ${models.find((m) => m.id === selectedModel)?.name}: I understand you're asking about "${userMessage.parts[0].text}". Here's a comprehensive response with enhanced features including code examples, reasoning, and sources when relevant.`,
          },
        ];
        if (hasReasoningModel || Math.random() > 0.6) {
          parts.push({
            type: "reasoning",
            text: `Let me think through this step by step:

1. First, I need to understand what the user is asking about: "${userMessage.parts[0].text}"
2. Based on the query, I should provide relevant information and examples
3. If this involves coding or technical concepts, I should include code snippets
4. I should also provide sources and reasoning to make the response more helpful`,
          });
        }
        if (hasCode || Math.random() > 0.5) {
          const codeBlock =
            sampleCodeBlocks[
              Math.floor(Math.random() * sampleCodeBlocks.length)
            ];
          parts.push({
            type: "code",
            language: codeBlock.language,
            code: codeBlock.code,
          });
        }
        if (Math.random() > 0.6) {
          const sources = [
            { url: "https://developer.mozilla.org", title: "MDN Web Docs" },
            { url: "https://docs.python.org", title: "Python Documentation" },
            { url: "https://reactjs.org/docs", title: "React Documentation" },
          ].slice(0, Math.floor(Math.random() * 3) + 1);
          sources.forEach((s) => {
            parts.push({ type: "source-url", url: s.url });
          });
        }
        const aiMessage: EnhancedMessage = {
          id: (Date.now() + 1).toString(),
          role: "assistant",
          parts,
        };
        setMessages((prev) => [...prev, aiMessage]);
        setIsLoading(false);
      }, 1500);
    }
  };

  return (
    <div className="max-w-4xl mx-auto bg-red-400 relative size-full rounded-lg border h-[700px]">
      <div className="flex flex-col h-full">
        <Conversation>
          <ConversationContent>
            {messages.map((message, messageIndex) => {
              const isLastMessage = messageIndex === messages.length - 1;
              const status = isLoading ? "streaming" : "ready";
              return (
                <div key={message.id}>
                  {message.role === "assistant" && (
                    <Sources>
                      <SourcesTrigger
                        count={
                          message.parts.filter(
                            (part) => part.type === "source-url"
                          ).length
                        }
                      />
                      {message.parts
                        .filter((part) => part.type === "source-url")
                        .map((part, i) => (
                          <SourcesContent key={`${message.id}-${i}`}>
                            <Source
                              key={`${message.id}-${i}`}
                              href={part.url!}
                              title={part.url!}
                            />
                          </SourcesContent>
                        ))}
                    </Sources>
                  )}
                  <Message from={message.role} key={message.id}>
                    <MessageContent>
                      {message.parts.map((part, i) => {
                        switch (part.type) {
                          case "text":
                            return (
                              <div key={`${message.id}-${i}`}>
                                <Response key={`${message.id}-${i}`}>
                                  {part.text!}
                                </Response>
                                {message.role === "assistant" &&
                                  isLastMessage && (
                                    <Actions className="mt-2">
                                      <Action
                                        onClick={handleRetry}
                                        label="Retry"
                                      >
                                        <RefreshCcwIcon className="size-3" />
                                      </Action>
                                      <Action
                                        onClick={() =>
                                          navigator.clipboard.writeText(
                                            part.text!
                                          )
                                        }
                                        label="Copy"
                                      >
                                        <CopyIcon className="size-3" />
                                      </Action>
                                    </Actions>
                                  )}
                                {message.role === "user" && (
                                  <Actions className="mt-2">
                                    <Action
                                      onClick={() =>
                                        navigator.clipboard.writeText(
                                          part.text!
                                        )
                                      }
                                      label="Copy"
                                    >
                                      <CopyIcon className="size-3" />
                                    </Action>
                                  </Actions>
                                )}
                              </div>
                            );
                          case "reasoning":
                            return (
                              <Reasoning
                                key={`${message.id}-${i}`}
                                className="w-full"
                                isStreaming={status === "streaming"}
                              >
                                <ReasoningTrigger />
                                <ReasoningContent>
                                  {part.text!}
                                </ReasoningContent>
                              </Reasoning>
                            );
                          default:
                            return null;
                        }
                      })}
                    </MessageContent>
                  </Message>
                </div>
              );
            })}
            {isLoading && <Loader />}
          </ConversationContent>
          <ConversationScrollButton />
        </Conversation>

        <PromptInput
          onSubmit={handleSubmit}
          className="mt-4 w-full max-w-2xl mx-auto"
        >
          <PromptInputTextarea
            value={input}
            placeholder="Ask about code, finances, or anything else. Try asking for code examples or technical help..."
            onChange={(e) => setInput(e.currentTarget.value)}
          />
          <PromptInputToolbar>
            <PromptInputTools>
              <PromptInputButton>
                <MicIcon size={16} />
              </PromptInputButton>
              <PromptInputButton>
                <PaperclipIcon size={16} />
                <span>Attach</span>
              </PromptInputButton>
              <PromptInputButton>
                <ImageIcon size={16} />
                <span>Image</span>
              </PromptInputButton>
              <PromptInputModelSelect
                onValueChange={(value) => setSelectedModel(value)}
                value={selectedModel}
              >
                <PromptInputModelSelectTrigger>
                  <PromptInputModelSelectValue />
                </PromptInputModelSelectTrigger>
                <PromptInputModelSelectContent>
                  {models.map((model) => (
                    <PromptInputModelSelectItem key={model.id} value={model.id}>
                      {model.name}
                    </PromptInputModelSelectItem>
                  ))}
                </PromptInputModelSelectContent>
              </PromptInputModelSelect>
            </PromptInputTools>
            <PromptInputSubmit
              status={isLoading ? "streaming" : "ready"}
              disabled={!input.trim() || isLoading}
            />
          </PromptInputToolbar>
        </PromptInput>
      </div>
    </div>
  );
};

export default AdvancedChat;
