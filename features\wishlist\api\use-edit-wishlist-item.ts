import { InferRequestType, InferResponseType } from "hono";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";

type ResponseType = InferResponseType<typeof client.api.wishlist[":id"]["$patch"]>;
type RequestType = InferRequestType<typeof client.api.wishlist[":id"]["$patch"]>["json"];

export const useEditWishlistItem = (id?: string) => {
  const userId = useCurrentUserId();
  const queryClient = useQueryClient();
  
  const mutation = useMutation<ResponseType, Error, RequestType>({
    mutationFn: async (json) => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const response = await client.api.wishlist[":id"].$patch(
        { param: { id }, json },
        {
          headers: {
            "X-User-ID": userId,
          },
        },
      );
      const data = await response.json();

      return data;
    },
    onSuccess: () => {
      toast.success("Wishlist item updated");
      queryClient.invalidateQueries({ queryKey: ["wishlist-items", userId] });
      queryClient.invalidateQueries({ queryKey: ["wishlist-item", userId, { id }] });
    },
    onError: () => {
      toast.error("Failed to update wishlist item");
    },
  });

  return mutation;
};