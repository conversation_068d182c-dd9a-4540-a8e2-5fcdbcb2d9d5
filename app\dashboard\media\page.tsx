"use client";

import React from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  FileImage, 
  Receipt, 
  FileText, 
  Image as ImageIcon,
  Upload,
  FolderOpen
} from "lucide-react";
import { MediaUploadWithPreview } from "@/components/media-upload-with-preview";
import { MediaPreview } from "@/components/media-preview";
import { MediaGallery } from "@/components/media-gallery";

export default function MediaPage() {
  return (
    <div className="flex-1 space-y-6 p-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Media Library</h1>
        <p className="text-muted-foreground">
          Upload, organize, and preview your files across different categories
        </p>
      </div>

      <Tabs defaultValue="all" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all" className="flex items-center gap-2">
            <FolderOpen className="h-4 w-4" />
            All Files
          </TabsTrigger>
          <TabsTrigger value="receipts" className="flex items-center gap-2">
            <Receipt className="h-4 w-4" />
            Receipts
          </TabsTrigger>
          <TabsTrigger value="documents" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Documents
          </TabsTrigger>
          <TabsTrigger value="images" className="flex items-center gap-2">
            <ImageIcon className="h-4 w-4" />
            Images
          </TabsTrigger>
          <TabsTrigger value="upload" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Upload
          </TabsTrigger>
        </TabsList>

        {/* All Files Tab */}
        <TabsContent value="all" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Receipt className="h-4 w-4 text-green-500" />
                  Receipts
                </CardTitle>
              </CardHeader>
              <CardContent>
                <MediaGallery 
                  category="receipts" 
                  compact={true} 
                  maxItems={4}
                  showViewToggle={false}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <FileText className="h-4 w-4 text-blue-500" />
                  Documents
                </CardTitle>
              </CardHeader>
              <CardContent>
                <MediaGallery 
                  category="documents" 
                  compact={true} 
                  maxItems={4}
                  showViewToggle={false}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <ImageIcon className="h-4 w-4 text-purple-500" />
                  Images
                </CardTitle>
              </CardHeader>
              <CardContent>
                <MediaGallery 
                  category="images" 
                  compact={true} 
                  maxItems={4}
                  showViewToggle={false}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <FileImage className="h-4 w-4 text-gray-500" />
                  General
                </CardTitle>
              </CardHeader>
              <CardContent>
                <MediaGallery 
                  category="general" 
                  compact={true} 
                  maxItems={4}
                  showViewToggle={false}
                />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FolderOpen className="h-5 w-5" />
                All Media Files
              </CardTitle>
              <CardDescription>
                Browse all your uploaded files across all categories
              </CardDescription>
            </CardHeader>
            <CardContent>
              <MediaPreview 
                title=""
                description=""
                viewMode="grid"
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Receipts Tab */}
        <TabsContent value="receipts" className="space-y-6">
          <MediaUploadWithPreview
            category="receipts"
            title="Receipt Management"
            description="Upload and organize your receipts"
            acceptedTypes={['image/jpeg', 'image/png', 'image/webp', 'application/pdf']}
            allowCamera={true}
          />
        </TabsContent>

        {/* Documents Tab */}
        <TabsContent value="documents" className="space-y-6">
          <MediaUploadWithPreview
            category="documents"
            title="Document Management"
            description="Store and organize important documents"
            acceptedTypes={['application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']}
            allowCamera={false}
          />
        </TabsContent>

        {/* Images Tab */}
        <TabsContent value="images" className="space-y-6">
          <MediaUploadWithPreview
            category="images"
            title="Image Gallery"
            description="Upload and manage your images and photos"
            acceptedTypes={['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'image/svg+xml']}
            allowCamera={true}
          />
        </TabsContent>

        {/* Upload Tab */}
        <TabsContent value="upload" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Receipt className="h-5 w-5 text-green-500" />
                  Quick Receipt Upload
                </CardTitle>
                <CardDescription>
                  Capture receipts with your camera or upload images
                </CardDescription>
              </CardHeader>
              <CardContent>
                <MediaUploadWithPreview
                  category="receipts"
                  title=""
                  description=""
                  acceptedTypes={['image/jpeg', 'image/png', 'image/webp']}
                  allowCamera={true}
                  maxFiles={5}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-500" />
                  Document Upload
                </CardTitle>
                <CardDescription>
                  Upload PDFs and other important documents
                </CardDescription>
              </CardHeader>
              <CardContent>
                <MediaUploadWithPreview
                  category="documents"
                  title=""
                  description=""
                  acceptedTypes={['application/pdf', 'text/plain', 'application/msword']}
                  allowCamera={false}
                  maxFiles={10}
                />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                General File Upload
              </CardTitle>
              <CardDescription>
                Upload any type of file for general storage
              </CardDescription>
            </CardHeader>
            <CardContent>
              <MediaUploadWithPreview
                category="general"
                title=""
                description=""
                maxFiles={15}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}