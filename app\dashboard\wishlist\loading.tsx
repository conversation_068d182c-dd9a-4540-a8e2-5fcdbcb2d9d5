import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Content } from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

export default function Loading() {
  return (
    <div className="max-w-screen-2xl mx-auto w-full pb-10">
      <Card className="border-none drop-shadow-sm">
        <CardHeader>
          <Skeleton className="h-8 w-48" />
        </CardHeader>
        <CardContent>
          <div className="h-[500px] flex items-center justify-center">
            <Loader2 className="size-4 text-slate-300 animate-spin" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}