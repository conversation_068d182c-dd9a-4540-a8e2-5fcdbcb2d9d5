{"name": "finance-tutorial", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate:pg --schema db/schema.ts --out ./drizzle", "db:migrate": "bun ./scripts/migrate.ts", "db:studio": "drizzle-kit studio", "pretty": "prettier --write \"./**/*.{js,jsx,mjs,cjs,ts,tsx,json}\""}, "dependencies": {"@aws-sdk/client-s3": "^3.879.0", "@aws-sdk/lib-storage": "^3.879.0", "@aws-sdk/s3-request-presigner": "^3.879.0", "@clerk/backend": "^1.2.4", "@daveyplate/better-auth-ui": "^2.0.12", "@hono/clerk-auth": "^2.0.0", "@hono/node-server": "^1.14.4", "@hono/zod-validator": "^0.2.2", "@hookform/resolvers": "^3.6.0", "@langchain/community": "^0.3.15", "@langchain/core": "^0.3.72", "@langchain/google-genai": "^0.2.16", "@langchain/langgraph": "^0.4.8", "@langchain/mistralai": "^0.1.1", "@mistralai/mistralai": "^1.3.4", "@neondatabase/serverless": "^0.9.3", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.1.0", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@radix-ui/react-use-controllable-state": "^1.2.2", "@tanstack/react-query": "^5.48.0", "@tanstack/react-table": "^8.17.3", "@types/dompurify": "^3.0.5", "@types/formidable": "^3.4.5", "add": "^2.0.6", "ai": "^5.0.28", "axios": "^1.7.4", "better-auth": "^1.2.10", "calc-js": "^3.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "css-mediaquery": "^0.1.2", "date-fns": "^3.6.0", "drizzle-kit": "0.20.6", "drizzle-orm": "0.30.10", "drizzle-zod": "^0.5.1", "embla-carousel-react": "^8.6.0", "form-data": "^4.0.0", "formidable": "^3.5.1", "framer-motion": "^11.13.1", "hono": "^4.4.7", "i": "^0.3.7", "install": "^0.13.0", "langchain": "^0.2.16", "lodash": "^4.17.21", "lucide-react": "^0.542.0", "matchmediaquery": "^0.4.2", "next": "14.2.4", "next-themes": "^0.3.0", "node-fetch": "^3.3.2", "p-limit": "^6.1.0", "query-string": "^9.1.0", "react": "^18.3.1", "react-countup": "^6.5.3", "react-currency-input-field": "^3.8.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.0", "react-icons": "^5.2.1", "react-papaparse": "^4.4.0", "react-responsive": "^10.0.0", "react-scripts": "^5.0.1", "react-select": "^5.8.1", "react-syntax-highlighter": "^15.6.6", "react-use": "^17.5.0", "recharts": "^2.12.7", "shadcn-ui": "^0.8.0", "shallow-equal": "^3.1.0", "sonner": "^1.5.0", "streamdown": "^1.1.6", "tailwind-merge": "^2.3.0", "tailwind-scrollbar": "^3.1.0", "tailwindcss-animate": "^1.0.7", "use-stick-to-bottom": "^1.1.1", "zod": "^3.23.8", "zustand": "^4.5.3"}, "devDependencies": {"@types/lodash": "^4.17.13", "@types/node": "^20.19.11", "@types/react": "^18", "@types/react-dom": "^18", "dompurify": "^3.1.6", "dotenv": "^16.4.5", "eslint": "^8", "eslint-config-next": "14.2.4", "openai": "^4.57.0", "pg": "^8.12.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^4.9.5"}}