import { useQuery } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";

export const useGetItem = (id?: string) => {
  const userId = useCurrentUserId();

  const query = useQuery({
    enabled: !!id && !!userId,
    queryKey: ["item", { id }],
    queryFn: async () => {
      const response = await client.api.items[":id"].$get(
        { param: { id: id! } },
        { headers: { "X-User-ID": userId! } }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch item");
      }

      const { data } = await response.json();
      return data;
    },
  });

  return query;
};