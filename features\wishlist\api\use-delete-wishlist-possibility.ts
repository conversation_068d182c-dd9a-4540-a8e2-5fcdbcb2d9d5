import { useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";
import { InferResponseType } from "hono";

type ResponseType = InferResponseType<typeof client.api.wishlist.possibilities[":id"]["$delete"]>;

export const useDeleteWishlistPossibility = () => {
  const userId = useCurrentUserId();
  const queryClient = useQueryClient();

  const mutation = useMutation<ResponseType, Error, string>({
    mutationFn: async (id: string) => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const response = await client.api.wishlist.possibilities[":id"].$delete(
        { param: { id } },
        {
          headers: {
            "X-User-ID": userId,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to delete possibility");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["wishlist-items", userId],
      });
      queryClient.invalidateQueries({
        queryKey: ["wishlist-item", userId],
      });
    },
  });

  return mutation;
};