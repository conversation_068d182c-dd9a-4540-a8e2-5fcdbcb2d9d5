import { z } from "zod";
import { Trash, Image } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import { Select } from "@/components/select";
import { AmountInput } from "@/components/account-input";
import { useOverviewNewDetails } from "../hooks/use-overview-newdetails";
import { useEffect, useState } from "react";
import { Separator } from "@/components/ui/separator";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { insertdetailsTransactionsSchema } from "@/db/schema";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { convertAmountToMiliunits } from "@/lib/utils";
import { FileInput } from "@/components/file-input";
import { MediaGallery } from "@/components/media-gallery";

const formSchema = z.object({
  categoryId: z.string().nullable().optional(),
  projectId: z.string().nullable().optional(),
  unitPrice: z.string(),
  amount: z.string(),
  quantity: z.string(),
  name: z.string().optional(),
});
const apiSchema = insertdetailsTransactionsSchema.omit({});

type FormValues = z.input<typeof formSchema>;
type ApiValues = z.input<typeof apiSchema>;

type Props = {
  id?: string;
  transactionId?: string;
  defaultValues?: FormValues;
  //TODO modify Submit : doit plutot update le react Zustand avec les valeurs
  onSubmit: (values: ApiValues, files?: FileList) => void;
  onDelete?: () => void;
  disabled?: boolean;
  categoryOptions: { label: string; value: string }[];
  projectOptions: { label: string; value: string }[];
  onCreateCategory?: (name: string) => void;
  existingFiles?: any[];
};

export const TransactionForm = ({
  id,
  transactionId,
  defaultValues,
  onSubmit,
  onDelete,
  disabled,
  projectOptions,
  categoryOptions,
  onCreateCategory,
  existingFiles = [],
}: Props) => {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: defaultValues,
  });

  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);

  const { temporalId, updateId } = useOverviewNewDetails();
  const handleSubmit = (values: any) => {
    updateId();
    const quantity = values.quantity;
    const unitPrice = parseFloat(values.unitPrice);
    const amount = parseFloat(values.amount);

    // Convert File[] to FileList for API
    const fileList = uploadedFiles.length > 0 ? (() => {
      const dt = new DataTransfer();
      uploadedFiles.forEach(file => dt.items.add(file));
      return dt.files;
    })() : undefined;

    onSubmit(
      id
        ? {
            ...values,
            amount,
            unitPrice,
            transactionId,
            quantity: +quantity,
          }
        : {
            ...values,
            id: `${temporalId}`,
            amount,
            unitPrice,
            transactionId,
            quantity: +quantity,
          },
      fileList
    );
    form.reset({
      name: "",
      quantity: "",
      categoryId: undefined, // Assuming it's nullable
      unitPrice: undefined,
      amount: undefined,
      projectId: undefined,
    });
  };

  const handleDelete = () => {
    onDelete?.();
  };

  const quantity = parseFloat(form.watch("quantity")?.replace(",", "."));
  const unitPrice = parseFloat(form.watch("unitPrice")?.replace(",", "."));

  const amount = (quantity || 0) * (unitPrice || 0);

  useEffect(() => {
    form.setValue("amount", amount.toString(), {
      shouldValidate: true,
      shouldDirty: true,
    });
  }, [amount, form]);
  return (
    <div className="flex-1">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(handleSubmit)}
          className="space-y-4 pt-4"
        >
          <FormField
            name="name"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    disabled={disabled}
                    placeholder="Add a Name"
                    {...field}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            name="quantity"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    type="number"
                    disabled={disabled}
                    placeholder="Quantity"
                    {...field}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            name="categoryId"
            control={form.control}
            render={({ field }) => {
              field.value;
              return (
                <FormItem>
                  <FormLabel>Category</FormLabel>
                  <FormControl>
                    <Select
                      placeholder="Select a category"
                      options={categoryOptions}
                      onCreate={onCreateCategory}
                      value={field.value}
                      onChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              );
            }}
          />
          <FormField
            name="projectId"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Project</FormLabel>
                <FormControl>
                  <Select
                    placeholder="Select a project"
                    options={projectOptions}
                    value={field.value}
                    onChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            name="unitPrice"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Unit Price</FormLabel>
                <FormControl>
                  <AmountInput
                    {...field}
                    disabled={disabled}
                    placeholder="0.00"
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            name="amount"
            control={form.control}
            render={({ field }) => {
              field.value = amount.toString();
              return (
                <FormItem>
                  <FormLabel>Amount</FormLabel>
                  <FormControl>
                    <AmountInput
                      value={field.value || amount.toString()}
                      onChange={field.onChange}
                      disabled={disabled}
                      placeholder="0.00"
                    />
                  </FormControl>
                </FormItem>
              );
            }}
          />
          
          <Separator />
          
          {/* Media Upload Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Image className="size-4" />
              <span className="text-sm font-medium">Transaction Item Media</span>
            </div>
            
            {id ? (
              // Edit mode: Show existing files and allow new uploads
              <div className="space-y-4">
                <MediaGallery
                  entityType="detailsTransaction"
                  entityId={id}
                  category="detailsTransaction"
                  allowDelete={true}
                  showViewToggle={false}
                  compact={false}
                />
                <div className="border-t pt-4">
                  <FileInput
                    onFilesSelected={setUploadedFiles}
                    maxFiles={10}
                    acceptedTypes={['image/jpeg', 'image/png', 'image/webp', 'application/pdf']}
                    allowCamera={true}
                    title="Add More Files"
                    description="Select additional images and documents to upload with this item"
                  />
                </div>
              </div>
            ) : (
              // Create mode: Allow file selection for upload after creation
              <FileInput
                onFilesSelected={setUploadedFiles}
                maxFiles={10}
                acceptedTypes={['image/jpeg', 'image/png', 'image/webp', 'application/pdf']}
                allowCamera={true}
                title="Select Item Images"
                description="Choose images and documents to upload with this transaction item"
              />
            )}
          </div>
          
          <Button className="w-full" disabled={disabled}>
            {id ? "Save changes" : "Add"}
          </Button>
          {!!id && (
            <Button
              type="button"
              disabled={disabled}
              onClick={handleDelete}
              className="w-full"
              variant="outline"
            >
              <Trash className="size-4 mr-2" />
              Delete
            </Button>
          )}
        </form>
      </Form>
    </div>
  );
};
