"use client";

import { <PERSON>u, ChevronDown, TrendingUp, Package, Target, FolderOpen } from "lucide-react";
import { Button } from "./ui/button";
import { usePathname, useRouter } from "next/navigation";
import { NavButton } from "./nav-button";
import { useMediaQuery } from "react-responsive";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useState } from "react";

// Single routes that don't belong to a group
const singleRoutes = [
  {
    href: "/dashboard",
    label: "Overview",
    icon: TrendingUp,
  },
];

// Grouped routes with dropdowns
const routeGroups = [
  {
    label: "Financial",
    icon: TrendingUp,
    routes: [
      { href: "/dashboard/transactions", label: "Transactions" },
      { href: "/dashboard/accounts", label: "Accounts" },
      { href: "/dashboard/categories", label: "Categories" },
    ],
  },
  {
    label: "Inventory",
    icon: Package,
    routes: [
      { href: "/dashboard/items", label: "Items" },
      { href: "/dashboard/item-transactions", label: "Item Transactions" },
    ],
  },
  {
    label: "Planning",
    icon: Target,
    routes: [
      { href: "/dashboard/projects", label: "Projects" },
      { href: "/dashboard/wishlist", label: "Wishlist" },
    ],
  },
  {
    label: "Media",
    icon: FolderOpen,
    routes: [
      { href: "/dashboard/media", label: "Media Gallery" },
    ],
  },
];

// Helper function to check if current path matches any route in a group
const isGroupActive = (groupRoutes: { href: string }[], currentPath: string) => {
  return groupRoutes.some(route => currentPath === route.href || currentPath.startsWith(route.href + '/'));
};

// Helper function to get all routes flattened for mobile menu
const getAllRoutes = () => {
  const allRoutes = [...singleRoutes];
  routeGroups.forEach(group => {
    allRoutes.push(...group.routes.map(route => ({ ...route, icon: group.icon })));
  });
  return allRoutes;
};

export const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const pathName = usePathname();
  const isMobile = useMediaQuery({ maxWidth: 1024 });
  const router = useRouter();

  const onClick = (href: string) => {
    router.push(href);
    setIsOpen(false);
  };

  if (isMobile) {
    const allRoutes = getAllRoutes();
    
    return (
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger>
          <Button
            variant="outline"
            size="sm"
            className="font-normal focus:bg-white/30 focus-visible:ring-offset-0 focus-visible:ring-transparent transition border-none bg-white/10 hover:bg-white/20 text-white hover:text-white outline-none "
          >
            <Menu className="size-4" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="px-2">
          <nav className="flex flex-col gap-y-2 pt-6">
            {allRoutes.map((route) => {
              const Icon = route.icon;
              const isActive = pathName === route.href || pathName.startsWith(route.href + '/');
              
              return (
                <Button
                  key={route.href}
                  variant={isActive ? "secondary" : "ghost"}
                  onClick={() => onClick(route.href)}
                  className="justify-start gap-2"
                >
                  {Icon && <Icon className="h-4 w-4" />}
                  {route.label}
                </Button>
              );
            })}
          </nav>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <nav className="hidden lg:flex items-center overflow-x-auto gap-x-2">
      {/* Single routes */}
      {singleRoutes.map((route) => {
        const Icon = route.icon;
        return (
          <NavButton
            key={route.href}
            href={route.href}
            label={route.label}
            isActive={pathName === route.href}
          />
        );
      })}
      
      {/* Grouped routes with dropdowns */}
      {routeGroups.map((group) => {
        const Icon = group.icon;
        const isActive = isGroupActive(group.routes, pathName);
        
        return (
          <DropdownMenu key={group.label}>
            <DropdownMenuTrigger asChild>
              <Button
                variant={isActive ? "secondary" : "ghost"}
                className="font-normal focus:bg-white/30 focus-visible:ring-offset-0 focus-visible:ring-transparent transition border-none bg-white/10 hover:bg-white/20 text-white hover:text-white outline-none gap-1"
                size="sm"
              >
                <Icon className="h-4 w-4" />
                {group.label}
                <ChevronDown className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-48">
              {group.routes.map((route) => {
                const routeActive = pathName === route.href || pathName.startsWith(route.href + '/');
                return (
                  <DropdownMenuItem
                    key={route.href}
                    onClick={() => onClick(route.href)}
                    className={routeActive ? "bg-accent" : ""}
                  >
                    {route.label}
                  </DropdownMenuItem>
                );
              })}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      })}
    </nav>
  );
};
