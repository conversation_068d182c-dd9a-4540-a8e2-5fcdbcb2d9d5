import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { getServerUserId, convertAmountFormMiliunits, convertAmountToMiliunits } from "@/lib/utils";
import { db } from "@/db/drizzle";
import { itemTransactions, items, transactions, accounts, categories, mediaFiles } from "@/db/schema";
import { insertItemTransactionSchema } from "@/db/schema";
import { createId } from "@paralleldrive/cuid2";
import { and, eq, inArray, desc, lte, gte, sql } from "drizzle-orm";
import { subDays, parse } from "date-fns";
import { getS3Storage } from "@/lib/s3-storage";

const app = new Hono()
  .get(
    "/",
    zValidator(
      "query",
      z.object({
        from: z.string().optional(),
        to: z.string().optional(),
        accountId: z.string().optional(),
        itemId: z.string().optional(),
        categoryId: z.string().optional(),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { from, to, accountId, itemId, categoryId } = c.req.valid("query");

        const defaultTo = new Date();
        const defaultFrom = subDays(defaultTo, 30);

        const startDate = from
          ? parse(from, "yyyy-MM-dd", new Date())
          : defaultFrom;
        const endDate = to ? parse(to, "yyyy-MM-dd", new Date()) : defaultTo;

        let data = await db
          .select({
            id: itemTransactions.id,
            itemId: itemTransactions.itemId,
            itemName: items.name,
            itemDescription: items.description,
            quantity: itemTransactions.quantity,
            unitPrice: itemTransactions.unitPrice,
            amount: itemTransactions.amount,
            date: transactions.date,
            transactionId: itemTransactions.transactionId,
            transactionPayee: transactions.payee,
            categoryId: itemTransactions.categoryId,
            categoryName: categories.name,
            projectId: itemTransactions.projectId,
            mediaFilesCount: sql<number>`count(${mediaFiles.id})`.as("mediaFilesCount"),
          })
          .from(itemTransactions)
          .innerJoin(items, eq(itemTransactions.itemId, items.id))
          .innerJoin(transactions, eq(itemTransactions.transactionId, transactions.id))
          .innerJoin(accounts, eq(transactions.accountId, accounts.id))
          .leftJoin(categories, eq(itemTransactions.categoryId, categories.id))
          .leftJoin(
            mediaFiles,
            and(
              eq(mediaFiles.entityId, itemTransactions.id),
              eq(mediaFiles.entityType, "itemTransaction")
            )
          )
          .where(
            and(
              eq(accounts.userId, userId),
              gte(transactions.date, startDate),
              lte(transactions.date, endDate),
              accountId ? eq(transactions.accountId, accountId) : undefined,
              itemId ? eq(itemTransactions.itemId, itemId) : undefined,
              categoryId ? eq(itemTransactions.categoryId, categoryId) : undefined,
            ),
          )
          .groupBy(
            itemTransactions.id,
            itemTransactions.itemId,
            items.name,
            items.description,
            itemTransactions.quantity,
            itemTransactions.unitPrice,
            itemTransactions.amount,
            transactions.date,
            itemTransactions.transactionId,
            transactions.payee,
            itemTransactions.categoryId,
            categories.name,
            itemTransactions.projectId
          )
          .orderBy(desc(transactions.date));

        data = data.map((item) => ({
          ...item,
          amount: convertAmountFormMiliunits(item.amount),
          unitPrice: convertAmountFormMiliunits(item.unitPrice || 0),
        }));

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .get(
    "/:id",
    zValidator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");

        const [data] = await db
          .select({
            id: itemTransactions.id,
            itemId: itemTransactions.itemId,
            itemName: items.name,
            itemDescription: items.description,
            quantity: itemTransactions.quantity,
            unitPrice: itemTransactions.unitPrice,
            amount: itemTransactions.amount,
            transactionId: itemTransactions.transactionId,
            categoryId: itemTransactions.categoryId,
            projectId: itemTransactions.projectId,
            createdAt: itemTransactions.createdAt,
            updatedAt: itemTransactions.updatedAt,
          })
          .from(itemTransactions)
          .innerJoin(items, eq(itemTransactions.itemId, items.id))
          .innerJoin(transactions, eq(itemTransactions.transactionId, transactions.id))
          .innerJoin(accounts, eq(transactions.accountId, accounts.id))
          .where(and(eq(itemTransactions.id, id), eq(accounts.userId, userId)));

        if (!data) {
          return c.json({ error: "Not found" }, 404);
        }

        const processedData = {
          ...data,
          amount: convertAmountFormMiliunits(data.amount),
          unitPrice: convertAmountFormMiliunits(data.unitPrice || 0),
        };

        return c.json({ data: processedData });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .post(
    "/",
    zValidator("json", insertItemTransactionSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const values = c.req.valid("json");

        // Verify the item belongs to the user
        const item = await db
          .select()
          .from(items)
          .where(and(eq(items.id, values.itemId), eq(items.userId, userId)))
          .then(rows => rows[0]);

        if (!item) {
          return c.json({ error: "Invalid item ID" }, 400);
        }

        // Verify the transaction belongs to the user
        const transaction = await db
          .select()
          .from(transactions)
          .innerJoin(accounts, eq(transactions.accountId, accounts.id))
          .where(and(eq(transactions.id, values.transactionId), eq(accounts.userId, userId)))
          .then(rows => rows[0]);

        if (!transaction) {
          return c.json({ error: "Invalid transaction ID" }, 400);
        }

        const [data] = await db
          .insert(itemTransactions)
          .values({
            id: createId(),
            ...values,
          })
          .returning();

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .patch(
    "/:id",
    zValidator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    zValidator("json", insertItemTransactionSchema.omit({ id: true, createdAt: true, updatedAt: true }).partial()),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");
        const values = c.req.valid("json");

        // Verify ownership before updating
        const itemTransactionToUpdate = db
          .$with("itemTransactions_to_update")
          .as(
            db
              .select({ id: itemTransactions.id })
              .from(itemTransactions)
              .innerJoin(transactions, eq(itemTransactions.transactionId, transactions.id))
              .innerJoin(accounts, eq(transactions.accountId, accounts.id))
              .where(
                and(
                  eq(itemTransactions.id, id),
                  eq(accounts.userId, userId),
                ),
              ),
          );

        const [data] = await db
          .with(itemTransactionToUpdate)
          .update(itemTransactions)
          .set({
            ...values,
            updatedAt: new Date(),
          })
          .where(
            inArray(
              itemTransactions.id,
              sql`(select id from ${itemTransactionToUpdate})`,
            ),
          )
          .returning();

        if (!data) {
          return c.json({ error: "Not found" }, 404);
        }

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .delete(
    "/:id",
    zValidator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");

        const itemTransactionToDelete = db
          .$with("itemTransactions_to_delete")
          .as(
            db
              .select({ id: itemTransactions.id })
              .from(itemTransactions)
              .innerJoin(transactions, eq(itemTransactions.transactionId, transactions.id))
              .innerJoin(accounts, eq(transactions.accountId, accounts.id))
              .where(
                and(
                  eq(itemTransactions.id, id),
                  eq(accounts.userId, userId),
                ),
              ),
          );

        const [data] = await db
          .with(itemTransactionToDelete)
          .delete(itemTransactions)
          .where(
            inArray(
              itemTransactions.id,
              sql`(select id from ${itemTransactionToDelete})`,
            ),
          )
          .returning({
            id: itemTransactions.id,
          });

        if (!data) {
          return c.json({ error: "Not found" }, 404);
        }

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .post(
    "/bulk-create",
    zValidator(
      "json",
      z.array(
        insertItemTransactionSchema.omit({
          id: true,
          createdAt: true,
          updatedAt: true,
        }),
      ),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const values = c.req.valid("json");

        // Verify all items and transactions belong to the user
        const itemIds = values.map(v => v.itemId);
        const transactionIds = values.map(v => v.transactionId);

        const userItems = await db
          .select({ id: items.id })
          .from(items)
          .where(and(inArray(items.id, itemIds), eq(items.userId, userId)));

        const userTransactions = await db
          .select({ id: transactions.id })
          .from(transactions)
          .innerJoin(accounts, eq(transactions.accountId, accounts.id))
          .where(and(inArray(transactions.id, transactionIds), eq(accounts.userId, userId)));

        if (userItems.length !== new Set(itemIds).size || userTransactions.length !== new Set(transactionIds).size) {
          return c.json({ error: "Invalid item or transaction IDs" }, 400);
        }

        const data = await db
          .insert(itemTransactions)
          .values(
            values.map((value) => ({
              id: createId(),
              ...value,
            })),
          )
          .returning();

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .post(
    "/bulk-delete",
    zValidator(
      "json",
      z.object({
        ids: z.array(z.string()),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { ids } = c.req.valid("json");

        const itemTransactionsToDelete = db
          .$with("itemTransactions_to_delete")
          .as(
            db
              .select({ id: itemTransactions.id })
              .from(itemTransactions)
              .innerJoin(transactions, eq(itemTransactions.transactionId, transactions.id))
              .innerJoin(accounts, eq(transactions.accountId, accounts.id))
              .where(
                and(
                  inArray(itemTransactions.id, ids),
                  eq(accounts.userId, userId),
                ),
              ),
          );

        const data = await db
          .with(itemTransactionsToDelete)
          .delete(itemTransactions)
          .where(
            inArray(
              itemTransactions.id,
              sql`(select id from ${itemTransactionsToDelete})`,
            ),
          )
          .returning({
            id: itemTransactions.id,
          });

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .post("/with-files", async (c) => {
    try {
      const userId = await getServerUserId(c.req.raw.headers);
      const formData = await c.req.formData();
      
      // Parse the data from form data
      const dataString = formData.get("data") as string;
      if (!dataString) {
        return c.json({ error: "Missing data" }, 400);
      }

      let itemTransactionData;
      try {
        itemTransactionData = JSON.parse(dataString);
      } catch (parseError) {
        return c.json({
          error: "Invalid JSON data",
          details: parseError instanceof Error ? parseError.message : "Unknown parse error",
        }, 400);
      }
      
      // Convert amounts to miliunits before saving
      const processedData = {
        ...itemTransactionData,
        amount: itemTransactionData.amount ? convertAmountToMiliunits(itemTransactionData.amount) : itemTransactionData.amount,
        unitPrice: itemTransactionData.unitPrice ? convertAmountToMiliunits(itemTransactionData.unitPrice) : itemTransactionData.unitPrice,
      };

      // Verify ownership of item and transaction
      const item = await db
        .select()
        .from(items)
        .where(and(eq(items.id, processedData.itemId), eq(items.userId, userId)))
        .then(rows => rows[0]);

      if (!item) {
        return c.json({ error: "Invalid item ID" }, 400);
      }

      const transaction = await db
        .select()
        .from(transactions)
        .innerJoin(accounts, eq(transactions.accountId, accounts.id))
        .where(and(eq(transactions.id, processedData.transactionId), eq(accounts.userId, userId)))
        .then(rows => rows[0]);

      if (!transaction) {
        return c.json({ error: "Invalid transaction ID" }, 400);
      }

      // Create the item transaction
      const [itemTransaction] = await db
        .insert(itemTransactions)
        .values({
          id: createId(),
          ...processedData,
        })
        .returning();

      // Handle file uploads if present
      const files: File[] = [];
      for (const [key, value] of formData.entries()) {
        if (key.startsWith("file-") && value instanceof File) {
          files.push(value);
        }
      }

      if (files.length > 0) {
        const s3Storage = getS3Storage();

        for (const file of files) {
          try {
            const result = await s3Storage.uploadFile(file, file.name, {
              userId,
              category: "itemTransaction",
              filename: `${itemTransaction.id}-${file.name}`,
              contentType: file.type,
            });

            // Save file metadata to database
            await db.insert(mediaFiles).values({
              id: createId(),
              userId,
              fileName: result.key.split('/').pop() || file.name,
              originalFileName: file.name,
              mimeType: file.type,
              fileSize: file.size,
              s3Key: result.key,
              s3Url: result.url,
              category: "itemTransaction",
              entityType: "itemTransaction",
              entityId: itemTransaction.id,
            });
          } catch (uploadError) {
            console.error("Failed to upload file:", uploadError);
          }
        }
      }

      return c.json({ 
        data: itemTransaction, 
        message: "Item transaction created successfully" 
      });
    } catch (error) {
      console.error("Error creating item transaction with files:", error);
      return c.json({ error: "Failed to create item transaction" }, 500);
    }
  })
  .patch("/:id/with-files", async (c) => {
    try {
      const userId = await getServerUserId(c.req.raw.headers);
      const { id } = c.req.param();
      const formData = await c.req.formData();
      
      // Parse the data from form data
      const dataString = formData.get("data") as string;
      if (!dataString) {
        return c.json({ error: "Missing data" }, 400);
      }

      let itemTransactionData;
      try {
        itemTransactionData = JSON.parse(dataString);
      } catch (parseError) {
        return c.json({
          error: "Invalid JSON data",
          details: parseError instanceof Error ? parseError.message : "Unknown parse error",
        }, 400);
      }

      // Convert amounts to miliunits before saving
      const processedData = {
        ...itemTransactionData,
        amount: itemTransactionData.amount ? convertAmountToMiliunits(itemTransactionData.amount) : itemTransactionData.amount,
        unitPrice: itemTransactionData.unitPrice ? convertAmountToMiliunits(itemTransactionData.unitPrice) : itemTransactionData.unitPrice,
        updatedAt: new Date(),
      };

      // Verify ownership and update the item transaction
      const itemTransactionToUpdate = db
        .$with("itemTransactions_to_update")
        .as(
          db
            .select({ id: itemTransactions.id })
            .from(itemTransactions)
            .innerJoin(transactions, eq(itemTransactions.transactionId, transactions.id))
            .innerJoin(accounts, eq(transactions.accountId, accounts.id))
            .where(
              and(
                eq(itemTransactions.id, id),
                eq(accounts.userId, userId),
              ),
            ),
        );

      const [itemTransaction] = await db
        .with(itemTransactionToUpdate)
        .update(itemTransactions)
        .set(processedData)
        .where(
          inArray(
            itemTransactions.id,
            sql`(select id from ${itemTransactionToUpdate})`,
          ),
        )
        .returning();

      if (!itemTransaction) {
        return c.json({ error: "Item transaction not found" }, 404);
      }

      // Handle file uploads if present
      const files: File[] = [];
      for (const [key, value] of formData.entries()) {
        if (key.startsWith("file-") && value instanceof File) {
          files.push(value);
        }
      }

      if (files.length > 0) {
        const s3Storage = getS3Storage();

        for (const file of files) {
          try {
            const result = await s3Storage.uploadFile(file, file.name, {
              userId,
              category: "itemTransaction",
              filename: `${id}-${file.name}`,
              contentType: file.type,
            });

            // Save file metadata to database
            await db.insert(mediaFiles).values({
              id: createId(),
              userId,
              fileName: result.key.split('/').pop() || file.name,
              originalFileName: file.name,
              mimeType: file.type,
              fileSize: file.size,
              s3Key: result.key,
              s3Url: result.url,
              category: "itemTransaction",
              entityType: "itemTransaction",
              entityId: id,
            });
          } catch (uploadError) {
            console.error("Failed to upload file:", uploadError);
          }
        }
      }

      return c.json({ 
        data: itemTransaction, 
        message: "Item transaction updated successfully" 
      });
    } catch (error) {
      console.error("Error updating item transaction with files:", error);
      return c.json({ error: "Failed to update item transaction" }, 500);
    }
  });

export default app;