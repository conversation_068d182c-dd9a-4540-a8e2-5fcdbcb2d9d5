# Enhanced Financial LangGraph Agent

This directory contains an enhanced LangGraph agent that integrates comprehensive financial tools with your existing financial application. The agent can now perform complex financial operations through natural language conversations.

## Files Overview

### Core Files
- **`agent.ts`** - Main ChatAgent class with LangGraph workflow and tool integration
- **`financial-tools-simple.ts`** - Financial tool definitions using DynamicStructuredTool
- **`example-usage.ts`** - Usage examples and integration patterns
- **`README.md`** - This documentation file

### Legacy Files
- **`financial-tools.ts`** - Initial tool implementation (replaced by financial-tools-simple.ts)

## Key Features

### 🔧 Comprehensive Financial Tools
The agent now has access to 16+ financial tools organized into categories:

**Fetch Tools:**
- `fetchCategories` - Retrieve all financial categories
- `fetchAccounts` - Retrieve all financial accounts  
- `fetchProjects` - Retrieve projects (with optional account filtering)
- `fetchTransactions` - Retrieve transactions within date ranges
- `fetchManyItems` - Retrieve transaction details within date ranges
- `fetchOneTransaction` - Retrieve specific transaction by ID
- `fetchOneItem` - Retrieve specific transaction detail by ID

**Create Tools:**
- `createOneTransaction` - Create new transactions
- `createOneItem` - Create transaction details/items
- `createAccount` - Create new financial accounts
- `createCategory` - Create categories with optional goals
- `createProject` - Create projects with budgets and dates

**Update Tools:**
- `updateTransaction` - Update existing transactions
- `updateProject` - Update existing projects
- `updateItem` - Update transaction details
- `updateCategory` - Update category information
- `updateAccount` - Update account information

**Utility Tools:**
- `getTodaysDate` - Get current date

### 🤖 Intelligent Conversation Flow
The agent uses LangGraph's conditional routing to:
1. **Understand** user requests through natural language
2. **Plan** which tools to use based on the request
3. **Execute** appropriate financial operations
4. **Respond** with formatted results and insights

### 💰 Fixed-Point Arithmetic Support
All monetary values are automatically handled with fixed-point arithmetic (multiplied by 1,000) for precise financial calculations, as required by your existing system.

### 👤 Persona-Based Context
Each tool execution is contextualized with the user's `personaId` for proper data isolation and security.

## Usage Examples

### Basic Integration
```typescript
import { ChatAgent } from "./lib/langgraph/agent";

const agent = new ChatAgent();

const result = await agent.invoke({
  messages: [], // Previous conversation history
  input: "Show me all my accounts and recent transactions",
  personaId: "user123", // User's persona ID
  media: [] // Optional media files
});

console.log(result.messages[result.messages.length - 1].content);
```

### Advanced Usage with Conversation History
```typescript
import { handleChatMessage } from "./lib/langgraph/example-usage";

const response = await handleChatMessage(
  "Create a new transaction: I spent $25.50 at Starbucks today",
  "user123",
  previousMessages, // Array of previous messages
  mediaFiles // Optional media attachments
);

if (response.success) {
  console.log("Agent Response:", response.response);
  // Update conversation history with response.messages
} else {
  console.error("Error:", response.error);
}
```

## Conversation Examples

### Financial Data Retrieval
```
User: "What accounts do I have?"
Agent: Uses fetchAccounts tool → Returns formatted list of all user accounts

User: "Show me my spending in December 2024"  
Agent: Uses fetchTransactions with date filter → Analyzes and presents spending summary
```

### Transaction Management
```
User: "I bought groceries for $85.50 at Walmart today"
Agent: Uses createOneTransaction → Creates transaction record with proper categorization

User: "Update my last transaction to $90.00"
Agent: Uses fetchTransactions + updateTransaction → Modifies the transaction amount
```

### Project and Budget Management
```
User: "Create a new project called 'Home Renovation' with a $5000 budget"
Agent: Uses createProject → Sets up project with budget tracking

User: "How much have I spent on my Home Renovation project?"
Agent: Uses fetchProjects → Analyzes project spending vs budget
```

## Architecture

### LangGraph Workflow
```
START → prepareRequest → agent → [conditional routing]
                          ↓
                      [tools] ← [if tool calls needed]
                          ↓
                      agent ← [continue conversation]
                          ↓
                        END ← [if no more tool calls]
```

### Tool Context Flow
1. **User Input** → Agent receives message with personaId
2. **Tool Creation** → Financial tools are created with personaId context
3. **Model Binding** → Gemini model is bound with contextualized tools
4. **Execution** → Tools execute with proper persona isolation
5. **Response** → Results are formatted and returned to user

## Integration with Existing System

### API Compatibility
The agent integrates seamlessly with your existing API structure:
- Uses existing `functions-ai-chat.ts` for all backend operations
- Maintains persona-based authentication via `X-Persona-ID` headers
- Preserves fixed-point arithmetic for monetary values
- Compatible with existing database schema and business logic

### Error Handling
- Comprehensive error handling for API failures
- Graceful degradation when tools are unavailable
- User-friendly error messages for common issues
- Automatic retry logic for transient failures

## Configuration

### Environment Variables
```bash
GOOGLE_API_KEY=your_gemini_api_key
NEXT_PUBLIC_APP_URL=your_app_base_url
```

### Model Configuration
The agent uses Google's Gemini 2.0 Flash model with:
- Temperature: 0.1 (for consistent responses)
- Max Retries: 2
- Context window management for long conversations

## Security Considerations

### Data Isolation
- All tools require valid `personaId` for execution
- Financial data is properly scoped to authenticated users
- No cross-persona data leakage possible

### Input Validation
- All tool inputs are validated using Zod schemas
- Monetary values are sanitized and converted properly
- Date formats are validated before processing

## Performance Optimizations

### Message History Management
- Automatic truncation of long conversation histories
- Intelligent context preservation for tool messages
- Efficient memory usage for extended conversations

### Tool Caching
- Tools are created once per conversation with personaId context
- Model binding is cached to reduce initialization overhead
- Efficient tool routing based on conversation state

## Troubleshooting

### Common Issues
1. **"GOOGLE_API_KEY not set"** - Ensure environment variable is configured
2. **"Tool execution failed"** - Check API connectivity and persona permissions
3. **"Invalid date format"** - Ensure dates are in ISO format (YYYY-MM-DD)
4. **"Amount conversion error"** - Verify numeric values for monetary amounts

### Debug Mode
Enable detailed logging by setting:
```typescript
console.log("Agent State:", state);
console.log("Tool Results:", toolResults);
```

## Future Enhancements

### Planned Features
- [ ] Batch transaction processing
- [ ] Advanced financial analytics and insights
- [ ] Receipt image processing integration
- [ ] Automated categorization suggestions
- [ ] Budget alerts and notifications
- [ ] Multi-currency support
- [ ] Export capabilities (CSV, PDF)

### Extension Points
- Custom tool development for specific business needs
- Integration with external financial services
- Advanced AI-powered financial advice
- Predictive spending analysis

## Contributing

When adding new financial tools:
1. Add the function to `functions-ai-chat.ts`
2. Create a corresponding tool in `financial-tools-simple.ts`
3. Update the tool array in `createFinancialTools()`
4. Add usage examples to `example-usage.ts`
5. Update this README with new capabilities

## Support

For issues related to:
- **Tool Integration**: Check `financial-tools-simple.ts` implementation
- **Agent Workflow**: Review `agent.ts` LangGraph configuration  
- **API Connectivity**: Verify `functions-ai-chat.ts` endpoints
- **Usage Examples**: Reference `example-usage.ts` patterns