import { ChatAgent } from "./agent";
import { BaseMessage, HumanMessage, AIMessage } from "@langchain/core/messages";

/**
 * Enhanced Chat Handler with Financial Tools
 * 
 * This replaces your existing chat handler and provides full financial tool integration
 */
export class FinancialChatHandler {
  private agent: ChatAgent;
  
  constructor() {
    this.agent = new ChatAgent();
  }

  /**
   * Process a chat message with financial tool capabilities
   */
  async processMessage(params: {
    message: string;
    personaId: string;
    conversationHistory?: BaseMessage[];
    mediaFiles?: any[];
  }) {
    const { message, personaId, conversationHistory = [], mediaFiles = [] } = params;

    try {
      console.log(`🤖 Processing message for persona: ${personaId}`);
      console.log(`📝 Message: ${message}`);

      const result = await this.agent.invoke({
        messages: conversationHistory,
        input: message,
        personaId: personaId,
        media: mediaFiles
      });

      // Extract the final response
      const finalMessage = result.messages[result.messages.length - 1];
      
      // Check if tools were called
      const toolCalls = result.messages.filter(msg => msg.tool_calls && msg.tool_calls.length > 0);
      
      return {
        success: true,
        response: finalMessage.content,
        messages: result.messages,
        toolsUsed: toolCalls.map(msg => msg.tool_calls?.map(call => call.name)).flat(),
        conversationHistory: result.messages
      };

    } catch (error) {
      console.error("❌ Chat processing error:", error);
      return {
        success: false,
        error: error.message,
        response: "I apologize, but I encountered an error processing your request. Please try again.",
        messages: conversationHistory,
        toolsUsed: [],
        conversationHistory: conversationHistory
      };
    }
  }

  /**
   * Handle specific financial queries with context
   */
  async handleFinancialQuery(params: {
    query: string;
    personaId: string;
    context?: string;
  }) {
    const { query, personaId, context } = params;

    const contextualMessage = context 
      ? `Context: ${context}\n\nQuery: ${query}`
      : query;

    return await this.processMessage({
      message: contextualMessage,
      personaId,
      conversationHistory: []
    });
  }
}

/**
 * API Route Integration Example
 * 
 * Use this in your Next.js API routes
 */
export async function handleChatAPI(req: any, res: any) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { message, personaId, conversationHistory, mediaFiles } = req.body;

    if (!message || !personaId) {
      return res.status(400).json({ error: 'Message and personaId are required' });
    }

    const chatHandler = new FinancialChatHandler();
    const result = await chatHandler.processMessage({
      message,
      personaId,
      conversationHistory,
      mediaFiles
    });

    return res.status(200).json(result);

  } catch (error) {
    console.error('API Error:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error.message 
    });
  }
}

/**
 * React Component Integration Example
 */
export class FinancialChatComponent {
  private chatHandler: FinancialChatHandler;
  private conversationHistory: BaseMessage[] = [];

  constructor() {
    this.chatHandler = new FinancialChatHandler();
  }

  async sendMessage(message: string, personaId: string) {
    const result = await this.chatHandler.processMessage({
      message,
      personaId,
      conversationHistory: this.conversationHistory
    });

    if (result.success) {
      // Update conversation history
      this.conversationHistory = result.conversationHistory;
      
      // Log tools used for debugging
      if (result.toolsUsed.length > 0) {
        console.log(`🔧 Tools used: ${result.toolsUsed.join(', ')}`);
      }
    }

    return result;
  }

  getConversationHistory() {
    return this.conversationHistory;
  }

  clearHistory() {
    this.conversationHistory = [];
  }
}

/**
 * Common Financial Queries Handler
 * 
 * Pre-built handlers for common financial operations
 */
export class FinancialOperations {
  private chatHandler: FinancialChatHandler;

  constructor() {
    this.chatHandler = new FinancialChatHandler();
  }

  // Account operations
  async getAccounts(personaId: string) {
    return await this.chatHandler.handleFinancialQuery({
      query: "Show me all my accounts with their details",
      personaId
    });
  }

  async createAccount(personaId: string, accountName: string) {
    return await this.chatHandler.handleFinancialQuery({
      query: `Create a new account called "${accountName}"`,
      personaId
    });
  }

  // Transaction operations
  async getTransactions(personaId: string, dateRange?: { from: string, to: string }) {
    const query = dateRange 
      ? `Show me transactions from ${dateRange.from} to ${dateRange.to}`
      : "Show me my recent transactions";
    
    return await this.chatHandler.handleFinancialQuery({
      query,
      personaId
    });
  }

  async createTransaction(personaId: string, transactionDetails: {
    amount: number;
    description: string;
    account?: string;
    category?: string;
  }) {
    const { amount, description, account, category } = transactionDetails;
    
    let query = `Record a transaction: ${description} for $${amount}`;
    if (account) query += ` using ${account}`;
    if (category) query += ` in category ${category}`;

    return await this.chatHandler.handleFinancialQuery({
      query,
      personaId
    });
  }

  // Category operations
  async getCategories(personaId: string) {
    return await this.chatHandler.handleFinancialQuery({
      query: "Show me all my categories with their goals and spending",
      personaId
    });
  }

  async createCategory(personaId: string, categoryName: string, monthlyGoal?: number) {
    const query = monthlyGoal
      ? `Create a category called "${categoryName}" with a monthly goal of $${monthlyGoal}`
      : `Create a category called "${categoryName}"`;

    return await this.chatHandler.handleFinancialQuery({
      query,
      personaId
    });
  }

  // Project operations
  async getProjects(personaId: string) {
    return await this.chatHandler.handleFinancialQuery({
      query: "Show me all my projects with their budgets and spending",
      personaId
    });
  }

  async createProject(personaId: string, projectDetails: {
    name: string;
    budget: number;
    startDate: string;
    endDate: string;
    description?: string;
  }) {
    const { name, budget, startDate, endDate, description } = projectDetails;
    
    let query = `Create a project called "${name}" with budget $${budget} from ${startDate} to ${endDate}`;
    if (description) query += ` with description: ${description}`;

    return await this.chatHandler.handleFinancialQuery({
      query,
      personaId
    });
  }

  // Analysis operations
  async analyzeSpending(personaId: string, period?: string) {
    const query = period 
      ? `Analyze my spending for ${period}`
      : "Analyze my recent spending patterns";

    return await this.chatHandler.handleFinancialQuery({
      query,
      personaId
    });
  }

  async checkBudgets(personaId: string) {
    return await this.chatHandler.handleFinancialQuery({
      query: "Check my budget status and show if I'm over or under budget in any categories",
      personaId
    });
  }
}

/**
 * Usage Examples
 */
export const USAGE_EXAMPLES = {
  // Basic chat integration
  basicChat: async (message: string, personaId: string) => {
    const handler = new FinancialChatHandler();
    return await handler.processMessage({ message, personaId });
  },

  // Structured operations
  structuredOperations: async (personaId: string) => {
    const ops = new FinancialOperations();
    
    // Get all accounts
    const accounts = await ops.getAccounts(personaId);
    console.log("Accounts:", accounts.response);

    // Create a transaction
    const transaction = await ops.createTransaction(personaId, {
      amount: 25.50,
      description: "Coffee at Starbucks",
      category: "Food & Dining"
    });
    console.log("Transaction:", transaction.response);

    // Analyze spending
    const analysis = await ops.analyzeSpending(personaId, "this month");
    console.log("Analysis:", analysis.response);
  },

  // Component integration
  componentIntegration: () => {
    const chatComponent = new FinancialChatComponent();
    
    // In your React component:
    const handleSendMessage = async (message: string, personaId: string) => {
      const result = await chatComponent.sendMessage(message, personaId);
      
      if (result.success) {
        // Update UI with response
        console.log("Agent Response:", result.response);
        
        // Show tools used (optional)
        if (result.toolsUsed.length > 0) {
          console.log("Tools Used:", result.toolsUsed);
        }
      } else {
        // Handle error
        console.error("Error:", result.error);
      }
    };

    return { handleSendMessage, chatComponent };
  }
};

// Export everything for easy integration
export default {
  FinancialChatHandler,
  FinancialOperations,
  FinancialChatComponent,
  handleChatAPI,
  USAGE_EXAMPLES
};