task : update 
affected : receipt transaction generator
description: actually , when the model extract the info from the image, it doesn't handle tags. i want to :
- fetch all tags 
- pass them to a second model along with te result of the first model.
- the model should then choose between the existing tag suitable tags.
- if no suitable tags exist it should propose tags.
- return an the tag that should be associated with each item.
- update the final result.
- the tags will then be created and associated to the items when submitting result of the receipt transaction to be saved in the DB.
- update the UI to properly handle the display of tag : already existing and when not existing.
- ensure the API endpoint properly handle the creation of tags.