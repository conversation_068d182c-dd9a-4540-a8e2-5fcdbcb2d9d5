import { <PERSON>o } from "hono";
import { db } from "@/db/drizzle";
import { wishlistItems, wishlistPossibilities, wishlistSources, mediaFiles, wishlistItemTags, tags } from "@/db/schema";
import { createId } from "@paralleldrive/cuid2";
import { zValidator } from "@hono/zod-validator";
import { and, eq, inArray, desc, sql } from "drizzle-orm";
import { insertWishlistItemSchema, insertWishlistPossibilitySchema, insertWishlistSourceSchema } from "@/db/schema";
import { z } from "zod";
import { getServerUserId } from "@/lib/utils";
import { getS3Storage } from "@/lib/s3-storage";

const app = new Hono()
  .get(
    "/",
    zValidator(
      "query",
      z.object({
        status: z.string().optional(),
        category: z.string().optional(),
        priority: z.string().optional(),
        tagIds: z.string().optional(), // Comma-separated tag IDs
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { status, category, priority, tagIds } = c.req.valid("query");

        const conditions = [eq(wishlistItems.userId, userId)];
        
        if (status) {
          conditions.push(eq(wishlistItems.status, status));
        }
        if (category) {
          conditions.push(eq(wishlistItems.category, category));
        }
        if (priority) {
          conditions.push(eq(wishlistItems.priority, priority));
        }
        
        // Handle tag filtering
        if (tagIds) {
          const tagIdArray = tagIds.split(',').filter(id => id.trim());
          if (tagIdArray.length > 0) {
            // Find wishlist items that have ALL of the specified tags
            const itemsWithTags = db
              .select({ wishlistItemId: wishlistItemTags.wishlistItemId })
              .from(wishlistItemTags)
              .where(inArray(wishlistItemTags.tagId, tagIdArray))
              .groupBy(wishlistItemTags.wishlistItemId)
              .having(sql`count(*) = ${tagIdArray.length}`);
              
            conditions.push(sql`${wishlistItems.id} IN (${itemsWithTags})`);
          }
        }

        const data = await db
          .select()
          .from(wishlistItems)
          .where(and(...conditions))
          .orderBy(desc(wishlistItems.createdAt));

        // Fetch possibilities, sources, and tags for each wishlist item
        const enrichedData = await Promise.all(
          data.map(async (item) => {
            const possibilities = await db
              .select()
              .from(wishlistPossibilities)
              .where(eq(wishlistPossibilities.wishlistItemId, item.id))
              .orderBy(desc(wishlistPossibilities.createdAt));

            const possibilitiesWithSources = await Promise.all(
              possibilities.map(async (possibility) => {
                const sources = await db
                  .select()
                  .from(wishlistSources)
                  .where(eq(wishlistSources.possibilityId, possibility.id))
                  .orderBy(desc(wishlistSources.createdAt));

                return { ...possibility, sources };
              })
            );

            // Fetch tags for this wishlist item
            const itemTags = await db
              .select({
                id: tags.id,
                name: tags.name,
                color: tags.color,
              })
              .from(tags)
              .innerJoin(wishlistItemTags, eq(tags.id, wishlistItemTags.tagId))
              .where(eq(wishlistItemTags.wishlistItemId, item.id))
              .orderBy(tags.name);

            return { ...item, possibilities: possibilitiesWithSources, tags: itemTags };
          })
        );

        return c.json({ data: enrichedData });
      } catch (error) {
        console.error("Error fetching wishlist items:", error);
        return c.json({ error: "Failed to fetch wishlist items" }, 500);
      }
    },
  )
  .get(
    "/:id",
    zValidator("param", z.object({ id: z.string().optional() })),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");

        if (!id) {
          return c.json({ error: "Missing id" }, 400);
        }

        const [data] = await db
          .select()
          .from(wishlistItems)
          .where(and(eq(wishlistItems.userId, userId), eq(wishlistItems.id, id)));

        if (!data) {
          return c.json({ error: "Not found" }, 404);
        }

        // Fetch possibilities and sources for this wishlist item
        const possibilities = await db
          .select()
          .from(wishlistPossibilities)
          .where(eq(wishlistPossibilities.wishlistItemId, data.id))
          .orderBy(desc(wishlistPossibilities.createdAt));

        const possibilitiesWithSources = await Promise.all(
          possibilities.map(async (possibility) => {
            const sources = await db
              .select()
              .from(wishlistSources)
              .where(eq(wishlistSources.possibilityId, possibility.id))
              .orderBy(desc(wishlistSources.createdAt));

            return { ...possibility, sources };
          })
        );

        // Fetch tags for this wishlist item
        const itemTags = await db
          .select({
            id: tags.id,
            name: tags.name,
            color: tags.color,
          })
          .from(tags)
          .innerJoin(wishlistItemTags, eq(tags.id, wishlistItemTags.tagId))
          .where(eq(wishlistItemTags.wishlistItemId, data.id))
          .orderBy(tags.name);

        const enrichedData = { ...data, possibilities: possibilitiesWithSources, tags: itemTags };

        return c.json({ data: enrichedData });
      } catch (error) {
        console.error("Error fetching wishlist item:", error);
        return c.json({ error: "Failed to fetch wishlist item" }, 500);
      }
    },
  )
  .post(
    "/",
    zValidator("json", insertWishlistItemSchema.omit({ id: true, userId: true, createdAt: true, updatedAt: true }).extend({
      tagIds: z.array(z.string()).optional(),
    })),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { tagIds, ...values } = c.req.valid("json");

        // Create the wishlist item first
        const wishlistItemId = createId();
        const [data] = await db
          .insert(wishlistItems)
          .values({
            id: wishlistItemId,
            userId,
            ...values,
          })
          .returning();

        // Add tags if provided
        if (tagIds && tagIds.length > 0) {
          const wishlistTagsToInsert = tagIds.map(tagId => ({
            id: createId(),
            wishlistItemId: wishlistItemId,
            tagId,
          }));
          
          await db.insert(wishlistItemTags).values(wishlistTagsToInsert);
        }

        return c.json({ data });
      } catch (error) {
        console.error("Error creating wishlist item:", error);
        return c.json({ error: "Failed to create wishlist item" }, 500);
      }
    },
  )
  .post(
    "/with-files",
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const formData = await c.req.formData();
        
        // Extract wishlist item data
        const { tagIds, ...itemData } = JSON.parse(formData.get("data") as string);
        
        // Create wishlist item first
        const wishlistItemId = createId();
        const [wishlistItem] = await db
          .insert(wishlistItems)
          .values({
            id: wishlistItemId,
            userId,
            ...itemData,
          })
          .returning();

        // Add tags if provided
        if (tagIds && tagIds.length > 0) {
          const wishlistTagsToInsert = tagIds.map((tagId: string) => ({
            id: createId(),
            wishlistItemId: wishlistItemId,
            tagId,
          }));
          
          await db.insert(wishlistItemTags).values(wishlistTagsToInsert);
        }

        // Handle file uploads if any
        const uploadedFiles: any[] = [];
        const s3Storage = getS3Storage();

        for (const [key, value] of formData.entries()) {
          if (key.startsWith('file-') && value instanceof File) {
            try {
              // Upload to S3
              const uploadResult = await s3Storage.uploadFile(
                value,
                value.name,
                {
                  userId,
                  category: 'images',
                  filename: `${wishlistItem.id}-${value.name}`,
                  contentType: value.type,
                }
              );

              // Save to database
              const [mediaFile] = await db
                .insert(mediaFiles)
                .values({
                  id: createId(),
                  userId,
                  fileName: uploadResult.key.split('/').pop() || value.name,
                  originalFileName: value.name,
                  mimeType: value.type,
                  fileSize: value.size,
                  s3Key: uploadResult.key,
                  s3Url: uploadResult.url,
                  category: 'images',
                  entityType: 'wishlist',
                  entityId: wishlistItem.id,
                })
                .returning();

              uploadedFiles.push(mediaFile);
            } catch (fileError) {
              console.error(`Error uploading file ${value.name}:`, fileError);
            }
          }
        }

        return c.json({ 
          data: wishlistItem, 
          uploadedFiles: uploadedFiles.length,
          message: `Wishlist item created with ${uploadedFiles.length} file(s)`
        });
      } catch (error) {
        console.error('Wishlist item creation error:', error);
        return c.json({ error: "Failed to create wishlist item" }, 500);
      }
    },
  )
  .post(
    "/bulk-delete",
    zValidator("json", z.object({ ids: z.array(z.string()) })),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const values = c.req.valid("json");

        const data = await db
          .delete(wishlistItems)
          .where(
            and(
              eq(wishlistItems.userId, userId),
              inArray(wishlistItems.id, values.ids),
            ),
          )
          .returning({ id: wishlistItems.id });

        return c.json({ data });
      } catch (error) {
        console.error("Error bulk deleting wishlist items:", error);
        return c.json({ error: "Failed to delete wishlist items" }, 500);
      }
    },
  )
  .patch(
    "/:id",
    zValidator("param", z.object({ id: z.string().optional() })),
    zValidator("json", insertWishlistItemSchema.omit({ id: true, userId: true, createdAt: true }).extend({
      tagIds: z.array(z.string()).optional(),
    }).partial()),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");
        const { tagIds, ...values } = c.req.valid("json");

        if (!id) {
          return c.json({ error: "Missing id" }, 400);
        }

        // Update the wishlist item
        const [data] = await db
          .update(wishlistItems)
          .set({
            ...values,
            updatedAt: new Date(),
          })
          .where(and(eq(wishlistItems.userId, userId), eq(wishlistItems.id, id)))
          .returning();

        if (!data) {
          return c.json({ error: "Not found" }, 404);
        }

        // Update tags if provided
        if (tagIds !== undefined) {
          // Delete existing tags
          await db.delete(wishlistItemTags).where(eq(wishlistItemTags.wishlistItemId, id));
          
          // Add new tags
          if (tagIds.length > 0) {
            const wishlistTagsToInsert = tagIds.map(tagId => ({
              id: createId(),
              wishlistItemId: id,
              tagId,
            }));
            
            await db.insert(wishlistItemTags).values(wishlistTagsToInsert);
          }
        }

        return c.json({ data });
      } catch (error) {
        console.error("Error updating wishlist item:", error);
        return c.json({ error: "Failed to update wishlist item" }, 500);
      }
    },
  )
  .patch(
    "/:id/with-files",
    zValidator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");
        const formData = await c.req.formData();
        
        // Extract wishlist item data
        const { tagIds, ...itemData } = JSON.parse(formData.get("data") as string);
        
        // Update wishlist item
        const [wishlistItem] = await db
          .update(wishlistItems)
          .set({
            ...itemData,
            updatedAt: new Date(),
          })
          .where(and(eq(wishlistItems.userId, userId), eq(wishlistItems.id, id)))
          .returning();

        // Update tags if provided
        if (tagIds !== undefined) {
          // Delete existing tags
          await db.delete(wishlistItemTags).where(eq(wishlistItemTags.wishlistItemId, id));
          
          // Add new tags
          if (tagIds.length > 0) {
            const wishlistTagsToInsert = tagIds.map((tagId: string) => ({
              id: createId(),
              wishlistItemId: id,
              tagId,
            }));
            
            await db.insert(wishlistItemTags).values(wishlistTagsToInsert);
          }
        }

        if (!wishlistItem) {
          return c.json({ error: "Wishlist item not found" }, 404);
        }

        // Handle file uploads if any
        const uploadedFiles: any[] = [];
        const s3Storage = getS3Storage();

        for (const [key, value] of formData.entries()) {
          if (key.startsWith('file-') && value instanceof File) {
            try {
              // Upload to S3
              const uploadResult = await s3Storage.uploadFile(
                value,
                value.name,
                {
                  userId,
                  category: 'images',
                  filename: `${wishlistItem.id}-${value.name}`,
                  contentType: value.type,
                }
              );

              // Save to database
              const [mediaFile] = await db
                .insert(mediaFiles)
                .values({
                  id: createId(),
                  userId,
                  fileName: uploadResult.key.split('/').pop() || value.name,
                  originalFileName: value.name,
                  mimeType: value.type,
                  fileSize: value.size,
                  s3Key: uploadResult.key,
                  s3Url: uploadResult.url,
                  category: 'images',
                  entityType: 'wishlist',
                  entityId: wishlistItem.id,
                })
                .returning();

              uploadedFiles.push(mediaFile);
            } catch (fileError) {
              console.error(`Error uploading file ${value.name}:`, fileError);
            }
          }
        }

        return c.json({ 
          data: wishlistItem, 
          uploadedFiles: uploadedFiles.length,
          message: `Wishlist item updated with ${uploadedFiles.length} new file(s)`
        });
      } catch (error) {
        console.error('Wishlist item update error:', error);
        return c.json({ error: "Failed to update wishlist item" }, 500);
      }
    },
  )
  .delete(
    "/:id",
    zValidator("param", z.object({ id: z.string().optional() })),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");

        if (!id) {
          return c.json({ error: "Missing id" }, 400);
        }

        const [data] = await db
          .delete(wishlistItems)
          .where(and(eq(wishlistItems.userId, userId), eq(wishlistItems.id, id)))
          .returning({ id: wishlistItems.id });

        if (!data) {
          return c.json({ error: "Not found" }, 404);
        }

        return c.json({ data });
      } catch (error) {
        console.error("Error deleting wishlist item:", error);
        return c.json({ error: "Failed to delete wishlist item" }, 500);
      }
    },
  )
  // Possibilities endpoints
  .post(
    "/:id/possibilities",
    zValidator("param", z.object({ id: z.string() })),
    zValidator("json", insertWishlistPossibilitySchema.omit({ id: true, wishlistItemId: true, createdAt: true, updatedAt: true })),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");
        const values = c.req.valid("json");

        // Verify wishlist item belongs to user
        const [wishlistItem] = await db
          .select()
          .from(wishlistItems)
          .where(and(eq(wishlistItems.userId, userId), eq(wishlistItems.id, id)));

        if (!wishlistItem) {
          return c.json({ error: "Wishlist item not found" }, 404);
        }

        const [data] = await db
          .insert(wishlistPossibilities)
          .values({
            id: createId(),
            wishlistItemId: id,
            ...values,
          })
          .returning();

        return c.json({ data });
      } catch (error) {
        console.error("Error creating possibility:", error);
        return c.json({ error: "Failed to create possibility" }, 500);
      }
    },
  )
  .post(
    "/:id/possibilities/with-files",
    zValidator("param", z.object({ id: z.string() })),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");
        const formData = await c.req.formData();
        
        // Extract possibility data
        const possibilityData = JSON.parse(formData.get("data") as string);

        // Verify wishlist item belongs to user
        const [wishlistItem] = await db
          .select()
          .from(wishlistItems)
          .where(and(eq(wishlistItems.userId, userId), eq(wishlistItems.id, id)));

        if (!wishlistItem) {
          return c.json({ error: "Wishlist item not found" }, 404);
        }

        // Create possibility first
        const [possibility] = await db
          .insert(wishlistPossibilities)
          .values({
            id: createId(),
            wishlistItemId: id,
            ...possibilityData,
          })
          .returning();

        // Handle file uploads if any
        const uploadedFiles: any[] = [];
        const s3Storage = getS3Storage();

        for (const [key, value] of formData.entries()) {
          if (key.startsWith('file-') && value instanceof File) {
            try {
              // Upload to S3
              const uploadResult = await s3Storage.uploadFile(
                value,
                value.name,
                {
                  userId,
                  category: 'images',
                  filename: `${possibility.id}-${value.name}`,
                  contentType: value.type,
                }
              );

              // Save to database
              const [mediaFile] = await db
                .insert(mediaFiles)
                .values({
                  id: createId(),
                  userId,
                  fileName: uploadResult.key.split('/').pop() || value.name,
                  originalFileName: value.name,
                  mimeType: value.type,
                  fileSize: value.size,
                  s3Key: uploadResult.key,
                  s3Url: uploadResult.url,
                  category: 'images',
                  entityType: 'wishlist_possibility',
                  entityId: possibility.id,
                })
                .returning();

              uploadedFiles.push(mediaFile);
            } catch (fileError) {
              console.error(`Error uploading file ${value.name}:`, fileError);
            }
          }
        }

        return c.json({ 
          data: possibility, 
          uploadedFiles: uploadedFiles.length,
          message: `Possibility created with ${uploadedFiles.length} file(s)`
        });
      } catch (error) {
        console.error('Possibility creation error:', error);
        return c.json({ error: "Failed to create possibility" }, 500);
      }
    },
  )
  .patch(
    "/possibilities/:id",
    zValidator("param", z.object({ id: z.string() })),
    zValidator("json", insertWishlistPossibilitySchema.omit({ id: true, wishlistItemId: true, createdAt: true }).partial()),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");
        const values = c.req.valid("json");

        // Verify possibility belongs to user's wishlist item
        const [possibility] = await db
          .select({ 
            id: wishlistPossibilities.id,
            wishlistItem: {
              userId: wishlistItems.userId
            }
          })
          .from(wishlistPossibilities)
          .innerJoin(wishlistItems, eq(wishlistPossibilities.wishlistItemId, wishlistItems.id))
          .where(and(
            eq(wishlistPossibilities.id, id),
            eq(wishlistItems.userId, userId)
          ));

        if (!possibility) {
          return c.json({ error: "Possibility not found" }, 404);
        }

        const [data] = await db
          .update(wishlistPossibilities)
          .set({
            ...values,
            updatedAt: new Date(),
          })
          .where(eq(wishlistPossibilities.id, id))
          .returning();

        return c.json({ data });
      } catch (error) {
        console.error("Error updating possibility:", error);
        return c.json({ error: "Failed to update possibility" }, 500);
      }
    },
  )
  .patch(
    "/possibilities/:id/with-files",
    zValidator("param", z.object({ id: z.string() })),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");
        const formData = await c.req.formData();
        
        // Extract possibility data
        const possibilityData = JSON.parse(formData.get("data") as string);

        // Verify possibility belongs to user's wishlist item
        const [existingPossibility] = await db
          .select({ 
            id: wishlistPossibilities.id,
            wishlistItem: {
              userId: wishlistItems.userId
            }
          })
          .from(wishlistPossibilities)
          .innerJoin(wishlistItems, eq(wishlistPossibilities.wishlistItemId, wishlistItems.id))
          .where(and(
            eq(wishlistPossibilities.id, id),
            eq(wishlistItems.userId, userId)
          ));

        if (!existingPossibility) {
          return c.json({ error: "Possibility not found" }, 404);
        }

        // Update possibility
        const [possibility] = await db
          .update(wishlistPossibilities)
          .set({
            ...possibilityData,
            updatedAt: new Date(),
          })
          .where(eq(wishlistPossibilities.id, id))
          .returning();

        // Handle file uploads if any
        const uploadedFiles: any[] = [];
        const s3Storage = getS3Storage();

        for (const [key, value] of formData.entries()) {
          if (key.startsWith('file-') && value instanceof File) {
            try {
              // Upload to S3
              const uploadResult = await s3Storage.uploadFile(
                value,
                value.name,
                {
                  userId,
                  category: 'images',
                  filename: `${possibility.id}-${value.name}`,
                  contentType: value.type,
                }
              );

              // Save to database
              const [mediaFile] = await db
                .insert(mediaFiles)
                .values({
                  id: createId(),
                  userId,
                  fileName: uploadResult.key.split('/').pop() || value.name,
                  originalFileName: value.name,
                  mimeType: value.type,
                  fileSize: value.size,
                  s3Key: uploadResult.key,
                  s3Url: uploadResult.url,
                  category: 'images',
                  entityType: 'wishlist_possibility',
                  entityId: possibility.id,
                })
                .returning();

              uploadedFiles.push(mediaFile);
            } catch (fileError) {
              console.error(`Error uploading file ${value.name}:`, fileError);
            }
          }
        }

        return c.json({ 
          data: possibility, 
          uploadedFiles: uploadedFiles.length,
          message: `Possibility updated with ${uploadedFiles.length} new file(s)`
        });
      } catch (error) {
        console.error('Possibility update error:', error);
        return c.json({ error: "Failed to update possibility" }, 500);
      }
    },
  )
  .delete(
    "/possibilities/:id",
    zValidator("param", z.object({ id: z.string() })),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");

        // Verify possibility belongs to user's wishlist item
        const [possibility] = await db
          .select({ 
            id: wishlistPossibilities.id,
            wishlistItem: {
              userId: wishlistItems.userId
            }
          })
          .from(wishlistPossibilities)
          .innerJoin(wishlistItems, eq(wishlistPossibilities.wishlistItemId, wishlistItems.id))
          .where(and(
            eq(wishlistPossibilities.id, id),
            eq(wishlistItems.userId, userId)
          ));

        if (!possibility) {
          return c.json({ error: "Possibility not found" }, 404);
        }

        const [data] = await db
          .delete(wishlistPossibilities)
          .where(eq(wishlistPossibilities.id, id))
          .returning({ id: wishlistPossibilities.id });

        return c.json({ data });
      } catch (error) {
        console.error("Error deleting possibility:", error);
        return c.json({ error: "Failed to delete possibility" }, 500);
      }
    },
  )
  // Sources endpoints
  .post(
    "/possibilities/:id/sources",
    zValidator("param", z.object({ id: z.string() })),
    zValidator("json", insertWishlistSourceSchema.omit({ id: true, possibilityId: true, createdAt: true, updatedAt: true })),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");
        const values = c.req.valid("json");

        // Verify possibility belongs to user's wishlist item
        const [possibility] = await db
          .select({ 
            id: wishlistPossibilities.id,
            wishlistItem: {
              userId: wishlistItems.userId
            }
          })
          .from(wishlistPossibilities)
          .innerJoin(wishlistItems, eq(wishlistPossibilities.wishlistItemId, wishlistItems.id))
          .where(and(
            eq(wishlistPossibilities.id, id),
            eq(wishlistItems.userId, userId)
          ));

        if (!possibility) {
          return c.json({ error: "Possibility not found" }, 404);
        }

        const [data] = await db
          .insert(wishlistSources)
          .values({
            id: createId(),
            possibilityId: id,
            ...values,
          })
          .returning();

        return c.json({ data });
      } catch (error) {
        console.error("Error creating source:", error);
        return c.json({ error: "Failed to create source" }, 500);
      }
    },
  )
  .patch(
    "/sources/:id",
    zValidator("param", z.object({ id: z.string() })),
    zValidator("json", insertWishlistSourceSchema.omit({ id: true, possibilityId: true, createdAt: true }).partial()),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");
        const values = c.req.valid("json");

        // Verify source belongs to user's wishlist item
        const [source] = await db
          .select({
            id: wishlistSources.id,
            userId: wishlistItems.userId
          })
          .from(wishlistSources)
          .innerJoin(wishlistPossibilities, eq(wishlistSources.possibilityId, wishlistPossibilities.id))
          .innerJoin(wishlistItems, eq(wishlistPossibilities.wishlistItemId, wishlistItems.id))
          .where(and(
            eq(wishlistSources.id, id),
            eq(wishlistItems.userId, userId)
          ));

        if (!source) {
          return c.json({ error: "Source not found" }, 404);
        }

        const [data] = await db
          .update(wishlistSources)
          .set({
            ...values,
            updatedAt: new Date(),
          })
          .where(eq(wishlistSources.id, id))
          .returning();

        return c.json({ data });
      } catch (error) {
        console.error("Error updating source:", error);
        return c.json({ error: "Failed to update source" }, 500);
      }
    },
  )
  .delete(
    "/sources/:id",
    zValidator("param", z.object({ id: z.string() })),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");

        // Verify source belongs to user's wishlist item
        const [source] = await db
          .select({
            id: wishlistSources.id,
            userId: wishlistItems.userId
          })
          .from(wishlistSources)
          .innerJoin(wishlistPossibilities, eq(wishlistSources.possibilityId, wishlistPossibilities.id))
          .innerJoin(wishlistItems, eq(wishlistPossibilities.wishlistItemId, wishlistItems.id))
          .where(and(
            eq(wishlistSources.id, id),
            eq(wishlistItems.userId, userId)
          ));

        if (!source) {
          return c.json({ error: "Source not found" }, 404);
        }

        const [data] = await db
          .delete(wishlistSources)
          .where(eq(wishlistSources.id, id))
          .returning({ id: wishlistSources.id });

        return c.json({ data });
      } catch (error) {
        console.error("Error deleting source:", error);
        return c.json({ error: "Failed to delete source" }, 500);
      }
    },
  );

export default app;