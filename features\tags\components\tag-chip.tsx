"use client";

import { X } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

interface TagChipProps {
  id: string;
  name: string;
  color?: string | null;
  variant?: "default" | "secondary" | "destructive" | "outline";
  onRemove?: (id: string) => void;
  removable?: boolean;
  size?: "sm" | "default" | "lg";
  onClick?: () => void;
}

export function TagChip({
  id,
  name,
  color,
  variant = "secondary",
  onRemove,
  removable = false,
  size = "default",
  onClick,
}: TagChipProps) {
  const chipStyle = color
    ? {
        backgroundColor: color,
        color: getContrastColor(color),
        borderColor: color,
      }
    : undefined;

  return (
    <Badge
      variant={variant}
      className={`${size === "sm" ? "text-xs px-2 py-0.5" : ""} ${
        removable ? "pr-1" : ""
      } inline-flex items-center gap-1 ${
        onClick ? "cursor-pointer hover:opacity-80" : ""
      }`}
      style={chipStyle}
      onClick={onClick}
    >
      <span>{name}</span>
      {removable && onRemove && (
        <Button
          variant="ghost"
          size="sm"
          className="h-3 w-3 p-0 hover:bg-transparent"
          onClick={(e) => {
            e.stopPropagation();
            onRemove(id);
          }}
        >
          <X className="h-2 w-2" />
        </Button>
      )}
    </Badge>
  );
}

// Helper function to determine contrast color for readability
function getContrastColor(hexColor: string): string {
  // Remove # if present
  const color = hexColor.replace("#", "");
  
  // Convert to RGB
  const r = parseInt(color.substr(0, 2), 16);
  const g = parseInt(color.substr(2, 2), 16);
  const b = parseInt(color.substr(4, 2), 16);
  
  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  
  // Return black or white based on luminance
  return luminance > 0.5 ? "#000000" : "#ffffff";
}