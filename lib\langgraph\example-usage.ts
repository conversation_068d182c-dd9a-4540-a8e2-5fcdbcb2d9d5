import { ChatAgent } from "./agent";
import { HumanMessage } from "@langchain/core/messages";

// Example usage of the enhanced ChatAgent with financial tools
export async function exampleUsage() {
  const agent = new ChatAgent();
  
  // Example 1: Fetch financial data
  const result1 = await agent.invoke({
    messages: [],
    input: "Show me all my accounts and categories",
    personaId: "user123", // Replace with actual persona ID
    media: []
  });
  
  console.log("Accounts and Categories:", result1.messages[result1.messages.length - 1].content);
  
  // Example 2: Create a new transaction
  const result2 = await agent.invoke({
    messages: [],
    input: "Create a new transaction: I spent $25.50 at Starbucks today using my checking account. Categorize it as 'Food & Dining'",
    personaId: "user123",
    media: []
  });
  
  console.log("Transaction Creation:", result2.messages[result2.messages.length - 1].content);
  
  // Example 3: Analyze spending patterns
  const result3 = await agent.invoke({
    messages: [],
    input: "Show me all my transactions from January 2024 and analyze my spending patterns",
    personaId: "user123",
    media: []
  });
  
  console.log("Spending Analysis:", result3.messages[result3.messages.length - 1].content);
  
  // Example 4: Create a project with budget
  const result4 = await agent.invoke({
    messages: [],
    input: "Create a new project called 'Home Renovation' with a budget of $5000, starting January 1st 2024 and ending June 30th 2024",
    personaId: "user123",
    media: []
  });
  
  console.log("Project Creation:", result4.messages[result4.messages.length - 1].content);
}

// Example of how to integrate with your existing chat system
export async function handleChatMessage(
  userMessage: string, 
  personaId: string, 
  previousMessages: any[] = [],
  mediaFiles: any[] = []
) {
  const agent = new ChatAgent();
  
  try {
    const result = await agent.invoke({
      messages: previousMessages,
      input: userMessage,
      personaId: personaId,
      media: mediaFiles
    });
    
    // Extract the final response
    const finalMessage = result.messages[result.messages.length - 1];
    return {
      success: true,
      response: finalMessage.content,
      messages: result.messages
    };
  } catch (error) {
    console.error("Chat agent error:", error);
    return {
      success: false,
      error: error.message,
      response: "I apologize, but I encountered an error processing your request. Please try again."
    };
  }
}

// Example tool capabilities the agent now has:
export const AGENT_CAPABILITIES = {
  fetch: [
    "fetchCategories - Get all financial categories",
    "fetchAccounts - Get all financial accounts", 
    "fetchProjects - Get all projects (optionally filtered by account)",
    "fetchTransactions - Get transactions within date range (optionally filtered by account)",
    "fetchManyItems - Get transaction details within date range",
    "fetchOneTransaction - Get specific transaction by ID",
    "fetchOneItem - Get specific transaction detail by ID"
  ],
  create: [
    "createOneTransaction - Create a new transaction",
    "createOneItem - Create transaction details/items",
    "createAccount - Create a new financial account",
    "createCategory - Create a new category with optional goal",
    "createProject - Create a new project with budget and dates"
  ],
  update: [
    "updateTransaction - Update existing transaction",
    "updateProject - Update existing project",
    "updateItem - Update transaction details",
    "updateCategory - Update category information",
    "updateAccount - Update account information"
  ],
  utility: [
    "getTodaysDate - Get current date"
  ]
};

// Example conversation flows the agent can handle:
export const EXAMPLE_CONVERSATIONS = [
  {
    user: "What accounts do I have?",
    agent: "Uses fetchAccounts tool to retrieve and display all user accounts"
  },
  {
    user: "Show me my spending in December 2024",
    agent: "Uses fetchTransactions with date range to get December transactions and analyzes spending"
  },
  {
    user: "Create a new category called 'Gym Membership' with a monthly goal of $50",
    agent: "Uses createCategory tool to create the category with the specified goal"
  },
  {
    user: "I bought groceries for $85.50 at Walmart today",
    agent: "Uses createOneTransaction to record the purchase, may ask for account if not specified"
  },
  {
    user: "Update my Home Renovation project budget to $6000",
    agent: "Uses fetchProjects to find the project, then updateProject to change the budget"
  },
  {
    user: "Show me all transactions for my Chase account in 2024",
    agent: "Uses fetchAccounts to find Chase account ID, then fetchTransactions filtered by account and date"
  }
];