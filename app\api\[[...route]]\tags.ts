import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { getServerUserId } from "@/lib/utils";
import { db } from "@/db/drizzle";
import { tags, itemTags, wishlistItemTags } from "@/db/schema";
import { insertTagSchema } from "@/db/schema";
import { createId } from "@paralleldrive/cuid2";
import { and, eq, ilike, sql } from "drizzle-orm";

const app = new Hono()
  .get(
    "/",
    zValidator(
      "query",
      z.object({
        search: z.string().optional(),
        limit: z.coerce.number().optional().default(50),
        offset: z.coerce.number().optional().default(0),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { search, limit, offset } = c.req.valid("query");

        let query = db
          .select({
            id: tags.id,
            name: tags.name,
            color: tags.color,
            usageCount: sql<number>`
              (SELECT COUNT(*)::int FROM ${itemTags} WHERE ${itemTags.tagId} = ${tags.id}) +
              (SELECT COUNT(*)::int FROM ${wishlistItemTags} WHERE ${wishlistItemTags.tagId} = ${tags.id})
            `.as("usageCount"),
            createdAt: tags.createdAt,
            updatedAt: tags.updatedAt,
          })
          .from(tags)
          .where(eq(tags.userId, userId))
          .limit(limit)
          .offset(offset)
          .orderBy(tags.name);

        if (search) {
          query = query.where(
            and(
              eq(tags.userId, userId),
              ilike(tags.name, `%${search}%`)
            )
          );
        }

        const data = await query;

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .get(
    "/:id",
    zValidator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");

        const [data] = await db
          .select({
            id: tags.id,
            name: tags.name,
            color: tags.color,
            usageCount: sql<number>`
              (SELECT COUNT(*)::int FROM ${itemTags} WHERE ${itemTags.tagId} = ${tags.id}) +
              (SELECT COUNT(*)::int FROM ${wishlistItemTags} WHERE ${wishlistItemTags.tagId} = ${tags.id})
            `.as("usageCount"),
            createdAt: tags.createdAt,
            updatedAt: tags.updatedAt,
          })
          .from(tags)
          .where(and(eq(tags.id, id), eq(tags.userId, userId)));

        if (!data) {
          return c.json({ error: "Not found" }, 404);
        }

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .post(
    "/",
    zValidator("json", insertTagSchema.omit({ id: true, userId: true, createdAt: true, updatedAt: true })),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const values = c.req.valid("json");

        // Check if tag with same name already exists for this user
        const existingTag = await db
          .select()
          .from(tags)
          .where(
            and(
              eq(tags.userId, userId),
              eq(sql`TRIM(LOWER(${tags.name}))`, values.name.trim().toLowerCase())
            )
          )
          .then(rows => rows[0]);

        if (existingTag) {
          return c.json({ error: "Tag with this name already exists" }, 400);
        }

        const [data] = await db
          .insert(tags)
          .values({
            id: createId(),
            ...values,
            name: values.name.trim(),
            userId,
          })
          .returning();

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .patch(
    "/:id",
    zValidator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    zValidator("json", insertTagSchema.omit({ id: true, userId: true, createdAt: true, updatedAt: true }).partial()),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");
        const values = c.req.valid("json");

        // If updating name, check for duplicates
        if (values.name) {
          const existingTag = await db
            .select()
            .from(tags)
            .where(
              and(
                eq(tags.userId, userId),
                eq(sql`TRIM(LOWER(${tags.name}))`, values.name.trim().toLowerCase()),
                sql`${tags.id} != ${id}`
              )
            )
            .then(rows => rows[0]);

          if (existingTag) {
            return c.json({ error: "Tag with this name already exists" }, 400);
          }
        }

        const updateData: any = {
          ...values,
          updatedAt: new Date(),
        };

        if (values.name) {
          updateData.name = values.name.trim();
        }

        const [data] = await db
          .update(tags)
          .set(updateData)
          .where(and(eq(tags.id, id), eq(tags.userId, userId)))
          .returning();

        if (!data) {
          return c.json({ error: "Not found" }, 404);
        }

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .delete(
    "/:id",
    zValidator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");

        // Check if tag is used in any items or wishlist items
        const itemUsageCount = await db
          .select({ count: sql<number>`count(*)` })
          .from(itemTags)
          .where(eq(itemTags.tagId, id))
          .then(rows => rows[0]?.count || 0);

        const wishlistUsageCount = await db
          .select({ count: sql<number>`count(*)` })
          .from(wishlistItemTags)
          .where(eq(wishlistItemTags.tagId, id))
          .then(rows => rows[0]?.count || 0);

        const totalUsage = itemUsageCount + wishlistUsageCount;

        if (totalUsage > 0) {
          return c.json({ 
            error: `Cannot delete tag. It is used in ${totalUsage} item(s)/wishlist item(s).` 
          }, 400);
        }

        const [data] = await db
          .delete(tags)
          .where(and(eq(tags.id, id), eq(tags.userId, userId)))
          .returning({
            id: tags.id,
          });

        if (!data) {
          return c.json({ error: "Not found" }, 404);
        }

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  );

export default app;