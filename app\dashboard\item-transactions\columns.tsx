"use client";

import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, Package, Calendar, Image } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { formatCurrency } from "@/lib/utils";
import { format } from "date-fns";
import { MediaTablePreview } from "@/components/media-table-preview";

export type ItemTransactionColumn = {
  id: string;
  itemId: string;
  itemName: string;
  itemDescription?: string | null;
  quantity?: number | null;
  unitPrice: number;
  amount: number;
  date: string;
  transactionId: string;
  transactionPayee: string;
  categoryId?: string | null;
  categoryName?: string | null;
  mediaFilesCount: number;
};

export const columns: ColumnDef<ItemTransactionColumn>[] = [
  {
    accessorKey: "date",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="p-0 h-auto font-medium"
        >
          <Calendar className="mr-2 h-4 w-4" />
          Date
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const date = new Date(row.getValue("date"));
      return (
        <div className="flex items-center">
          <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
          {format(date, "MMM dd, yyyy")}
        </div>
      );
    },
  },
  {
    accessorKey: "itemName",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="p-0 h-auto font-medium"
        >
          <Package className="mr-2 h-4 w-4" />
          Item
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const name = row.getValue("itemName") as string;
      const description = row.original.itemDescription;
      return (
        <div className="flex flex-col">
          <div className="font-medium">{name}</div>
          {description && (
            <div className="text-sm text-muted-foreground truncate max-w-[200px]">
              {description}
            </div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "transactionPayee",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="p-0 h-auto font-medium"
        >
          Payee
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="font-medium">
          {row.getValue("transactionPayee")}
        </div>
      );
    },
  },
  {
    accessorKey: "categoryName",
    header: "Category",
    cell: ({ row }) => {
      const category = row.getValue("categoryName") as string | null;
      if (!category) {
        return <span className="text-muted-foreground">Uncategorized</span>;
      }
      return (
        <Badge variant="secondary" className="font-medium">
          {category}
        </Badge>
      );
    },
  },
  {
    accessorKey: "quantity",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="p-0 h-auto font-medium"
        >
          Qty
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const quantity = row.getValue("quantity") as number | null;
      return (
        <div className="text-right">
          {quantity ? quantity.toLocaleString() : "1"}
        </div>
      );
    },
  },
  {
    accessorKey: "unitPrice",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="p-0 h-auto font-medium"
        >
          Unit Price
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const unitPrice = parseFloat(row.getValue("unitPrice"));
      return (
        <div className="text-right font-medium">
          {formatCurrency(unitPrice)}
        </div>
      );
    },
  },
  {
    accessorKey: "amount",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="p-0 h-auto font-medium"
        >
          Total Amount
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("amount"));
      return (
        <div className="text-right font-medium">
          {formatCurrency(amount)}
        </div>
      );
    },
  },
  {
    id: "media",
    header: () => {
      return (
        <div className="flex items-center gap-2">
          <Image className="h-4 w-4" />
          Media
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <MediaTablePreview
          entityType="item-transaction"
          entityId={row.original.id}
          compact={true}
        />
      );
    },
  },
];