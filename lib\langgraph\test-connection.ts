import { ChatAgent } from "./agent";
import { HumanMessage } from "@langchain/core/messages";

// Simple test to verify tool connection
export async function testToolConnection() {
  console.log("🧪 Testing Financial Tools Connection...");
  
  const agent = new ChatAgent();
  
  try {
    // Test 1: Simple tool call - fetch accounts
    console.log("\n📋 Test 1: Fetching accounts...");
    const result1 = await agent.invoke({
      messages: [],
      input: "Show me all my accounts",
      personaId: "test-user-123",
      media: []
    });
    
    console.log("✅ Test 1 Result:", result1.messages[result1.messages.length - 1].content);
    
    // Test 2: Tool call with parameters - fetch transactions
    console.log("\n📊 Test 2: Fetching transactions...");
    const result2 = await agent.invoke({
      messages: [],
      input: "Show me transactions from January 1, 2024 to January 31, 2024",
      personaId: "test-user-123",
      media: []
    });
    
    console.log("✅ Test 2 Result:", result2.messages[result2.messages.length - 1].content);
    
    // Test 3: Creation tool - create account
    console.log("\n🏦 Test 3: Creating account...");
    const result3 = await agent.invoke({
      messages: [],
      input: "Create a new account called 'Test Savings Account'",
      personaId: "test-user-123",
      media: []
    });
    
    console.log("✅ Test 3 Result:", result3.messages[result3.messages.length - 1].content);
    
    console.log("\n🎉 All tests completed successfully!");
    return true;
    
  } catch (error) {
    console.error("❌ Test failed:", error);
    return false;
  }
}

// Test individual tool functionality
export async function testIndividualTools() {
  console.log("🔧 Testing Individual Tools...");
  
  const agent = new ChatAgent();
  
  const testCases = [
    {
      name: "Get Today's Date",
      input: "What's today's date?",
      expectedTool: "getTodaysDate"
    },
    {
      name: "Fetch Categories",
      input: "What categories do I have?",
      expectedTool: "fetchCategories"
    },
    {
      name: "Fetch Projects",
      input: "Show me my projects",
      expectedTool: "fetchProjects"
    },
    {
      name: "Create Category",
      input: "Create a category called 'Test Category' with a goal of $500",
      expectedTool: "createCategory"
    }
  ];
  
  for (const testCase of testCases) {
    try {
      console.log(`\n🧪 Testing: ${testCase.name}`);
      const result = await agent.invoke({
        messages: [],
        input: testCase.input,
        personaId: "test-user-123",
        media: []
      });
      
      const lastMessage = result.messages[result.messages.length - 1];
      console.log(`✅ ${testCase.name} Result:`, lastMessage.content);
      
      // Check if tool was called (look for tool_calls in previous messages)
      const toolCalls = result.messages.find(msg => msg.tool_calls && msg.tool_calls.length > 0);
      if (toolCalls) {
        console.log(`🔧 Tool called:`, toolCalls.tool_calls[0].name);
      }
      
    } catch (error) {
      console.error(`❌ ${testCase.name} failed:`, error);
    }
  }
}

// Conversation flow test
export async function testConversationFlow() {
  console.log("💬 Testing Conversation Flow...");
  
  const agent = new ChatAgent();
  let messages: any[] = [];
  
  const conversationSteps = [
    "Hello, I need help with my finances",
    "Show me all my accounts",
    "Create a new account called 'Emergency Fund'",
    "What categories do I have?",
    "Create a category called 'Groceries' with a monthly goal of $400"
  ];
  
  for (let i = 0; i < conversationSteps.length; i++) {
    try {
      console.log(`\n💬 Step ${i + 1}: "${conversationSteps[i]}"`);
      
      const result = await agent.invoke({
        messages: messages,
        input: conversationSteps[i],
        personaId: "test-user-123",
        media: []
      });
      
      // Update conversation history
      messages = result.messages;
      
      const lastMessage = messages[messages.length - 1];
      console.log(`🤖 Response:`, lastMessage.content);
      
    } catch (error) {
      console.error(`❌ Step ${i + 1} failed:`, error);
      break;
    }
  }
  
  console.log(`\n📊 Conversation completed with ${messages.length} messages`);
}

// Main test runner
export async function runAllTests() {
  console.log("🚀 Starting Financial Agent Tool Connection Tests\n");
  
  // Test 1: Basic tool connection
  const basicTest = await testToolConnection();
  
  if (basicTest) {
    // Test 2: Individual tools
    await testIndividualTools();
    
    // Test 3: Conversation flow
    await testConversationFlow();
    
    console.log("\n🎉 All tests completed! The financial tools are properly connected to the agent.");
  } else {
    console.log("\n❌ Basic connection test failed. Please check your configuration.");
  }
}

// Export for easy testing
if (require.main === module) {
  runAllTests().catch(console.error);
}