"use client";

import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Eye,
  Download,
  Trash2,
  FileText,
  Image as ImageIcon,
  File,
  Video,
  Music,
  Archive,
  Calendar,
  HardDrive,
  Tag
} from "lucide-react";
import { format } from "date-fns";
import { useGetMedia } from "@/features/media/api/use-get-media";
import { useDeleteMedia } from "@/features/media/api/use-delete-media";

interface MediaFile {
  id: string;
  fileName: string;
  originalFileName: string;
  mimeType: string;
  fileSize: number;
  url: string;
  category: string;
  entityType: string | null;
  entityId: string | null;
  metadata: Record<string, any> | null;
  createdAt: string;
}

interface MediaPreviewProps {
  category?: string;
  entityType?: string;
  entityId?: string;
  title?: string;
  description?: string;
  viewMode?: "grid" | "list";
  allowDelete?: boolean;
  onFileClick?: (file: MediaFile) => void;
}

export function MediaPreview({
  category,
  entityType,
  entityId,
  title = "Media Files",
  description = "View and manage your uploaded files",
  viewMode = "grid",
  allowDelete = true,
  onFileClick,
}: MediaPreviewProps) {
  const [selectedFile, setSelectedFile] = useState<MediaFile | null>(null);
  const [isViewerOpen, setIsViewerOpen] = useState(false);

  const { data: mediaData, isLoading, error } = useGetMedia({
    category,
    entityType,
    entityId,
  });

  const deleteMutation = useDeleteMedia();

  const files = mediaData?.data || [];

  const getFileIcon = (mimeType: string, size: "sm" | "md" | "lg" = "md") => {
    const sizeClasses = {
      sm: "h-4 w-4",
      md: "h-8 w-8",
      lg: "h-12 w-12"
    };

    if (mimeType.startsWith('image/')) {
      return <ImageIcon className={`${sizeClasses[size]} text-blue-500`} />;
    }
    if (mimeType.startsWith('video/')) {
      return <Video className={`${sizeClasses[size]} text-purple-500`} />;
    }
    if (mimeType.startsWith('audio/')) {
      return <Music className={`${sizeClasses[size]} text-green-500`} />;
    }
    if (mimeType === 'application/pdf') {
      return <FileText className={`${sizeClasses[size]} text-red-500`} />;
    }
    if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('tar')) {
      return <Archive className={`${sizeClasses[size]} text-orange-500`} />;
    }
    return <File className={`${sizeClasses[size]} text-gray-500`} />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      receipts: "bg-green-100 text-green-800",
      documents: "bg-blue-100 text-blue-800",
      images: "bg-purple-100 text-purple-800",
      general: "bg-gray-100 text-gray-800",
    };
    return colors[category] || colors.general;
  };

  const handleFileClick = (file: MediaFile) => {
    if (onFileClick) {
      onFileClick(file);
    } else {
      setSelectedFile(file);
      setIsViewerOpen(true);
    }
  };

  const handleDownload = (file: MediaFile) => {
    // Create a temporary link to download the file
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.originalFileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleDelete = async (file: MediaFile) => {
    if (confirm(`Are you sure you want to delete "${file.originalFileName}"?`)) {
      try {
        await deleteMutation.mutateAsync(file.id);
      } catch (error) {
        // Error handling is done in the mutation
      }
    }
  };

  const renderGridView = () => (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
      {files.map((file) => (
        <div key={file.id} className="group relative">
          <Card className="overflow-hidden hover:shadow-md transition-shadow cursor-pointer">
            <div 
              className="aspect-square bg-gray-50 flex items-center justify-center relative"
              onClick={() => handleFileClick(file)}
            >
              {file.mimeType.startsWith('image/') ? (
                <img
                  src={file.url}
                  alt={file.originalFileName}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="flex flex-col items-center gap-2">
                  {getFileIcon(file.mimeType, "lg")}
                  <span className="text-xs text-gray-500 text-center px-2 truncate">
                    {file.originalFileName}
                  </span>
                </div>
              )}
              
              {/* Overlay with actions */}
              <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                <Button size="sm" variant="secondary" onClick={(e) => {
                  e.stopPropagation();
                  handleFileClick(file);
                }}>
                  <Eye className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="secondary" onClick={(e) => {
                  e.stopPropagation();
                  handleDownload(file);
                }}>
                  <Download className="h-4 w-4" />
                </Button>
                {allowDelete && (
                  <Button size="sm" variant="destructive" onClick={(e) => {
                    e.stopPropagation();
                    handleDelete(file);
                  }}>
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
            
            <CardContent className="p-2">
              <div className="space-y-1">
                <p className="text-xs font-medium truncate">{file.originalFileName}</p>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>{formatFileSize(file.fileSize)}</span>
                  <Badge variant="outline" className={`text-xs ${getCategoryColor(file.category)}`}>
                    {file.category}
                  </Badge>
                </div>
                <p className="text-xs text-gray-400">
                  {format(new Date(file.createdAt), 'MMM dd, yyyy')}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      ))}
    </div>
  );

  const renderListView = () => (
    <div className="space-y-2">
      {files.map((file) => (
        <Card key={file.id} className="hover:shadow-sm transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0">
                {file.mimeType.startsWith('image/') ? (
                  <img
                    src={file.url}
                    alt={file.originalFileName}
                    className="w-12 h-12 object-cover rounded"
                  />
                ) : (
                  getFileIcon(file.mimeType, "lg")
                )}
              </div>
              
              <div className="flex-grow min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="text-sm font-medium truncate">{file.originalFileName}</h4>
                  <Badge variant="outline" className={`text-xs ${getCategoryColor(file.category)}`}>
                    {file.category}
                  </Badge>
                </div>
                
                <div className="flex items-center gap-4 text-xs text-gray-500">
                  <div className="flex items-center gap-1">
                    <HardDrive className="h-3 w-3" />
                    {formatFileSize(file.fileSize)}
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {format(new Date(file.createdAt), 'MMM dd, yyyy HH:mm')}
                  </div>
                  {file.entityType && (
                    <div className="flex items-center gap-1">
                      <Tag className="h-3 w-3" />
                      {file.entityType}
                    </div>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Button size="sm" variant="outline" onClick={() => handleFileClick(file)}>
                  <Eye className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="outline" onClick={() => handleDownload(file)}>
                  <Download className="h-4 w-4" />
                </Button>
                {allowDelete && (
                  <Button 
                    size="sm" 
                    variant="destructive" 
                    onClick={() => handleDelete(file)}
                    disabled={deleteMutation.isPending}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            <span className="ml-2">Loading files...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-500">
            Error loading files: {error.message}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          {files.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <File className="h-12 w-12 mx-auto mb-2" />
              <p>No files found</p>
            </div>
          ) : (
            <>
              <div className="mb-4 flex justify-between items-center">
                <span className="text-sm text-gray-600">
                  {files.length} file{files.length !== 1 ? 's' : ''}
                </span>
              </div>
              {viewMode === "grid" ? renderGridView() : renderListView()}
            </>
          )}
        </CardContent>
      </Card>

      {/* File Viewer Modal */}
      <Dialog open={isViewerOpen} onOpenChange={setIsViewerOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>{selectedFile?.originalFileName}</DialogTitle>
            <DialogDescription>
              {selectedFile && (
                <div className="flex items-center gap-4 text-sm">
                  <span>{formatFileSize(selectedFile.fileSize)}</span>
                  <span>{format(new Date(selectedFile.createdAt), 'MMM dd, yyyy HH:mm')}</span>
                  <Badge className={getCategoryColor(selectedFile.category)}>
                    {selectedFile.category}
                  </Badge>
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
          
          {selectedFile && (
            <div className="flex-1 overflow-hidden">
              {selectedFile.mimeType.startsWith('image/') ? (
                <img
                  src={selectedFile.url}
                  alt={selectedFile.originalFileName}
                  className="w-full h-auto max-h-[60vh] object-contain rounded"
                />
              ) : selectedFile.mimeType.startsWith('video/') ? (
                <video
                  src={selectedFile.url}
                  controls
                  className="w-full h-auto max-h-[60vh] rounded"
                />
              ) : selectedFile.mimeType.startsWith('audio/') ? (
                <audio
                  src={selectedFile.url}
                  controls
                  className="w-full"
                />
              ) : selectedFile.mimeType === 'application/pdf' ? (
                <iframe
                  src={selectedFile.url}
                  className="w-full h-[60vh] rounded"
                  title={selectedFile.originalFileName}
                />
              ) : (
                <div className="flex flex-col items-center justify-center py-12">
                  {getFileIcon(selectedFile.mimeType, "lg")}
                  <p className="mt-4 text-sm text-gray-500">
                    Preview not available for this file type
                  </p>
                  <Button 
                    className="mt-4" 
                    onClick={() => handleDownload(selectedFile)}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download File
                  </Button>
                </div>
              )}
              
              <div className="mt-4 flex justify-end gap-2">
                <Button variant="outline" onClick={() => handleDownload(selectedFile)}>
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
                {allowDelete && (
                  <Button 
                    variant="destructive" 
                    onClick={() => {
                      handleDelete(selectedFile);
                      setIsViewerOpen(false);
                    }}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}