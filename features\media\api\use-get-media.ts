import { useQuery } from "@tanstack/react-query";
import { useCurrentUserId } from "@/lib/utils";
import { client } from "@/lib/hono";

interface MediaFile {
  id: string;
  fileName: string;
  originalFileName: string;
  mimeType: string;
  fileSize: number;
  url: string;
  category: string;
  entityType: string | null;
  entityId: string | null;
  metadata: Record<string, any> | null;
  createdAt: string;
}

interface UseGetMediaParams {
  category?: string;
  entityType?: string;
  entityId?: string;
  limit?: number;
  offset?: number;
}

export const useGetMedia = (params: UseGetMediaParams = {}) => {
  const userId = useCurrentUserId();

  const query = useQuery<{ data: MediaFile[] }>({
    enabled: !!userId,
    queryKey: ["media", params],
    queryFn: async () => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const queryParams: Record<string, string> = {};
      
      if (params.category) queryParams.category = params.category;
      if (params.entityType) queryParams.entityType = params.entityType;
      if (params.entityId) queryParams.entityId = params.entityId;
      if (params.limit) queryParams.limit = params.limit.toString();
      if (params.offset) queryParams.offset = params.offset.toString();

      const response = await client.api.media.$get(
        { query: queryParams },
        { headers: { "X-User-ID": userId } }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch media files");
      }

      return response.json();
    },
  });

  return query;
};