CREATE TABLE IF NOT EXISTS "wishlist_possibilities" (
	"id" text PRIMARY KEY NOT NULL,
	"wishlist_item_id" text NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"notes" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "wishlist_sources" (
	"id" text PRIMARY KEY NOT NULL,
	"possibility_id" text NOT NULL,
	"name" text NOT NULL,
	"url" text,
	"price" integer,
	"notes" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "wishlist_items" ADD COLUMN "quantity" integer DEFAULT 1 NOT NULL;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "wishlist_possibilities" ADD CONSTRAINT "wishlist_possibilities_wishlist_item_id_wishlist_items_id_fk" FOREIGN KEY ("wishlist_item_id") REFERENCES "wishlist_items"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "wishlist_sources" ADD CONSTRAINT "wishlist_sources_possibility_id_wishlist_possibilities_id_fk" FOREIGN KEY ("possibility_id") REFERENCES "wishlist_possibilities"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
