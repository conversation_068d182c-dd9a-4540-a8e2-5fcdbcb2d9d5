import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useCurrentUserId } from "@/lib/utils";
import { client } from "@/lib/hono";
import { toast } from "sonner";

export const useDeleteMedia = () => {
  const userId = useCurrentUserId();
  const queryClient = useQueryClient();

  return useMutation<
    { data: { id: string }; message: string },
    Error,
    string
  >({
    mutationFn: async (mediaId: string) => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const response = await client.api.media[":id"].$delete(
        { param: { id: mediaId } },
        { headers: { "X-User-ID": userId } }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Delete failed");
      }

      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["media"] });
      toast.success(data.message || "File deleted successfully");
    },
    onError: (error) => {
      toast.error(`Delete failed: ${error.message}`);
    },
  });
};