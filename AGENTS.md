# AGENTS.md

## Build/Lint/Test Commands

- Build: `npm run build`
- Lint: `npm run lint` (Next.js ESLint config)
- Test: No test setup detected. No explicit command for running a single test.
- Format: `npm run pretty` (Prettier)
- Database: `npm run db:generate`, `npm run db:migrate`, `npm run db:studio`

## Code Style Guidelines

- **Imports**: ES modules, omit file extensions (Next.js handles resolution), use `@/*` path aliases
- **Formatting**: Prettier (2 spaces, semicolons)
- **Types**: Strict TypeScript, always define types, use Zod for validation
- **Naming**: camelCase for functions/variables, PascalCase for components
- **Error Handling**: Async/await with try/catch, React error boundaries
- **Components**: Follow shadcn/ui patterns, use Tailwind CSS with custom theme
- **Database**: Drizzle ORM with PostgreSQL, use schema types
- **State**: TanStack Query for server state, <PERSON>ustand for client state

No Cursor/Copilot rules detected.
