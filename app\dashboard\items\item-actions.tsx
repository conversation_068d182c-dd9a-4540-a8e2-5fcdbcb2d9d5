"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Edit, Eye, MoreHorizontal, Trash } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { useDeleteItem } from "@/features/items/api/use-delete-item";
import { useConfirm } from "@/hooks/use-comform";
import { useOpenItem } from "@/features/items/hooks/use-open-item";

type Props = {
  id: string;
  name: string;
  payee?: string | null;
  usageCount: number;
};

export const ItemActions = ({ id, name, payee, usageCount }: Props) => {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [ConfirmDialog, confirm] = useConfirm(
    "Are you sure?",
    "You are about to delete this item. This action cannot be undone."
  );
  
  const deleteMutation = useDeleteItem(id);
  const { onOpen } = useOpenItem();

  const handleView = () => {
    router.push(`/dashboard/items/${id}`);
    setIsOpen(false);
  };

  const handleEdit = () => {
    onOpen(id);
    setIsOpen(false);
  };

  const handleDelete = async () => {
    if (usageCount > 0) {
      toast.error(`Cannot delete item "${name}". It is used in ${usageCount} transaction(s).`);
      return;
    }
    
    const ok = await confirm();
    if (ok) {
      deleteMutation.mutate();
    }
    setIsOpen(false);
  };

  const displayName = payee ? `${name} (${payee})` : name;

  return (
    <>
      <ConfirmDialog />
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={handleView}>
          <Eye className="mr-2 h-4 w-4" />
          View Transactions
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleEdit}>
          <Edit className="mr-2 h-4 w-4" />
          Edit Item
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem 
          onClick={handleDelete}
          className="text-destructive focus:text-destructive"
          disabled={usageCount > 0 || deleteMutation.isPending}
        >
          <Trash className="mr-2 h-4 w-4" />
          Delete Item
          {usageCount > 0 && (
            <span className="ml-2 text-xs text-muted-foreground">
              ({usageCount} used)
            </span>
          )}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
    </>
  );
};