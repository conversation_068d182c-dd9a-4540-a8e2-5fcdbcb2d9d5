import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageSquareIcon, CodeIcon, BrainIcon, BookOpenIcon } from 'lucide-react';

export default function ChatDemoPage() {
  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4">AI Chat Interface Demos</h1>
        <p className="text-lg text-muted-foreground">
          New chat interfaces built with AI Elements components - showcasing modern conversational UI patterns
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquareIcon className="h-5 w-5" />
              Basic Chat Interface
            </CardTitle>
            <CardDescription>
              Clean, simple chat UI using core AI Elements components
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 mb-4">
              <div className="flex items-center gap-2 text-sm">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                Conversation & Message components
              </div>
              <div className="flex items-center gap-2 text-sm">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                PromptInput with toolbar
              </div>
              <div className="flex items-center gap-2 text-sm">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                Response rendering
              </div>
              <div className="flex items-center gap-2 text-sm">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                Loading states & Actions
              </div>
            </div>
            <Button asChild className="w-full">
              <Link href="/new-chat">Try Basic Chat</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BrainIcon className="h-5 w-5" />
              Advanced Chat Interface
            </CardTitle>
            <CardDescription>
              Full-featured chat with reasoning, sources, and code blocks
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 mb-4">
              <div className="flex items-center gap-2 text-sm">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                Reasoning component (collapsible)
              </div>
              <div className="flex items-center gap-2 text-sm">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                Sources & citations
              </div>
              <div className="flex items-center gap-2 text-sm">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                Code blocks with syntax highlighting
              </div>
              <div className="flex items-center gap-2 text-sm">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                Model selection & enhanced toolbar
              </div>
            </div>
            <Button asChild className="w-full">
              <Link href="/advanced-chat">Try Advanced Chat</Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>🔧 Implementation Details</CardTitle>
          <CardDescription>
            Built step-by-step using AI Elements components while preserving existing functionality
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-3 gap-4">
            <div>
              <h3 className="font-semibold mb-2">Step 1: Core UI</h3>
              <p className="text-sm text-muted-foreground">
                Conversation, Message, and PromptInput components for basic chat structure
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Step 2: Enhanced Features</h3>
              <p className="text-sm text-muted-foreground">
                Added Loader, Actions, and Response components for better UX
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Step 3: Advanced Components</h3>
              <p className="text-sm text-muted-foreground">
                Integrated Reasoning, Sources, and CodeBlock for rich responses
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="text-center">
        <p className="text-sm text-muted-foreground">
          Both interfaces use existing helper functions and maintain compatibility with your current system.
          <br />
          The old chat component remains untouched as requested.
        </p>
      </div>
    </div>
  );
}
