import { InferRequestType, InferResponseType } from "hono";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";

type ResponseType = InferResponseType<typeof client.api.wishlist.$post>;
type RequestType = InferRequestType<typeof client.api.wishlist.$post>["json"];

export const useCreateWishlistItem = () => {
  const userId = useCurrentUserId();
  const queryClient = useQueryClient();
  
  const mutation = useMutation<ResponseType, Error, RequestType>({
    mutationFn: async (json) => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const response = await client.api.wishlist.$post(
        { json },
        {
          headers: {
            "X-User-ID": userId,
          },
        },
      );
      const data = await response.json();

      return data;
    },
    onSuccess: () => {
      toast.success("Wishlist item created");
      queryClient.invalidateQueries({ queryKey: ["wishlist-items", userId] });
    },
    onError: () => {
      toast.error("Failed to create wishlist item");
    },
  });

  return mutation;
};