"use client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";
import { toast } from "sonner";

type RequestType = {
  name: string;
  description?: string;
  payee?: string;
  defaultCategoryId?: string;
  barcode?: string;
};

export const useCreateItem = () => {
  const queryClient = useQueryClient();
  const userId = useCurrentUserId();

  const mutation = useMutation({
    mutationFn: async (json: RequestType) => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const response = await client.api.items.$post(
        { json },
        {
          headers: {
            "X-User-ID": userId,
          },
        },
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || "Failed to create item");
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success("Item created");
      queryClient.invalidateQueries({ queryKey: ["items"] });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  return mutation;
};