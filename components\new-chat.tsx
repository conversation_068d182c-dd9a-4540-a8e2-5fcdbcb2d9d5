'use client';

import { useState } from 'react';
import {
  Conversation,
  ConversationContent,
  ConversationScrollButton,
} from '@/components/conversation';
import { Message, MessageContent } from '@/components/message';
import {
  PromptInput,
  PromptInputTextarea,
  PromptInputSubmit,
  PromptInputToolbar,
  PromptInputTools,
  PromptInputButton,
  PromptInputModelSelect,
  PromptInputModelSelectContent,
  PromptInputModelSelectItem,
  PromptInputModelSelectTrigger,
  PromptInputModelSelectValue,
} from '@/components/prompt-input';
import { Response } from '@/components/response';
import { Loader } from '@/components/loader';
import { Actions, Action } from '@/components/actions';
import { Reasoning, ReasoningContent, ReasoningTrigger } from '@/components/reasoning';
import { Sources, SourcesContent, SourcesTrigger, Source } from '@/components/sources';
import { RefreshCcwIcon, CopyIcon, ThumbsUpIcon, ThumbsDownIcon, MicIcon, PaperclipIcon } from 'lucide-react';

// Define types for our messages
interface SimpleMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  reasoning?: string;
  sources?: Array<{ url: string; title: string }>;
}

// Available models
const models = [
  { id: 'gpt-4', name: 'GPT-4' },
  { id: 'claude-3-sonnet', name: 'Claude 3 Sonnet' },
  { id: 'gemini-pro', name: 'Gemini Pro' },
];

const NewChat = () => {
  const [input, setInput] = useState('');
  const [messages, setMessages] = useState<SimpleMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedModel, setSelectedModel] = useState(models[0].id);

  const handleRetry = () => {
    if (messages.length > 0) {
      const lastUserMessage = [...messages].reverse().find(m => m.role === 'user');
      if (lastUserMessage && !isLoading) {
        setIsLoading(true);
        // Remove the last AI message and regenerate
        setMessages(prev => prev.filter(m => m.id !== messages[messages.length - 1].id));
        
        setTimeout(() => {
          const aiMessage: SimpleMessage = {
            id: Date.now().toString(),
            role: 'assistant',
            content: `Regenerated response using ${models.find(m => m.id === selectedModel)?.name} for: "${lastUserMessage.content}". This is an updated response using the new AI elements UI components.`,
            reasoning: `Regenerating response based on user request. Using improved reasoning for better response quality.`,
          };
          setMessages(prev => [...prev, aiMessage]);
          setIsLoading(false);
        }, 1000);
      }
    }
  };

  const handleCopy = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim() && !isLoading) {
      const userMessage: SimpleMessage = {
        id: Date.now().toString(),
        role: 'user',
        content: input,
      };
      
      setMessages(prev => [...prev, userMessage]);
      setInput('');
      setIsLoading(true);

      // Simulate AI response with enhanced features
      setTimeout(() => {
        const aiMessage: SimpleMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: `Using model ${models.find(m => m.id === selectedModel)?.name}: You said "${userMessage.content}". This is a test response using the new AI elements UI components with enhanced features.`,
          reasoning: Math.random() > 0.5 ? `Let me think about this question step by step. The user asked about "${userMessage.content}", which requires me to consider multiple factors and provide a comprehensive response.` : undefined,
          sources: Math.random() > 0.7 ? [
            { url: 'https://example.com/source1', title: 'Example Source 1' },
            { url: 'https://example.com/source2', title: 'Example Source 2' },
          ] : undefined,
        };
        setMessages(prev => [...prev, aiMessage]);
        setIsLoading(false);
      }, 1000);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 relative size-full rounded-lg border h-[600px]">
      <div className="flex flex-col h-full">
        <Conversation>
          <ConversationContent>
            {messages.map((message, messageIndex) => (
              <div key={message.id}>
                {/* Show sources before the message for assistant responses */}
                {message.role === 'assistant' && message.sources && (
                  <Sources>
                    <SourcesTrigger count={message.sources.length} />
                    <SourcesContent>
                      {message.sources.map((source, i) => (
                        <Source
                          key={i}
                          href={source.url}
                          title={source.title}
                        />
                      ))}
                    </SourcesContent>
                  </Sources>
                )}
                
                <Message from={message.role}>
                  <MessageContent>
                    {/* Show reasoning if available */}
                    {message.reasoning && (
                      <Reasoning isStreaming={isLoading && messageIndex === messages.length - 1}>
                        <ReasoningTrigger />
                        <ReasoningContent>
                          {message.reasoning}
                        </ReasoningContent>
                      </Reasoning>
                    )}
                    
                    <Response>
                      {message.content}
                    </Response>
                    
                    {message.role === 'assistant' && messageIndex === messages.length - 1 && (
                      <Actions className="mt-2">
                        <Action
                          onClick={handleRetry}
                          label="Retry"
                          tooltip="Regenerate response"
                        >
                          <RefreshCcwIcon className="size-3" />
                        </Action>
                        <Action
                          onClick={() => handleCopy(message.content)}
                          label="Copy"
                          tooltip="Copy to clipboard"
                        >
                          <CopyIcon className="size-3" />
                        </Action>
                        <Action
                          onClick={() => console.log('Liked')}
                          label="Like"
                          tooltip="Like this response"
                        >
                          <ThumbsUpIcon className="size-3" />
                        </Action>
                        <Action
                          onClick={() => console.log('Disliked')}
                          label="Dislike"
                          tooltip="Dislike this response"
                        >
                          <ThumbsDownIcon className="size-3" />
                        </Action>
                      </Actions>
                    )}
                  </MessageContent>
                </Message>
              </div>
            ))}
            {isLoading && <Loader />}
          </ConversationContent>
          <ConversationScrollButton />
        </Conversation>

        <PromptInput
          onSubmit={handleSubmit}
          className="mt-4 w-full max-w-2xl mx-auto"
        >
          <PromptInputTextarea
            value={input}
            placeholder="Ask about your finances, upload receipts, or get insights..."
            onChange={(e) => setInput(e.currentTarget.value)}
          />
          <PromptInputToolbar>
            <PromptInputTools>
              <PromptInputButton>
                <MicIcon size={16} />
              </PromptInputButton>
              <PromptInputButton>
                <PaperclipIcon size={16} />
                <span>Attach</span>
              </PromptInputButton>
              <PromptInputModelSelect
                onValueChange={(value) => setSelectedModel(value)}
                value={selectedModel}
              >
                <PromptInputModelSelectTrigger>
                  <PromptInputModelSelectValue />
                </PromptInputModelSelectTrigger>
                <PromptInputModelSelectContent>
                  {models.map((model) => (
                    <PromptInputModelSelectItem key={model.id} value={model.id}>
                      {model.name}
                    </PromptInputModelSelectItem>
                  ))}
                </PromptInputModelSelectContent>
              </PromptInputModelSelect>
            </PromptInputTools>
            <PromptInputSubmit
              status={isLoading ? 'streaming' : 'ready'}
              disabled={!input.trim() || isLoading}
            />
          </PromptInputToolbar>
        </PromptInput>
      </div>
    </div>
  );
};

export default NewChat;
