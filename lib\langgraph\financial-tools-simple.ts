import { DynamicStructuredTool } from "@langchain/core/tools";
import { z } from "zod";
import { functions } from "@/lib/AI/functions-ai-chat";

// Create a factory function that creates tools with personaId context
export function createFinancialTools(personaId: string) {
  
  // Fetch tools
  const fetchCategoriesTools = new DynamicStructuredTool({
    name: "fetchCategories",
    description: "Retrieves a list of all created financial categories. Returns an array of objects with id, name, goal, and amount properties.",
    schema: z.object({}),
    func: async (_input: {}) => {
      return await functions.fetchCategories({ personaId });
    },
  });

  const fetchAccountsTools = new DynamicStructuredTool({
    name: "fetchAccounts", 
    description: "Retrieves a list of all financial accounts, such as bank accounts or payment services like PayPal. Returns an array with id and name properties.",
    schema: z.object({}),
    func: async (_input: {}) => {
      return await functions.fetchAccounts({ personaId });
    },
  });

  const fetchProjectsTools = new DynamicStructuredTool({
    name: "fetchProjects",
    description: "Retrieves a list of all created projects. Optionally filters projects based on a specific accountId.",
    schema: z.object({
      accountId: z.string().optional().describe("Optional ID of the account to filter projects by"),
    }),
    func: async (input: { accountId?: string }) => {
      const { accountId } = input;
      return await functions.fetchProjects({ accountId, personaId });
    },
  });

  const fetchTransactionsTools = new DynamicStructuredTool({
    name: "fetchTransactions",
    description: "Retrieves multiple transactions within a specified date interval, optionally filtered by account ID. Includes all related detailsTransactions.",
    schema: z.object({
      from: z.string().describe("The start date of the interval in ISO format (e.g., '2024-01-01')"),
      to: z.string().describe("The end date of the interval in ISO format (e.g., '2024-12-31')"),
      accountId: z.string().optional().describe("Optional ID of the account to filter transactions by"),
    }),
    func: async (input: { from: string; to: string; accountId?: string }) => {
      const { from, to, accountId } = input;
      return await functions.fetchTransactions({ from, to, accountId, personaId });
    },
  });

  const fetchManyItemsTools = new DynamicStructuredTool({
    name: "fetchManyItems",
    description: "Retrieves all detailsTransactions within a specified date interval, independent of their related transactions.",
    schema: z.object({
      from: z.string().describe("The start date of the interval in ISO format (e.g., '2024-01-01')"),
      to: z.string().describe("The end date of the interval in ISO format (e.g., '2024-12-31')"),
    }),
    func: async (input: { from: string; to: string }) => {
      const { from, to } = input;
      return await functions.fetchManyItems({ from, to, personaId });
    },
  });

  const fetchOneTransactionTools = new DynamicStructuredTool({
    name: "fetchOneTransaction",
    description: "Retrieves a specific transaction by its unique ID, including all related detailsTransactions.",
    schema: z.object({
      id: z.string().describe("The unique identifier of the transaction to fetch"),
    }),
    func: async (input: { id: string }) => {
      const { id } = input;
      return await functions.fetchOneTransaction({ id, personaId });
    },
  });

  const fetchOneItemTools = new DynamicStructuredTool({
    name: "fetchOneItem", 
    description: "Retrieves a specific detailsTransaction by its unique ID.",
    schema: z.object({
      id: z.string().describe("The unique identifier of the detailsTransaction to fetch"),
    }),
    func: async (input: { id: string }) => {
      const { id } = input;
      return await functions.fetchOneItem({ id, personaId });
    },
  });

  // Create tools
  const createOneTransactionTools = new DynamicStructuredTool({
    name: "createOneTransaction",
    description: "Creates a new transaction with the provided details. Amount values are multiplied by 1,000 for fixed-point arithmetic.",
    schema: z.object({
      date: z.string().describe("The date of the transaction in ISO format (e.g., '2024-11-14')"),
      accountId: z.string().describe("The ID of the account associated with the transaction"),
      amount: z.number().describe("The amount of the transaction. Income are positive values, expenses are negative values. Will be multiplied by 1,000 for storage."),
      payee: z.string().describe("The payee for the transaction"),
      notes: z.string().optional().nullable().describe("Optional notes or description for the transaction"),
      projectId: z.string().optional().nullable().describe("Optional project ID to associate the transaction with"),
      categoryId: z.string().optional().nullable().describe("Optional category ID to classify the transaction"),
    }),
    func: async (input: { date: string; accountId: string; amount: number; payee: string; notes?: string | null; projectId?: string | null; categoryId?: string | null }) => {
      const { date, accountId, amount, payee, notes, projectId, categoryId } = input;
      return await functions.createOneTransaction({
        date: new Date(date),
        accountId,
        amount: amount * 1000, // Apply fixed-point arithmetic
        payee,
        notes,
        projectId,
        categoryId,
        personaId,
      });
    },
  });

  const createOneItemTools = new DynamicStructuredTool({
    name: "createOneItem",
    description: "Creates a new detailsTransaction with the provided details. All price-related values are multiplied by 1,000 for fixed-point arithmetic.",
    schema: z.object({
      transactionId: z.string().describe("The ID of the parent transaction"),
      name: z.string().describe("The name or description of the detail transaction"),
      quantity: z.number().describe("The quantity of the item purchased"),
      unitPrice: z.number().describe("The price per unit of the item. Usually positive. Will be multiplied by 1,000 for storage."),
      amount: z.number().describe("The total amount for this detail transaction. Income are positive, expenses are negative."),
      projectId: z.string().optional().nullable().describe("Optional project ID to associate the detail transaction with"),
      categoryId: z.string().optional().nullable().describe("Optional category ID to classify the detail transaction"),
    }),
    func: async (input: { transactionId: string; name: string; quantity: number; unitPrice: number; amount: number; projectId?: string | null; categoryId?: string | null }) => {
      const { transactionId, name, quantity, unitPrice, amount, projectId, categoryId } = input;
      return await functions.createOneItem({
        transactionId,
        name,
        quantity,
        unitPrice: unitPrice * 1000, // Apply fixed-point arithmetic
        amount: amount * 1000, // Apply fixed-point arithmetic
        projectId,
        categoryId,
        personaId,
      });
    },
  });

  const createAccountTools = new DynamicStructuredTool({
    name: "createAccount",
    description: "Creates a new financial account with the provided name.",
    schema: z.object({
      name: z.string().describe("The name of the financial account (e.g., 'Commerzbank', 'PayPal')"),
    }),
    func: async (input: { name: string }) => {
      const { name } = input;
      return await functions.createAccount({ name, personaId });
    },
  });

  const createCategoryTools = new DynamicStructuredTool({
    name: "createCategory",
    description: "Creates a new financial category with the provided name and optional monthly goal. Goal is multiplied by 1,000 for storage.",
    schema: z.object({
      name: z.string().describe("The name of the financial category (e.g., 'Groceries', 'Entertainment')"),
      goal: z.number().optional().nullable().describe("Optional monthly goal for the category. Will be multiplied by 1,000 for storage."),
    }),
    func: async (input: { name: string; goal?: number | null }) => {
      const { name, goal } = input;
      return await functions.createCategory({
        name,
        goal: goal ? goal * 1000 : goal, // Apply fixed-point arithmetic if goal exists
        personaId,
      });
    },
  });

  const createProjectTools = new DynamicStructuredTool({
    name: "createProject",
    description: "Creates a new project with the provided details. Budget is multiplied by 1,000 for fixed-point arithmetic.",
    schema: z.object({
      name: z.string().describe("The name of the project (e.g., 'Marketing Campaign', 'Website Redesign')"),
      budget: z.number().describe("The financial budget allocated for the project. Will be multiplied by 1,000 for storage."),
      startDate: z.string().describe("The start date of the project in ISO format (e.g., '2024-01-01')"),
      endDate: z.string().describe("The end date of the project in ISO format (e.g., '2024-06-30')"),
      description: z.string().optional().nullable().describe("Optional description or details about the project"),
    }),
    func: async (input: { name: string; budget: number; startDate: string; endDate: string; description?: string | null }) => {
      const { name, budget, startDate, endDate, description } = input;
      return await functions.createProject({
        name,
        budget: budget * 1000, // Apply fixed-point arithmetic
        startDate,
        endDate,
        description,
        personaId,
      });
    },
  });

  // Update tools
  const updateTransactionTools = new DynamicStructuredTool({
    name: "updateTransaction",
    description: "Updates a transaction with the specified details. Amount is multiplied by 1,000 for fixed-point arithmetic.",
    schema: z.object({
      id: z.string().describe("ID of the transaction to be modified"),
      accountId: z.string().describe("Identifier for the account associated with the transaction"),
      amount: z.number().describe("Amount of the transaction. Income positive, expenses negative. Will be multiplied by 1,000."),
      date: z.string().describe("Date of the transaction in ISO 8601 format"),
      notes: z.string().nullable().describe("Additional notes about the transaction"),
      payee: z.string().describe("Name of the payee for the transaction"),
      projectId: z.string().nullable().describe("Identifier for the project associated with the transaction"),
      categoryId: z.string().nullable().describe("Identifier for the category associated with the transaction"),
    }),
    func: async (input: { id: string; accountId: string; amount: number; date: string; notes?: string | null; payee: string; projectId?: string | null; categoryId?: string | null }) => {
      const { id, accountId, amount, date, notes, payee, projectId, categoryId } = input;
      return await functions.updateTransaction({
        id,
        accountId,
        amount: amount * 1000, // Apply fixed-point arithmetic
        date: new Date(date),
        notes,
        payee,
        projectId,
        categoryId,
        personaId,
      });
    },
  });

  const updateProjectTools = new DynamicStructuredTool({
    name: "updateProject",
    description: "Updates a project with the provided information. Budget is multiplied by 1,000 for fixed-point arithmetic.",
    schema: z.object({
      id: z.string().describe("Unique identifier for the project"),
      name: z.string().describe("The name of the project"),
      budget: z.number().describe("The budget allocated for the project. Will be multiplied by 1,000."),
      startDate: z.string().describe("The starting date of the project in ISO 8601 format"),
      endDate: z.string().describe("The ending date of the project in ISO 8601 format"),
      description: z.string().nullable().describe("A description of the project"),
    }),
    func: async (input: { id: string; name: string; budget: number; startDate: string; endDate: string; description?: string | null }) => {
      const { id, name, budget, startDate, endDate, description } = input;
      return await functions.updateProject({
        id,
        name,
        budget: budget * 1000, // Apply fixed-point arithmetic
        startDate,
        endDate,
        description,
        personaId,
      });
    },
  });

  const updateItemTools = new DynamicStructuredTool({
    name: "updateItem",
    description: "Updates one item (transaction detail) with the provided data. Price values are multiplied by 1,000 for fixed-point arithmetic.",
    schema: z.object({
      id: z.string().describe("Unique identifier for the transaction detail"),
      amount: z.number().describe("Amount for the transaction. Income positive, expenses negative. Will be multiplied by 1,000."),
      transactionId: z.string().describe("Identifier for the transaction"),
      name: z.string().nullable().describe("Name associated with the transaction"),
      quantity: z.number().nullable().describe("Quantity of items in the transaction"),
      unitPrice: z.number().nullable().describe("Unit price of the items. Usually positive. Will be multiplied by 1,000."),
      categoryId: z.string().nullable().describe("Identifier for the category"),
      projectId: z.string().nullable().describe("Identifier for the associated project"),
    }),
    func: async (input: { id: string; amount: number; transactionId: string; name?: string | null; quantity?: number | null; unitPrice?: number | null; categoryId?: string | null; projectId?: string | null }) => {
      const { id, amount, transactionId, name, quantity, unitPrice, categoryId, projectId } = input;
      return await functions.updateItem({
        id,
        amount: amount * 1000, // Apply fixed-point arithmetic
        transactionId,
        name,
        quantity,
        unitPrice: unitPrice ? unitPrice * 1000 : unitPrice, // Apply fixed-point arithmetic if exists
        categoryId,
        projectId,
        personaId,
      });
    },
  });

  const updateCategoryTools = new DynamicStructuredTool({
    name: "updateCategory",
    description: "Updates category with specified parameters. Goal is multiplied by 1,000 for fixed-point arithmetic.",
    schema: z.object({
      id: z.string().describe("Unique identifier for the category"),
      name: z.string().describe("Name of the category"),
      goal: z.number().nullable().describe("Monthly spending limit or income earning goal. Will be multiplied by 1,000."),
    }),
    func: async (input: { id: string; name: string; goal?: number | null }) => {
      const { id, name, goal } = input;
      return await functions.updateCategory({
        id,
        name,
        goal: goal ? goal * 1000 : goal, // Apply fixed-point arithmetic if goal exists
        personaId,
      });
    },
  });

  const updateAccountTools = new DynamicStructuredTool({
    name: "updateAccount",
    description: "Updates account with the provided information.",
    schema: z.object({
      id: z.string().describe("Unique identifier of the account"),
      name: z.string().describe("Name of the account"),
    }),
    func: async (input: { id: string; name: string }) => {
      const { id, name } = input;
      return await functions.updateAccount({
        id,
        name,
        personaId,
      });
    },
  });

  const getTodaysDateTools = new DynamicStructuredTool({
    name: "getTodaysDate",
    description: "Gets the current date and returns it as a string representation of today's date.",
    schema: z.object({}),
    func: async (_input: {}) => {
      return await functions.getTodaysDate({ personaId });
    },
  });

  // Return all tools as an array
  return [
    // Fetch tools
    fetchCategoriesTools,
    fetchAccountsTools,
    fetchProjectsTools,
    fetchTransactionsTools,
    fetchManyItemsTools,
    fetchOneTransactionTools,
    fetchOneItemTools,
    
    // Create tools
    createOneTransactionTools,
    createOneItemTools,
    createAccountTools,
    createCategoryTools,
    createProjectTools,
    
    // Update tools
    updateTransactionTools,
    updateProjectTools,
    updateItemTools,
    updateCategoryTools,
    updateAccountTools,
    
    // Utility tools
    getTodaysDateTools,
  ];
}